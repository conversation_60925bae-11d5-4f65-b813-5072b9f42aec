#!/bin/bash
set -o errexit

DIR=`pwd`
USER_UID=`id -u ${USER}`
USER_GID=`id -g ${USER}`
SCRIPT_DIR=$( cd -- "$( dirname -- "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )

#-------------------------------------------

shopt -s expand_aliases
if [ -f ~/.bash_aliases ]; then
    . ~/.bash_aliases
fi

#-------------------------------------------
# if nerdctl is in use the user uid needs to be root - the tool itself handles file access mapping
# if nerdctl is used -u 0:${USER_GID}
# if docker is used -u ${USER_UID}:${USER_GID}
docker -v | grep Docker >/dev/null 2>&1 || USER_UID=0;


GCLOUD_PROJECT_ID=endless-gizmo-264508
GOOGLE_APPLICATION_CREDENTIALS=gcloud-service-key.json
GOOGLE_APPLICATION_TOKEN_FILE=./.build/.gcloud-auth-access-token

#NODEJS_VERSION=12.22.12
#ALPINE_VERSION=3.15

NODEJS_VERSION=18.17.1
ALPINE_VERSION=3.18

#NODEJS_VERSION=20.18.0
#ALPINE_VERSION=3.20

START_COMMAND="node server/app.js"

USE_TTY=""
# https://stackoverflow.com/questions/911168/how-can-i-detect-if-my-shell-script-is-running-through-a-pipe
# change -t 1 to -t 0 if you're worried about STDIN, but not STDOOUT.
test -t 0 && USE_TTY="-t"
#echo ${USE_TTY}


if test `find "${GOOGLE_APPLICATION_TOKEN_FILE}" -mmin +120`
then
    echo "token file is too old - will remove it (${GOOGLE_APPLICATION_TOKEN_FILE})"
    rm -rf ${GOOGLE_APPLICATION_TOKEN_FILE}
fi


if [ -f "${GOOGLE_APPLICATION_TOKEN_FILE}" ] && [ -s "${GOOGLE_APPLICATION_TOKEN_FILE}" ]; then
   echo "token file already exists (${GOOGLE_APPLICATION_TOKEN_FILE})"
else
   mkdir -p "${DIR}/.build"
   docker run -i ${USE_TTY} --rm --restart=no \
      -u ${USER_UID}:${USER_GID} \
      -v "${DIR}":"${DIR}" \
      -w "${DIR}" \
      -e GOOGLE_APPLICATION_CREDENTIALS="${GOOGLE_APPLICATION_CREDENTIALS}" \
      gcr.io/google.com/cloudsdktool/google-cloud-cli:496.0.0-slim -- \
          gcloud auth activate-service-account --key-file ${GOOGLE_APPLICATION_CREDENTIALS} \
          && gcloud config set project ${GCLOUD_PROJECT_ID} \
          && echo === \
          && gcloud auth print-access-token --project=${GCLOUD_PROJECT_ID} > ${GOOGLE_APPLICATION_TOKEN_FILE}
fi

#docker build --rm \
#   -t allo-node:20.18.0-alpine3.20 \
#   -f .src/main/docker/Dockerfile.local.node.corepack .

#
#https://github.com/yarnpkg/yarn/issues/8904
#https://github.com/yarnpkg/yarn/issues/6363
#
#.yarn, yarn.lock
mkdir -p "${DIR}/.build"


docker run -i ${USE_TTY} --rm --restart=no \
   -u ${USER_UID}:${USER_GID} \
   -v "${DIR}":"${DIR}" \
   -w "${DIR}" \
   -e GOOGLE_APPLICATION_CREDENTIALS="${GOOGLE_APPLICATION_CREDENTIALS}" \
   --entrypoint /bin/sh \
   node:$NODEJS_VERSION-alpine$ALPINE_VERSION -- -ce ./build-script.sh

docker build --rm \
   -t test \
   --build-arg "NODEJS_VERSION=${NODEJS_VERSION}" \
   --build-arg "ALPINE_VERSION=${ALPINE_VERSION}" \
   --build-arg "START_COMMAND=${START_COMMAND}" \
   -f .src/main/docker/Dockerfile .

docker run -it --rm \
   -p 3000:3000 \
   -e PORT=3000 \
   -e NEXT_TELEMETRY_DISABLED="1" \
   -e NEXT_PUBLIC_LOG_LEVEL="info" \
   -e NODE_ENV="staging" \
   -e NEXT_PUBLIC_LEVIEE_ENV="local" \
   -e NODE_OPTIONS="--openssl-legacy-provider" \
   -e START_COMMAND="${START_COMMAND}" \
   test

#-e NODE_DEBUG=* \