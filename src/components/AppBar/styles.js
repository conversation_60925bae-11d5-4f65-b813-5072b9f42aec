import { makeStyles } from '@material-ui/core/styles';
import { fade } from '@material-ui/core';
import { colors, drawerModalStyle, fontStyles } from '../../../styles/theme';
import palette from "../../../styles/palette";

const useStyles = makeStyles((theme) => ({
  appBar: {
    background: palette.grayscale["200"]
  },
  logoAnchor: {
    display: 'block',
    lineHeight: '1px', // fix for extra height on anchor
    height: 30
  },
  logoImg: {
    height: 30
  },
  callToActionBtn: {
    background: theme.palette.primary.main,
    color: 'rgba(255, 255, 255, 0.84)'
  },
  nav: {
    '& > button': {
      marginLeft: theme.spacing(1)
    }
  },
  sidebarModal: {
    background: theme.palette.common.white,
    position: 'absolute',
    top: 0,
    left: 0,
    width: '80%',
    maxWidth: '300px',
    borderTopRightRadius: 8,
    borderBottomRightRadius: 8,
    minHeight: '100%',
    bottom: 0
  },
  sidebarContainer: {
    '&&': {
      ...drawerModalStyle,
      zIndex: 1700,
    },
  },
  modalWrapper: {
    marginTop: theme.spacing(2),
    background: theme.palette.common.white
  },
  listItemWrapper: {
    marginTop: 6 + 8,
    marginBottom: 6 + 8,
    '& > div:not(:first-child)': {
      marginTop: 8
    }
  },
  divider: {
    borderBottom: `1px solid ${fade(colors.leviee.main.dark, 0.06)}`,
    marginTop: 8,
    marginBottom: 8
  },
  languageFormControlRoot: {
    width: '100%'
  },
  language: {
    marginTop: theme.spacing(4),
  },
  user: {
    display: 'flex',
    marginTop: theme.spacing(1),
    marginBottom: theme.spacing(3) + 8,
  },
  avatar: {
    height: 48,
    width: 48,
    ...fontStyles.secondaryTitleRegular
  },
  label: {
    flex: 1,
    marginLeft: theme.spacing(2),
    minHeight: 48,
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center'
  },
  secondary: {
    marginTop: 2,
    color: colors.leviee.greyscale.darkGray
  },
}));

export default useStyles;
