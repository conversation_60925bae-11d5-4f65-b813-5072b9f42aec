import React, { useEffect, useState } from 'react';
import { LazyLoadImage } from 'react-lazy-load-image-component';
import MuiAppBar from '@material-ui/core/AppBar';
import Container from '@material-ui/core/Container';
import Toolbar from '@material-ui/core/Toolbar';
import {Box, ButtonBase, withStyles} from '@material-ui/core';
import IconButton from '@material-ui/core/IconButton';
import Drawer from 'react-drag-drawer';
import InputBase from '@material-ui/core/InputBase';
import NativeSelect from '@material-ui/core/NativeSelect';
import FormControl from '@material-ui/core/FormControl';
import Link from 'next/link';
import initials from 'initials';
import { useSelector } from 'react-redux';
import isEmpty from '../../utilities/isEmpty';
import {
  ClockIcon,
  CloseIcon,
  RestaurantTypeIcon
} from '../Icons';
import ListItem from '../ListItem';
import ActivityBar from '../ActivityBar';
import { i18n, withTranslation } from '../../../i18n';
import Avatar from '../Avatar';
import { Paragraph } from '../Text';
import { meSelectors, restaurantsSelectors } from '../../../redux/selectors';
import useStyles from './styles';
import { AlloNavLogo50x20Black } from '../../utilities/icons';

const LevieeSimpleInput = withStyles((theme) => ({
  root: {
    'label + &': {
      marginTop: theme.spacing(3),
    },
  },
  input: {
    borderRadius: 4,
    position: 'relative',
    backgroundColor: theme.palette.background.paper,
    border: '1px solid #ced4da',
    padding: '10px 26px 10px 12px',
    transition: theme.transitions.create(['border-color', 'box-shadow']),
    '&:focus': {
      borderRadius: 4,
      boxShadow: 'none'
    },
  },
}))(InputBase);

const LanguageSelector = () => {
  const classes = useStyles();
  const [selectedLang, selectLang] = useState('');

  useEffect(() => {
    selectLang(i18n.language);
  }, []);

  if (selectLang) {
    return (
      <div className={classes.language}>
        <FormControl variant="filled" classes={{ root: classes.languageFormControlRoot }}>
          <NativeSelect
            value={selectedLang}
            onChange={(event) => {
              selectLang(event.target.value);
              i18n.changeLanguage(event.target.value, () => {
                // eslint-disable-next-line no-restricted-globals,no-undef
                location.reload();
                return false;
              });
            }}
            inputProps={{
              name: 'language',
              id: 'language-selector',
            }}
            input={<LevieeSimpleInput />}
          >
            <option value="de">Deutsch</option>
            <option value="en">English</option>
            <option value="zh">中文</option>
          </NativeSelect>
        </FormControl>
      </div>
    );
  }

  return null;
};

const Sidebar = withTranslation('common')(({ t, open, onClose }) => {
  const classes = useStyles();

  const { customer } = useSelector(meSelectors.getMe);

  return (
    <Drawer
      open={open}
      onRequestClose={onClose}
      modalElementClass={classes.sidebarModal}
      containerElementClass={classes.sidebarContainer}
      direction="left"
    >
      <ActivityBar
        color="transparent"
        back={(
          <IconButton component="a" edge="start" color="inherit" aria-label="menu" onClick={onClose}>
            <CloseIcon />
          </IconButton>
        )}
      />
      <div className={classes.modalWrapper}>
        <Container>
          {!isEmpty(customer) && (
            <>
              <div className={classes.user}>
                <Avatar alt={`${customer.firstName ?? ''} ${customer.lastName ?? ''}`} className={classes.avatar}>
                  {initials(`${customer.firstName ?? ''} ${customer.lastName ?? ''}`)}
                </Avatar>
                <div className={classes.label}>
                  <Paragraph>
                    {`${customer.firstName ?? ''} ${customer.lastName ?? ''}`}
                  </Paragraph>
                  <div className={classes.secondary}>
                    <Paragraph>
                      {customer.email || customer.phone}
                    </Paragraph>
                  </div>
                </div>
              </div>
              <div className={classes.divider} />
            </>
          )}
          <div className={classes.listItemWrapper}>
            <Link href="/">
              <a>
                <ListItem icon={<RestaurantTypeIcon />} label={t('navigation-restaurants')} />
              </a>
            </Link>
            <a>
              <ListItem icon={<ClockIcon />} label={t('navigation-historical-orders')} disabled />
            </a>
          </div>
          <div className={classes.divider} />
          <div className={classes.listItemWrapper}>
            <a href="https://company.leviee.de/gtc-for-users">
              <ListItem label={t('footer-terms-of-service-label')} />
            </a>
            <a href="https://company.leviee.de/legal-2/data-privacy">
              <ListItem label={t('footer-data-privacy-label')} />
            </a>
            <a href="https://company.leviee.de/legal-2/imprint">
              <ListItem label={t('footer-imprint-label')} />
            </a>
          </div>
          <div className={classes.divider} />
          <LanguageSelector />
        </Container>
      </div>
    </Drawer>
  );
});

const AppBar = (props) => {
  const classes = useStyles();
  const [sidebar, setSidebar] = useState(false);
  const restaurantLogoUrl = useSelector(restaurantsSelectors.getRestaurantLogoUrl);

  const { action, color, justifyContent = "space-between", ...otherProps } = props;

  return (
    <MuiAppBar position="static" elevation={0} className={classes.appBar} color="inherit" {...otherProps}>
      <Toolbar disableGutters variant="dense">
        <Container maxWidth="lg">
          <Box
            display="flex"
            justifyContent={justifyContent}
            alignItems="center"
            position="relative"
          >
            {restaurantLogoUrl
              ? <LazyLoadImage src={restaurantLogoUrl} height={50} alt="restaurant logo" style={{ paddingTop: 8 }} />
              : <AlloNavLogo50x20Black />}
            {action}
          </Box>
        </Container>
      </Toolbar>
      <Sidebar open={sidebar} onClose={() => setSidebar(false)} />
    </MuiAppBar>
  );
};

export default AppBar;
