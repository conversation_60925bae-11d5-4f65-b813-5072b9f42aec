import React from 'react';
import Link from 'next/link';
import MuiAppBar from '@material-ui/core/AppBar';
import Container from '@material-ui/core/Container';
import Toolbar from '@material-ui/core/Toolbar';
import { Box } from '@material-ui/core';
import Img from '../../Img';
import Button from '../../Buttons/Button';
import useStyles from './styles';

const Desktop = () => {
  const classes = useStyles();

  return (
    <MuiAppBar position="static" elevation={0} className={classes.appBar} color="transparent">
      <Toolbar disableGutters>
        <Container maxWidth="lg">
          <Box
            display="flex"
            justifyContent="space-between"
            alignItems="center"
          >
            <Link href="/" shallow>
              <a className={classes.logoAnchor}>
                <Img alt="leviee-logo" src="/logo/leviee-font-logo-230x100.svg" className={classes.logoImg} />
              </a>
            </Link>
            <div className={classes.nav}>
              {/*<Button>Login</Button>*/}
              {/*<Button>Sign Up</Button>*/}
              <Button variant="contained" className={classes.callToActionBtn}>For Restaurants</Button>
            </div>
          </Box>
        </Container>
      </Toolbar>
    </MuiAppBar>
  );
};

export default Desktop;
