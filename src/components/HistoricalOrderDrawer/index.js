import React, { useEffect, useState } from 'react';
import Container from '@material-ui/core/Container';
import Drawer from 'react-drag-drawer';
import { useDispatch, useSelector } from 'react-redux';
import moment from 'moment';
import { LazyLoadImage } from 'react-lazy-load-image-component';
import isEmpty from '../../utilities/isEmpty';
import ActivityBar from '../ActivityBar';
import { IconButton } from '../Buttons';
import {
  ArrowBackIcon, ClockIconDark16, PinIconDark16,
} from '../Icons';
import { meSelectors, orderSelectors } from '../../../redux/selectors';
import { DisplayTitle, SmallParagraph, Title } from '../Text';
import AvatarItem from '../AvatarItem';
import OrderItem from '../OrderItem';
import { withTranslation } from '../../../i18n';
import { DisplayTitleContainer, TitleContainer } from '../Containers';
import { getRestaurantById } from '../../../redux/api';
import useStyles from './styles';
import { orderActions } from '../../../redux/actions';

const HistoricalOrderDrawer = ({ t, open, onClose }) => {
  const classes = useStyles();
  const dispatch = useDispatch();

  useEffect(() => {
    dispatch(orderActions.getActiveOrder());
  }, []);

  const { activeOrder = {} } = useSelector(meSelectors.getMyActiveOrder);
  const { restaurantId, total = 0, items = [] } = activeOrder;
  const [restaurant, setRestaurant] = useState({});

  const { participant } = useSelector(orderSelectors.getParticipant);
  const { customer = {} } = participant;
  const { firstName = '', lastName = '' } = customer;

  let resolvedAmount = 0;
  (items || [])
    .filter((i) => ['CANCELLED'].indexOf(i.status) < 0)
    .forEach(({ total: itemTotal }) => {
      resolvedAmount += itemTotal;
    });

  resolvedAmount = (total || resolvedAmount).toFixed(2);

  const { name = '', address = {} } = restaurant;
  const { street, number, zipCode, city } = address;
  const resolvedAddress = `${street} ${number}, ${zipCode} ${city}`;
  const resolveLocation = `https://www.google.com/maps/dir/?api=1&destination=${name.split(' ').join('+')}+${street}+${number}+${zipCode}`;

  useEffect(() => {
    if (restaurantId) {
      getRestaurantById(restaurantId).then(({ data }) => setRestaurant(data)).catch(() => {});
    }
  }, [restaurantId]);

  return (
    <Drawer
      open={open}
      onRequestClose={onClose}
      modalElementClass={classes.drawerModal}
      containerElementClass={classes.drawerContainer}
    >
      <ActivityBar
        title={t('activity-title-current-order')}
        color="transparent"
        position="sticky"
        back={(
          <IconButton component="a" edge="start" color="inherit" aria-label="menu" onClick={onClose}>
            <ArrowBackIcon />
          </IconButton>
        )}
      />
      <div className={classes.modalWrapper}>
        <Container>
          <div className={classes.restaurantMeta}>
            {restaurant.thumbnailUrl && (
              <LazyLoadImage
                alt={restaurant.name}
                src={restaurant.thumbnailUrl}
                className={classes.img}
              />
            )}
            <div className={classes.titleWrapper}>
              <DisplayTitle>
                {restaurant.name}
              </DisplayTitle>
            </div>
            <div className={classes.orderTime}>
              <IconButton edge="start" disabled onClick={() => {}} label={moment(activeOrder.creationTime).calendar()}>
                <ClockIconDark16 />
              </IconButton>
            </div>
            <div className={classes.orderRoute}>
              <a href={resolveLocation} target="_blank" rel="noreferrer">
                <IconButton edge="start" disabled onClick={() => {}} label={resolvedAddress}>
                  <PinIconDark16 />
                </IconButton>
              </a>
            </div>
          </div>
          {!isEmpty(customer) && (
            <AvatarItem
              firstName={firstName}
              lastName={lastName}
            />
          )}
          <div className={classes.orderItemWrapper}>
            {items
              .map((i) => (
                <OrderItem
                  unitPrice={i.unitPrice}
                  name={i.name}
                  qtd={i.qtd}
                  {...i}
                />
              ))}
          </div>
        </Container>
        <TitleContainer withText divider>
          <Container>
            <div className={classes.totalRow}>
              <Title weight="medium">
                {t('pickup-success-order-summary-total-label')}
              </Title>
              <Title weight="medium">
                {`${resolvedAmount}€`}
              </Title>
            </div>
          </Container>
        </TitleContainer>
        <DisplayTitleContainer>
          <Container>
            <div className={classes.vatDisclaimer}>
              <SmallParagraph>
                {t('order-summary-vat-disclaimer-label')}
              </SmallParagraph>
            </div>
          </Container>
        </DisplayTitleContainer>
      </div>
    </Drawer>
  );
};

export default withTranslation('common')(HistoricalOrderDrawer);
