import { makeStyles } from '@material-ui/core/styles';
import { fade } from '@material-ui/core';
import { colors, drawerModalStyle } from '../../../styles/theme';

const useStyles = makeStyles((theme) => ({
  drawerModal: {
    background: theme.palette.common.white,
    position: 'absolute',
    top: 200,
    width: '100%',
    maxWidth: '700px',
    borderTopLeftRadius: 8,
    borderTopRightRadius: 8,
    minHeight: '100%',
    bottom: 0
  },
  drawerContainer: {
    '&&': {
      ...drawerModalStyle
    }
  },
  modalWrapper: {
    marginTop: theme.spacing(4),
    background: theme.palette.common.white
  },
  restaurantMeta: {
    paddingBottom: 16
  },
  img: {
    height: 180,
    width: '100%',
    objectFit: 'cover',
    borderRadius: 12,
  },
  titleWrapper: {
    marginTop: 16
  },
  orderTime: {
    marginTop: theme.spacing(2),

    // fix issue with visible single pixel between menu and header on scroll stick
    paddingBottom: 1
  },
  orderRoute: {
    marginTop: theme.spacing(2) - 4,

    // fix issue with visible single pixel between menu and header on scroll stick
    paddingBottom: 1,
    '& p': {
      textDecoration: 'underline'
    }
  },
  orderItemWrapper: {
    marginTop: 8,
    '& > div:not(:first-child)': {
      marginTop: 8
    }
  },
  totalRow: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between'
  },
  vatDisclaimer: {
    color: fade(colors.leviee.main.dark, 0.48)
  }
}));

export default useStyles;
