import React from 'react';
import { useSelector } from 'react-redux';
import { useRouter } from "next/router";
import isEmpty from '../../utilities/isEmpty';
import { withTranslation } from '../../../i18n';
import { meSelectors, uiSelectors } from '../../../redux/selectors';
import ButtonBase from "@material-ui/core/ButtonBase";
import palette from "../../../styles/palette";
import {Typography} from "@material-ui/core";
import typography from "../../../styles/typography";
import shadows from "../../../styles/shadows";

const ReturnToOrderBtn = ({ t }) => {
  const router = useRouter();
  const { activeOrder = {} } = useSelector(meSelectors.getMyActiveOrder);
  const { type } = activeOrder;
  const { loaded } = useSelector(uiSelectors.getMyActiveOrder);

  if (isEmpty(activeOrder)) {
    return null;
  }

  if (type !== 'DINE_IN') {
    return null;
  }

  const { id, restaurant = {} } = activeOrder;
  if (isEmpty(restaurant) || !id) {
    return null;
  }
  
  const { slug, name } = restaurant;
  const isLoading = !loaded;
  
  const onClick = () => {
    return router.push(id ? `/restaurant/${slug}/order/${id}` : '/scan')
  }

  return (
    <div style={{
      position: "absolute",
      width: "100%",
      bottom: 24,
      paddingLeft: 16,
      paddingRight: 16
    }}>
      <div style={{
        maxWidth: 400,
        margin: "0 auto",
        paddingTop: 16,
        paddingLeft: 16,
        paddingRight: 16,
        paddingBottom: 12,
        backgroundColor: palette.grayscale["800"],
        borderRadius: 12,
        ...shadows.large
      }}>
        <div style={{ paddingBottom: 16 }}>
          <Typography style={{ ...typography.body.medium, color: palette.grayscale.white }}>
            {t('your-order')}
          </Typography>
          <Typography style={{ ...typography.body.regular, color: palette.transparency.light["80"] }}>
            {t('you-have-an-order-in-progress-at-restaurant', { name })}
          </Typography>
        </div>
        <div style={{
          borderTop: `1px dashed ${palette.transparency.light["20"]}`,
          paddingTop: 12,
          display: "flex",
          justifyContent: "flex-end",
          width: "100%"
        }}>
          <ButtonBase
            onClick={onClick}
            disableRipple
            disableTouchRipple
            loading={isLoading}
            disabled={isLoading}
            style={{
              paddingTop: 12,
              paddingBottom: 12,
              paddingLeft: 24,
              paddingRight: 24,
              background: palette.primary["500"],
              borderRadius: 12
            }}
          >
            <Typography style={{ ...typography.body.medium, color: palette.grayscale["100"] }}>
              {t('landing-return-to-order-btn-label')}
            </Typography>
          </ButtonBase>
        </div>
      </div>
    </div>
  );
};

export default withTranslation('common')(ReturnToOrderBtn);
