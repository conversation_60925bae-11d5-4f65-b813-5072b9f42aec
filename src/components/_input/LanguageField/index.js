import React, {useEffect, useState} from 'react';
import {i18n} from "../../../../i18n";
import NativeSelect from "@material-ui/core/NativeSelect";
import Field from "../Field";

const LanguageField = () => {
	const [selectedLang, selectLang] = useState('');
	
	useEffect(() => {
		selectLang(i18n.language);
	}, []);
	
	if (selectLang) {
		return (
			<NativeSelect
				value={selectedLang}
				onChange={(event) => {
					selectLang(event.target.value);
					i18n.changeLanguage(event.target.value, () => {
						// eslint-disable-next-line no-restricted-globals,no-undef
						location.reload();
						return false;
					});
				}}
				input={<Field name={'language'} skipNavigatorCheck />}
			>
				<option value="de">Deutsch</option>
				<option value="en">English</option>
				<option value="zh">中文</option>
			</NativeSelect>
		);
	}
	
	return null;
};

export default LanguageField;
