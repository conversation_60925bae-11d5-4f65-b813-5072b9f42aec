import { makeStyles } from '@material-ui/core/styles';
import typography from "../../../../styles/typography";
import palette from "../../../../styles/palette";

const useStyles = makeStyles(theme => ({
  formControlRoot: {
    width: '100%'
  },
  inputLabel: {
    ...typography.body.medium,
    textTransform: "capitalize",
    transform: "scale(1)",
    whiteSpace: "nowrap"
  },
  inputBaseRoot: {
    backgroundColor: palette.grayscale["100"],
    'label + &': {
      marginTop: 28,
    },
    transition: theme.transitions.create(['border-color', 'box-shadow']),
    border: "1px solid #D8D7D6",
    borderRadius: 12,
    "&:focus": {
      borderColor: "#333332",
      borderRadius: 12,
    },
    "&:active": {
      borderColor: "#333332",
      borderRadius: 12,
    },
    "&:focus-within": {
      borderColor: "#333332",
      borderRadius: 12,
    },
    "&:hover": {
      borderColor: "#333332",
      borderRadius: 12,
    },
  },
  inputBaseRootError: {
    borderColor: `${palette.negative["500"]} !important`
  },
  inputBaseRootMultiline: {
    display: "flex",
    alignItems: "flex-start"
  },
  inputBaseInput: {
    height: 42, // 44 - 2px border
    position: 'relative',
    padding: '0px 14px',
    ...typography.medium.regular
  },
  selectInputBaseInput: {
    height: "100%",
    minHeight: 36,
    paddingTop: 4,
    paddingBottom: 4
  },
  noFocusInputBaseInput: {
    color: palette.grayscale.black
  },
  dateBaseInput: {
    lineHeight: "38px"
  },
  timeBaseInput: {
    lineHeight: "40px"
  },
  adornStartInput: {
    paddingLeft: 36
  },
  inputBaseMultiline: {
    paddingTop: 11,
    paddingBottom: 11,
    borderRadius: 12,
    minHeight: 88,
    border: "1px solid #D8D7D6",
    "& > textarea": {
      border: "none",
      lineHeight: "20px"
    }
  }
}));

export default useStyles;

