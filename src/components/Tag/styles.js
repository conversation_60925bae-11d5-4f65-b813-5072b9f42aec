import { makeStyles } from '@material-ui/core/styles';
import { colors } from '../../../styles/theme';

const useStyles = makeStyles((theme) => ({
  container: {
    display: 'inline-flex',
    background: colors.leviee.greyscale.lightestGray,
    border: `1px solid ${colors.leviee.greyscale.lighterGray}`,
    padding: theme.spacing('4px', 1, '4px', 1),
    borderRadius: 4
  },
  layout: {
    display: 'flex',
    flexDirection: 'row'
  },
  icon: {
    marginRight: 2,
    lineHeight: '1px'
  }
}));

export default useStyles;
