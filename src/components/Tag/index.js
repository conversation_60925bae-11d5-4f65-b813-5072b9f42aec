import React from 'react';
import { SmallParagraph } from '../Text';
import useStyles from './styles';

const Tag = ({ icon, text }) => {
  const classes = useStyles();

  return (
    <div className={classes.container}>
      <div className={classes.layout}>
        {icon && (
          <div className={classes.icon}>
            {icon}
          </div>
        )}
        <SmallParagraph>
          {text}
        </SmallParagraph>
      </div>
    </div>
  );
};

export default Tag;
