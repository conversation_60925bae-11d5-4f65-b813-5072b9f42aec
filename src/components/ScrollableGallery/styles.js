import { makeStyles } from '@material-ui/core/styles';

const useStyles = makeStyles((theme) => ({
  margin: {
    marginLeft: -theme.spacing(2),
    marginRight: -theme.spacing(2),
  },
  scrollHider: {
    height: '100%',
    width: '100%',
    overflowY: 'hidden'
  },
  scrollContainer: {
    height: '100%',
    overflowY: 'hidden',
    overflowX: 'scroll',
    whiteSpace: 'nowrap',
    paddingBottom: '30px',
    marginBottom: -30,
  },
  itemsContainer: {
    display: 'flex'
  },
  itemContainer: {
    display: 'inline-block',
    boxSizing: 'content-box',
    marginLeft: theme.spacing(2),
    '&:last-child': {
      paddingRight: theme.spacing(2)
    }
  },
  '@media (min-width: 800px)': {
    itemContainer: {
      borderWidth: '0px 5px',
      width: '100%',
    }
  },
  item: {
  },
  itemBody: {
    paddingTop: theme.spacing(1) + 2,
    paddingBottom: theme.spacing(1) + 2,
    whiteSpace: 'normal'
  },
  imageBorders: {
    position: 'relative',
    // borderTopLeftRadius: theme.spacing(1),
    // borderTopRightRadius: theme.spacing(1),
    overflow: 'hidden'
  },
  imageWrapper: {
    paddingTop: '50%',
    position: 'relative',
    backgroundPosition: '50% 50%',
    backgroundRepeat: 'no-repeat'
  },
  imagePositioning: {
    position: 'absolute',
    top: 0,
    bottom: 0,
    left: 0,
    right: 0,
    display: 'flex',
    justifyContent: 'center'
  },
  image: {
    objectFit: 'cover',
    height: '100%',
    width: '100%',
    borderRadius: theme.spacing(1)
  },
  wider: {
    paddingLeft: theme.spacing(1) + 2,
    paddingRight: theme.spacing(1) + 2,
  },
}));

export default useStyles;
