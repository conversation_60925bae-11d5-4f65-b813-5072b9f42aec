import React from 'react';
import Skeleton from '@material-ui/lab/Skeleton';
import Box from '@material-ui/core/Box';
import isEmpty from '../../utilities/isEmpty';
import useStyles from './styles';

const ScrollableGallery = ({ loading, items = [], height, width, radius }) => {
  const classes = useStyles();

  if (isEmpty(items) && !loading) {
    return null;
  }

  return (
    <div className={classes.margin}>
      <div className={classes.scrollHider}>
        <div className={classes.scrollContainer}>
          <div className={classes.itemsContainer}>
            {items.map(({ id, alt, src }) => (
              <div className={classes.itemContainer} key={id} style={{ minWidth: width }}>
                <div className={classes.item}>
                  <div className={classes.content}>
                    <div className={classes.imageBorders}>
                      <div className={classes.imageWrapper} style={{ paddingTop: height }}>
                        <div className={classes.imagePositioning}>
                          <img
                            alt={alt}
                            src={src}
                            className={classes.image}
                            style={{ borderRadius: radius }}
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
            {isEmpty(items) && [1, 2, 3].map((id) => (
              <div className={classes.itemContainer} key={id}>
                <Skeleton variant="rect" width={width} height={height} />
                <Box pt={0.5}>
                  <Skeleton />
                  <Skeleton width="60%" />
                </Box>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ScrollableGallery;
