import React from 'react';
import FormControl from "@material-ui/core/FormControl";
import InputBase from "@material-ui/core/InputBase";
import InputLabel from "@material-ui/core/InputLabel";
import {generate} from "shortid";
import useStyles from "./styles";
import palette from "../../../../styles/palette";

const Field = React.forwardRef((props, ref) => {
	const classes = useStyles();
	const { id: componentId, label, required, ...otherProps } = props;
	const id = componentId || `field-${generate()}`;
	
	return (
		<FormControl classes={{ root: classes.formControlRoot }}>
			{label && (
				<InputLabel shrink htmlFor={id} className={classes.inputLabel}>
					{label}
					{required && <span style={{ color: palette.primary["500"] }}>*</span>}
				</InputLabel>
			)}
			<InputBase id={id} classes={{
				root: classes.inputBaseRoot,
				input: classes.inputBaseInput
			}} ref={ref} required={required} {...otherProps} />
		</FormControl>
	)
});

export default Field;
