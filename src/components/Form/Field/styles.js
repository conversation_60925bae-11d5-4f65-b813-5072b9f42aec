import { makeStyles } from '@material-ui/core/styles';
import typography from "../../../../styles/typography";
import palette from "../../../../styles/palette";

const useStyles = makeStyles(theme => ({
  formControlRoot: {
    width: '100%'
  },
  inputLabel: {
    ...typography.body.medium,
    textTransform: "capitalize",
    transform: "scale(1)"
  },
  inputBaseRoot: {
    backgroundColor: palette.grayscale["100"],
    borderRadius: 12,
    'label + &': {
      marginTop: 24,
    },
  },
  inputBaseInput: {
    height: 42, // 44 - 2px border
    position: 'relative',
    padding: '0px 14px',
    transition: theme.transitions.create(['border-color', 'box-shadow']),
    border: "1px solid #D8D7D6",
    borderRadius: 12,
    "&:focus": {
      borderColor: "#333332",
      borderRadius: 12,
    }
  }
}));

export default useStyles;
