import React, { useState } from 'react';
import { LazyLoadImage } from 'react-lazy-load-image-component';
import clsx from 'clsx';
import getConfig from 'next/config';
import isEmpty from '../../utilities/isEmpty';
import { DisplayTitle, Paragraph, Title } from '../Text';
import { TitleContainer } from '../Containers';
// eslint-disable-next-line import/named
import { withTranslation } from '../../../i18n';
import useStyles from './styles';

const { publicRuntimeConfig } = getConfig();

const MenuItemAdditionElementDetails = ({
  t, thumbnailUrl, name, description, remarks, unitPrice, salePrice, volume, tags = []
}) => {
  const classes = useStyles();

  const [imgError, setImgError] = useState(false);
  unitPrice = (unitPrice || 0).toFixed(Number.isInteger(unitPrice) ? 0 : 2);
  salePrice = salePrice && salePrice.toFixed(Number.isInteger(salePrice) ? 0 : 2);

  return (
    <div className={classes.container}>
      <div>
        <div className={classes.layout}>
          {thumbnailUrl && (
            <LazyLoadImage
              alt={name}
              src={thumbnailUrl}
              className={clsx(classes.img, { [classes.imgError]: imgError })}
              onError={() => setImgError(true)}
            />
          )}
          <div className={classes.text}>
            <div className={classes.name}>
              <DisplayTitle>{name}</DisplayTitle>
            </div>
            {!isEmpty(tags) && (
              <div className={classes.tags}>
                {tags.map((tag) => (
                  <div className={classes.tag}>
                    <img className={classes.tagImg} src={`${publicRuntimeConfig.basePath}/icons/tags/${tag.identifier}.svg`} alt={tag.identifier} />
                    <Paragraph>{tag.label}</Paragraph>
                  </div>
                ))}
              </div>
            )}
            {description && (
              <div className={classes.description}>
                <Paragraph>{description}</Paragraph>
              </div>
            )}
            {volume && (
              <div className={classes.description}>
                <Paragraph>{volume}</Paragraph>
              </div>
            )}
            <div className={classes.pricing}>
              <div className={classes.price}>
                <Paragraph>{`+ ${salePrice || unitPrice}€`}</Paragraph>
              </div>
              {salePrice && (
                <div className={classes.regularPrice}>
                  <Paragraph>{`+ ${unitPrice}€`}</Paragraph>
                </div>
              )}
            </div>
          </div>
        </div>
        {!isEmpty(remarks) && (
          <div className={classes.remarks}>
            <div className={classes.remarksHeader}>
              <TitleContainer divider>
                <Title weight="medium">
                  {t('order-item-details-remarks-section-title')}
                </Title>
                <div className={classes.remarksDescription}>
                  <Paragraph>
                    {t('order-item-details-remarks-section-descriptions')}
                  </Paragraph>
                </div>
                <div className={classes.remarkList}>
                  <ul>
                    {remarks.map(({ id, description: remarkDescription }) => (
                      <li className={classes.remarkDescription}>
                        <Paragraph key={id}>
                          {remarkDescription}
                        </Paragraph>
                      </li>
                    ))}
                  </ul>
                </div>
              </TitleContainer>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default withTranslation('common')(MenuItemAdditionElementDetails);
