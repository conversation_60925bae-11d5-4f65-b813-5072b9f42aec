import { fade, makeStyles } from '@material-ui/core/styles';
import { colors } from '../../../../styles/theme';

const useStyles = makeStyles((theme) => ({
  buttonRoot: {
    width: '100%',
    borderRadius: theme.spacing(1),
  },
  disabled: {
    opacity: 0.4
  },
  badge: {
    width: '100%',
  },
  badgePosition: {
    top: 6,
    right: 6
  },
  wrapper: {
    padding: theme.spacing(2, 2, '16px', 2),
    background: colors.leviee.greyscale.lightestGray,
    width: '100%',
    borderRadius: theme.spacing(1)
  },
  layout: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center'
  },
  icon: {
    marginBottom: theme.spacing(1) + 2,
    margin: '0 auto'
  },
  title: {
    marginBottom: 2,
    textAlign: 'center',
  },
  description: {
    textAlign: 'center',
    color: fade(colors.leviee.main.dark, 0.48),
  }
}));

export default useStyles;
