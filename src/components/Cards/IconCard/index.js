import React from 'react';
import ButtonBase from '@material-ui/core/ButtonBase';
import Badge from '@material-ui/core/Badge';
import clsx from 'clsx';
// eslint-disable-next-line import/named
import { withTranslation } from '../../../../i18n';
import { Paragraph, SmallParagraph } from '../../Text';
import { CheckIconRound } from '../../Icons';
import useStyles from './styles';

const IconCard = ({ icon, title, description, showBadge, disabled, ...otherProps }) => {
  const classes = useStyles();

  return (
    <ButtonBase
      classes={{ root: classes.buttonRoot }}
      className={clsx({ [classes.disabled]: !!disabled })}
      disabled={disabled}
      {...otherProps}
    >
      <Badge
        className={classes.badge}
        classes={{ anchorOriginTopRightRectangle: classes.badgePosition }}
        overlap="rectangle"
        anchorOrigin={{
          vertical: 'top',
          horizontal: 'right',
        }}
        badgeContent={showBadge ? <CheckIconRound /> : null}
      >
        <div className={classes.wrapper}>
          <div className={classes.layout}>
            {icon && (
              <div className={classes.icon}>{icon}</div>
            )}
            <div className={classes.title}>
              <Paragraph>{title}</Paragraph>
            </div>
            <div className={classes.description}>
              <SmallParagraph>{description}</SmallParagraph>
            </div>
          </div>
        </div>
      </Badge>
    </ButtonBase>
  );
};

export default withTranslation('common')(IconCard);
