import { makeStyles } from '@material-ui/core/styles';

const useStyles = makeStyles((theme) => ({
  appBar: {
  },
  logoAnchor: {
    display: 'block',
    lineHeight: '1px' // fix for extra height on anchor
  },
  logoImg: {
    height: 30
  },
  callToActionBtn: {
    background: theme.palette.primary.main,
    color: 'rgba(255, 255, 255, 0.84)'
  },
  nav: {
    '& > button': {
      marginLeft: theme.spacing(1)
    }
  }
}));

export default useStyles;
