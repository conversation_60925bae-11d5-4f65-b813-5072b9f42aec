import React, {useEffect, useState} from 'react'
import {withTranslation} from "../../../i18n";
import {useSelector} from "react-redux";
import {restaurantsSelectors} from "../../../redux/selectors";
import {getDiscountConfig} from "../../../redux/api";
import isEmpty from "../../utilities/isEmpty";
import Typography from "@material-ui/core/Typography";
import typography from "../../../styles/typography";
import palette from "../../../styles/palette";
import shadows from "../../../styles/shadows";

const PromotionDisclaimer = ({ t }) => {
  const { restaurant } = useSelector(restaurantsSelectors.getRestaurant);
  const { id } = (restaurant || {})
  
  const [discountConfiguration, setDiscountConfiguration] = useState(null);
  
  const { pickupDiscountPercentage, promotionText } = (discountConfiguration || {});
  const showDiscountModal = !isEmpty(discountConfiguration) && pickupDiscountPercentage
  
  useEffect(() => {
    if (id) {
      getDiscountConfig(id).then(({ data = {} }) => {
        const fetched = data.data || {};
        const { getRestaurantConfig = {} } = fetched || {};
        const { webshopConfig = {} } = getRestaurantConfig || {};
        setDiscountConfiguration(webshopConfig)
      }).catch(() => {})
    }
  }, [id])
  
  if (showDiscountModal) {
    return (
      <div style={{ background: palette.grayscale["100"], ...shadows.base, padding: 12, borderRadius: 12, maxWidth: 400 }}>
        <Typography style={{ ...typography.medium.medium, marginBottom: 4 }}>{t('percent-off-on-pickup', { percent: pickupDiscountPercentage })}</Typography>
        <Typography style={{ ...typography.medium.regular }}>{promotionText || t('pickup-discount-description')}</Typography>
      </div>
    )
  }
  
  return null;
}

export default withTranslation('common')(PromotionDisclaimer);
