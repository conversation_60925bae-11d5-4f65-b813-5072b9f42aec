import React from 'react';
import Container from '@material-ui/core/Container';
import Drawer from 'react-drag-drawer';
import { useSelector } from 'react-redux';
import isEmpty from '../../utilities/isEmpty';
import ActivityBar from '../ActivityBar';
import { IconButton } from '../Buttons';
import {
  ArrowBackIcon, CalendarIcon,
  InfoIcon,
  PhoneIcon,
  PinIcon
} from '../Icons';
import ListItem from '../ListItem';
import { restaurantsSelectors } from '../../../redux/selectors';
import { withTranslation } from '../../../i18n';
import { Paragraph } from '../Text';
import calculateOpeningHours from '../../utilities/calculateOpeningHours';
import useStyles from './styles';

const RestaurantDetailsDrawer = ({ t, open, onClose }) => {
  const classes = useStyles();

  const { restaurant } = useSelector(restaurantsSelectors.getRestaurant);
  const { name = '', address = {}, phone, description, openingHours = {} } = restaurant;
  const {
    MONDAY = {},
    TUESDAY = {},
    WEDNESDAY = {},
    THURSDAY = {},
    FRIDAY = {},
    SATURDAY = {},
    SUNDAY = {}
  } = (isEmpty(openingHours) ? {} : openingHours);
  const { street, number, zipCode, city } = address;
  const resolvedAddress = `${street} ${number}, ${zipCode} ${city}`;
  const resolveLocation = `https://www.google.com/maps/dir/?api=1&destination=${name.split(' ').join('+')}`;

  return (
    <Drawer
      open={open}
      onRequestClose={onClose}
      modalElementClass={classes.drawerModal}
      containerElementClass={classes.drawerContainer}
    >
      <ActivityBar
        title={t('activity-title-restaurant-details')}
        color="transparent"
        back={(
          <IconButton component="a" edge="start" color="inherit" aria-label="menu" onClick={onClose}>
            <ArrowBackIcon />
          </IconButton>
        )}
      />
      <div className={classes.modalWrapper}>
        <Container>
          <div className={classes.listItemWrapper}>
            {/*
              <ListItem icon={<RestaurantTypeIcon />} label="Italian" />
              <ListItem icon={<EuroIcon />} label="€-€€" />
              <ListItem icon={<ClockIcon />} label="15-20 min" />
              <ListItem icon={<CalendarIcon />} label="24/7" />
            */}
            <div key="address">
              <a href={resolveLocation} target="_blank" rel="noreferrer">
                <ListItem icon={<PinIcon />} label={resolvedAddress} />
              </a>
            </div>
            <div key="phone">
              <a href={`tel:${phone}`}>
                <ListItem icon={<PhoneIcon />} label={phone} />
              </a>
            </div>
            <ListItem
              icon={<CalendarIcon />}
              key="openHours"
              labelComponent={(
                <div>
                  <div className={classes.item} key="monday">
                    <Paragraph>{t('common-monday')}</Paragraph>
                    <Paragraph>{calculateOpeningHours(MONDAY)}</Paragraph>
                  </div>
                  <div className={classes.item} key="tuesday">
                    <Paragraph>{t('common-tuesday')}</Paragraph>
                    <Paragraph>{calculateOpeningHours(TUESDAY)}</Paragraph>
                  </div>
                  <div className={classes.item} key="wednesday">
                    <Paragraph>{t('common-wednesday')}</Paragraph>
                    <Paragraph>{calculateOpeningHours(WEDNESDAY)}</Paragraph>
                  </div>
                  <div className={classes.item} key="thursday">
                    <Paragraph>{t('common-thursday')}</Paragraph>
                    <Paragraph>{calculateOpeningHours(THURSDAY)}</Paragraph>
                  </div>
                  <div className={classes.item} key="friday">
                    <Paragraph>{t('common-friday')}</Paragraph>
                    <Paragraph>{calculateOpeningHours(FRIDAY)}</Paragraph>
                  </div>
                  <div className={classes.item} key="saturday">
                    <Paragraph>{t('common-saturday')}</Paragraph>
                    <Paragraph>{calculateOpeningHours(SATURDAY)}</Paragraph>
                  </div>
                  <div className={classes.item} key="sunday">
                    <Paragraph>{t('common-sunday')}</Paragraph>
                    <Paragraph>{calculateOpeningHours(SUNDAY)}</Paragraph>
                  </div>
                </div>
              )}
            />
            <ListItem key="description" icon={<InfoIcon />} label={description} labelStyles={classes.description} />
          </div>
        </Container>
      </div>
    </Drawer>
  );
};

export default withTranslation('common')(RestaurantDetailsDrawer);
