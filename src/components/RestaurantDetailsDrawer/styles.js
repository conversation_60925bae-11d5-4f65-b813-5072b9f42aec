import { makeStyles } from '@material-ui/core/styles';
import { drawerModalStyle } from '../../../styles/theme';

const useStyles = makeStyles((theme) => ({
  drawerModal: {
    background: theme.palette.common.white,
    position: 'absolute',
    top: 200,
    width: '100%',
    maxWidth: '700px',
    borderTopLeftRadius: 8,
    borderTopRightRadius: 8,
    minHeight: '100%',
    bottom: 0
  },
  drawerContainer: {
    '&&': {
      ...drawerModalStyle
    }
  },
  modalWrapper: {
    marginTop: theme.spacing(4),
    background: theme.palette.common.white
  },
  listItemWrapper: {
    '& > div:not(:first-child)': {
      marginTop: 8
    }
  },
  item: {
    paddingBottom: 8,
    '&:not(:first-child)': {
      paddingTop: 8
    },
    display: 'flex',
    '& > p': {
      marginRight: 8,
      minWidth: 90
    }
  },
  description: {
    whiteSpace: 'pre-line'
  }
}));

export default useStyles;
