import React from 'react';
import clsx from 'clsx';
import { loadStripe } from '@stripe/stripe-js';
import Cookies from 'js-cookie';
import { useDispatch } from 'react-redux';
import getConfig from 'next/config';
import { createCheckoutSession, getStripeConfiguration, updatePickup } from '../../../redux/api';
import { MainButton } from '../Buttons';
// eslint-disable-next-line import/named
import { withTranslation } from '../../../i18n';
import formatNumber from '../../utilities/formatNumber';
import useStyles from './styles';
import { appActions, orderActions } from '../../../redux/actions';
import { key } from '../../../stripe';
import { googleTags } from '../../../gtm';
import palette from "../../../styles/palette";
import Typography from "@material-ui/core/Typography";
import typography from "../../../styles/typography";
import ButtonBase from "@material-ui/core/ButtonBase";
import {useRouter} from "next/router";

const { publicRuntimeConfig } = getConfig();

const noop = () => {};

class StripePayActionWrapper extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      stripePromise: '',
    };
  }

  componentDidMount() {
    const { restaurantId } = this.props;
    if (restaurantId) {
      this.setup(restaurantId);
    }
  }

  setup = (restaurantId) => {
    const isProd = publicRuntimeConfig.APPLICATION_ENV === 'production';
    console.log(`Detected Stripe configuration is live: ${isProd}`);
    getStripeConfiguration(restaurantId)
      .then(({ data }) => {
        this.setState({
          stripePromise: loadStripe(key, { stripeAccount: data.stripeAccountId })
        });
      })
      .catch(() => {});
  }

  render() {
    const { stripePromise } = this.state;
    if (stripePromise) {
      return (
        <StripePayAction stripePromise={stripePromise} {...this.props} />
      );
    }
    return null;
  }
}

const StripePayAction = ({ t, count, description, errorMsg, amount = 0, stripePromise, callback, loading, disabled, ...otherProps }) => {
  const classes = useStyles();
  const dispatch = useDispatch();
  delete otherProps.tReady;
  
  const router = useRouter();

  const orderId = Cookies.get('lvPickupId');

  const handleClick = async () => {
    if (callback) {
      callback();
    }
    // Get Stripe.js instance
    const stripe = await stripePromise;
    const { customer, type, takeawayDate, pickupTime, notes } = otherProps.form;
    updatePickup(orderId, customer, type, 'PAYMENT_REQUESTED', takeawayDate, pickupTime, notes)
      .then(({ data }) => {
        const { customer: storedCustomer } = data;
        dispatch(appActions.setMe(storedCustomer));
        dispatch(orderActions.getOrderSuccess(data));
        // eslint-disable-next-line no-console
        console.log(`[LOG] Updated order to PAYMENT_REQUESTED with order id ${orderId}`);

        createCheckoutSession(orderId, storedCustomer.id)
          .then(({ data: checkoutData }) => {
            const { sessionUrl } = checkoutData;
            // eslint-disable-next-line no-console
            // console.log(`[LOG] Stripe checkout triggered for session id ${id}`);
            // stripe.redirectToCheckout({ sessionId: id })
            //   .then((res) => {
            //     if (res && res.error) {
            //       console.log(res.error.message);
            //     }
            //   })
            //   .catch((e) => {
            //     console.log(e);
            //     if (e.error) {
            //       console.log(e.error);
            //     }
            //     if (e.error.message) {
            //       console.log(e.error.message);
            //     }
            //   });
            router.push(sessionUrl);
          })
          .catch((err = {}) => {
            const { response = {} } = err;
            const { status } = response;
            // eslint-disable-next-line no-console
            console.log(`[ERROR] Session creation failed for order id ${orderId}`);
            // eslint-disable-next-line no-console
            console.log(`[ERROR-STATUS] ${status}`);
          });
      })
      .catch(() => {});
  };

  return (
    <div style={{
      position: "absolute",
      width: "100%",
      bottom: 24,
      paddingLeft: 16,
      paddingRight: 16
    }}>
      <div style={{
        maxWidth: 400,
        margin: "0 auto",
        paddingTop: 12,
        paddingLeft: 16,
        paddingRight: 16,
        paddingBottom: 12,
        backgroundColor: palette.grayscale["800"],
        borderRadius: 12,
      }}>
        <div style={{ paddingBottom: 16 }}>
          <Typography style={{ ...typography.body.medium, color: palette.grayscale.white }}>
            {t(disabled ? 'missing-information': 'checkout')}
          </Typography>
          <Typography style={{ ...typography.body.regular, color: palette.transparency.light["80"] }}>
            {errorMsg ? errorMsg : t('confirm-checkout-preference-for-your-purchase')}
            {description}
          </Typography>
        </div>
        <div
          style={{
            borderTop: `1px dashed ${palette.transparency.light["20"]}`,
            paddingTop: 12,
            display: "flex",
            justifyContent: "flex-end",
            alignItems: "center",
            width: "100%"
          }}
        >
          <ButtonBase
            style={{
              paddingLeft: 24,
              paddingRight: 24,
              background: (loading || disabled) ? palette.grayscale["400"] : palette.primary["500"],
              borderRadius: 12,
              width: "100%"
            }}
            {...otherProps}
            onClick={(loading || disabled) ? noop : handleClick}
            disabled={disabled}
            loading={loading}
            id={googleTags.dineOut.checkoutBtn.id}
          >
            <div className={classes.buttonLayout}>
              <div>
                <Typography style={{ ...typography.body.medium, color: palette.grayscale["100"] }}>
                  {t(loading ? 'order-payment-btn-label-disabled' : 'order-payment-btn-label')}
                </Typography>
              </div>
              <div className={classes.sidebar}>
                <div className={clsx(classes.divider, { [classes.disabled]: !!otherProps.disabled })} />
                <div className={classes.priceLabel} style={{ ...typography.body.medium, color: palette.grayscale.white }}>
                  {`${formatNumber(amount)} €`}
                </div>
              </div>
            </div>
          </ButtonBase>
        </div>
      </div>
    </div>
  );
};

export default withTranslation('common')(StripePayActionWrapper);
