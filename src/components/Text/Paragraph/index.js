import React from 'react';
import PropTypes from 'prop-types';
import Typography from '@material-ui/core/Typography';
import useStyles from './styles';

const Paragraph = ({ weight, ...otherProps }) => {
  const classes = useStyles();

  const weights = {
    regular: classes.regular,
    medium: classes.medium
  };

  return <Typography component="p" className={weights[weight]} {...otherProps} />;
};

Paragraph.propTypes = {
  weight: PropTypes.oneOf(['regular', 'medium'])
};

Paragraph.defaultProps = {
  weight: 'regular'
};

export default Paragraph;
