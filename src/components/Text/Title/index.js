import React from 'react';
import PropTypes from 'prop-types';
import Typography from '@material-ui/core/Typography';
import useStyles from './styles';

const Title = ({ weight, ...otherProps }) => {
  const classes = useStyles();

  const weights = {
    regular: classes.regular,
    medium: classes.medium
  };

  return <Typography component="h2" className={weights[weight]} {...otherProps} />;
};

Title.propTypes = {
  weight: PropTypes.oneOf(['regular', 'medium'])
};

Title.defaultProps = {
  weight: 'regular'
};

export default Title;
