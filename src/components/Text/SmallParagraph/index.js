import React from 'react';
import PropTypes from 'prop-types';
import Typography from '@material-ui/core/Typography';
import useStyles from './styles';

const SmallParagraph = ({ weight, ...otherProps }) => {
  const classes = useStyles();

  const weights = {
    regular: classes.regular,
    medium: classes.medium
  };

  return <Typography component="p" className={weights[weight]} {...otherProps} />;
};

SmallParagraph.propTypes = {
  weight: PropTypes.oneOf(['regular', 'medium'])
};

SmallParagraph.defaultProps = {
  weight: 'regular'
};

export default SmallParagraph;
