import React from 'react';
import clsx from 'clsx';
import { MainButton } from '../Buttons';
import { withTranslation } from '../../../i18n';
import formatNumber from '../../utilities/formatNumber';
import useStyles from './styles';

const PayAction = ({ t, count, amount = 0, ...otherProps }) => {
  const classes = useStyles();
  delete otherProps.tReady;

  return (
    <div className={classes.container}>
      <div className={classes.layout}>
        <MainButton className={classes.buttonContainer} {...otherProps}>
          <div className={classes.buttonLayout}>
            <div>
              {t(otherProps.disabled ? 'order-payment-btn-label-disabled' : 'order-payment-btn-label')}
            </div>
            <div className={classes.sidebar}>
              <div className={clsx(classes.divider, { [classes.disabled]: !!otherProps.disabled })} />
              <div className={classes.priceLabel}>
                {`${formatNumber(amount)} €`}
              </div>
            </div>
          </div>
        </MainButton>
      </div>
    </div>
  );
};

export default withTranslation('common')(PayAction);
