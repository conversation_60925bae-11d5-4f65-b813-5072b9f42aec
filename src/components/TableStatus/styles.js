import { fade, makeStyles } from '@material-ui/core/styles';
import { colors } from '../../../styles/theme';

const useStyles = makeStyles((theme) => ({
  container: {
    border: `1px solid ${fade(colors.leviee.secondary.lightYellow, 0.16)}`,
    padding: 7,
    borderRadius: 4,
    background: fade(colors.leviee.secondary.lightYellow, 0.08)
  },
  layout: {
    display: 'flex',
    flexDirection: 'row',
  },
  status: {
    display: 'flex',
    alignItems: 'center'
  },
  statusIcon: {
    lineHeight: '1px',
    marginRight: 4
  },
  statusText: {
    color: colors.leviee.secondary.yellow,
    marginRight: 12
  },
  action: {
    background: fade(colors.leviee.secondary.lightYellow, 0.12),
    marginTop: -7,
    marginBottom: -7,
    marginRight: -7,
    display: 'flex',
    alignItems: 'center'
  },
  actionText: {
    marginRight: 2,
    marginLeft: 12
  },
  actionIcon: {
    marginRight: theme.spacing(1) + 2,
    lineHeight: '1px',
  },
  users: {
    flex: 1,
    marginLeft: theme.spacing(2),
    display: 'flex',
    alignItems: 'center'
  },
  requests: {
    marginLeft: theme.spacing(1)
  },
  avatarGroup: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center'
  },
  avatarItem: {
    marginLeft: -12,
    // border: '3px solid #FFF',
    borderRadius: '100%',
    // zIndex: 1600,
    '&:first-child': {
      marginLeft: 0
    }
  },
  clickable: {
    // width: '100%'
  },
  dot: {
    height: 10,
    width: 10,
    background: colors.leviee.secondary.red,
    border: '2px solid #fff',
    borderRadius: '100%'
  },
  anchorRoot: {
    right: '50%',
    bottom: '3%'
  },
  verifyBtn: {
    paddingTop: 4,
    paddingBottom: 4
  }
}));

export default useStyles;
