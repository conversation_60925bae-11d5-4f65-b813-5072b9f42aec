import React from 'react';
import ButtonBase from '@material-ui/core/ButtonBase';
import { useSelector } from 'react-redux';
import initials from 'initials';
import Badge from '@material-ui/core/Badge';
import Typography from '@material-ui/core/Typography';
import { withTranslation } from '../../../i18n';
import { orderSelectors } from '../../../redux/selectors';
import Avatar from '../Avatar';
import isEmpty from '../../utilities/isEmpty';
import useStyles from './styles';
import typography from '../../../styles/typography';

const TableStatus = ({ t, verify, manage }) => {
  const classes = useStyles();

  const { participant } = useSelector(orderSelectors.getParticipant);
  const { participants = [] } = useSelector(orderSelectors.getParticipants);
  const { approvalId } = participant;
  const verified = !!approvalId;

  const approvalRequests = (participants ?? []).filter((p) => !p.approvalId).length;

  if (isEmpty(participant)) {
    return null;
  }

  if (!verified) {
    return null;
  }

  // if (!verified) {
  //   return (
  //     <ButtonBase onClick={verify} disableRipple disableTouchRipple className={classes.verifyBtn}>
  //       <div className={classes.container}>
  //         <div>
  //           <div className={classes.layout}>
  //             <div className={classes.status}>
  //               <div className={classes.statusIcon}>
  //                 <TableVerificationIcon20 />
  //               </div>
  //               <div className={classes.statusText}>
  //                 <SmallParagraph>
  //                   {t('order-table-status-verification-description')}
  //                 </SmallParagraph>
  //               </div>
  //             </div>
  //             <div className={classes.action}>
  //               <div className={classes.actionText}>
  //                 <SmallParagraph>
  //                   {t('order-table-status-verify-table-btn-label')}
  //                 </SmallParagraph>
  //               </div>
  //               <div className={classes.actionIcon}>
  //                 <ArrowRightIcon20 />
  //               </div>
  //             </div>
  //           </div>
  //         </div>
  //       </div>
  //     </ButtonBase>
  //   );
  // }

  return (
    <ButtonBase onClick={manage} className={classes.clickable} disableRipple disableTouchRipple>
      <div className={classes.users}>
        <div>
          <div className={classes.avatarGroup}>
            {(participants ?? []).map(({ id, customer, approvalId: participantApprovalId }) => {
              const customerFullName = `${customer.firstName} ${customer.lastName || ''}`;

              return (
                <div className={classes.avatarItem} key={id}>
                  <Badge
                    overlap="circle"
                    anchorOrigin={{
                      vertical: 'bottom',
                      horizontal: 'right',
                    }}
                    invisible={!!participantApprovalId}
                    badgeContent={<div className={classes.dot} />}
                    classes={{
                      root: classes.badgeRoot,
                      anchorOriginBottomRightCircle: classes.anchorRoot
                    }}
                  >
                    <Avatar alt={customerFullName}>
                      {initials(customerFullName)}
                    </Avatar>
                  </Badge>
                </div>
              );
            })}
          </div>
        </div>
        {!!approvalRequests && (
          <div className={classes.requests}>
            <Typography style={{ ...typography.small.medium }}>
              {t('order-table-status-requests-label', { requests: approvalRequests })}
            </Typography>
          </div>
        )}
      </div>
    </ButtonBase>
  );
};

export default withTranslation('common')(TableStatus);
