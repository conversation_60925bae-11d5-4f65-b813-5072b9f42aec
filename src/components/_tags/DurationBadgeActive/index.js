import React from 'react';
import { Typography } from "@material-ui/core";
import typography from "../../../../styles/typography";
import palette from "../../../../styles/palette";
import {
  DurationIcon16Active
} from "../../../utilities/icons";
import { zeroPadNumber } from "../../../utilities/zeroPadNumber";

const DurationBadgeActive = ({ minutes }) => {
  
  const hrs = Math.floor(minutes / 60);
  const mins = minutes % 60
  
  return (
    <div style={{
      border: `1px solid rgba(249, 249, 249, 0.2)`,
      borderRadius: 12,
      paddingTop: 1,
      paddingBottom: 1,
      paddingLeft: 4,
      paddingRight: 6,
      display: "flex",
      alignItems: "center"
    }}>
      <div style={{
        display: "flex",
        alignItems: "center",
      }}>
        <DurationIcon16Active />
        <Typography style={{
          ...typography.small.medium,
          marginLeft: 2,
          color: palette.grayscale["100"]
        }}>{zeroPadNumber(hrs, 2)}:{zeroPadNumber(mins, 2)}h</Typography>
      </div>
    </div>
  )
}

export default DurationBadgeActive;
