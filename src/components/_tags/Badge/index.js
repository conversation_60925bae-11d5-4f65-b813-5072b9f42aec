import React from 'react';
import { Typography } from "@material-ui/core";
import typography from "../../../../styles/typography";
import palette from "../../../../styles/palette";

const Badge = ({ icon, quantity, label, variant = "FILL", color = "GREY_300_BG", extraPadding }) => {
  let paddingLeft = 8, paddingRight = 8;
  const background = {
    GREY_100_BG: palette.grayscale["250"],
    GREY_300_BG: palette.grayscale["500"],
    ACTION: palette.primary["500"]
  }
  
  const labelColor = {
    GREY_100_BG: palette.grayscale["800"],
    GREY_300_BG: palette.grayscale["100"],
    ACTION: palette.grayscale["100"]
  }
  
  return (
    <div style={{ paddingLeft, paddingRight, background: background[color], display: "inline-block", borderRadius: 12, paddingTop: 2, paddingBottom: 2 }}>
      <Typography style={{ ...typography.extraSmall.medium, color: labelColor[color], lineHeight: "16px" }}>
        <span style={{ marginLeft: !!icon ? 4 : 0 }}>{quantity}</span>
        {label && <span style={{ marginLeft: (!!quantity || !!icon) ? 4 : 0 }}>{label}</span>}
      </Typography>
    </div>
  )
}

export default Badge;
