import React from 'react';
import Snackbar from '@material-ui/core/Snackbar';
import IconButton from '@material-ui/core/IconButton';
import { useDispatch, useSelector } from 'react-redux';
import { uiSelectors } from '../../../redux/selectors';
// eslint-disable-next-line import/named
import { withTranslation } from '../../../i18n';
import { appActions } from '../../../redux/actions';
import useStyles, { useSnackbarContentStyles } from './styles';
import isEmpty from "../../utilities/isEmpty";
import {CheckIcon20Green, CloseIcon20Gray, WarningIcon20Red} from "../../utilities/icons";
import typography from "../../../styles/typography";
import palette from "../../../styles/palette";
import {Typography} from "@material-ui/core";

const Notification = ({ t, autoHideDuration = 4000 }) => {
  const dispatch = useDispatch();
  const { msgKey, msgVars, severity } = useSelector(uiSelectors.getNotification);

  const classes = useStyles();
  const snackbarContentClasses = useSnackbarContentStyles({ severity });
  
  const iconsBySeverity = {
    "success": <CheckIcon20Green />,
    "error": <WarningIcon20Red />
  }
  
  const close = () => {
    dispatch(appActions.closeNotification());
  };

  return (
    <Snackbar
      open={!!msgKey}
      autoHideDuration={autoHideDuration}
      onClose={close}
      anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      classes={{
        root: classes.snackbarRoot,
        anchorOriginTopCenter: classes.anchorOriginBottomRight
      }}
      ContentProps={{
        classes: snackbarContentClasses
      }}
    >
      <div style={{ padding: 12, background: palette.grayscale.black, borderRadius: 12, display: "flex", alignItems: "center", minHeight: 44 }}>
        <div style={{ paddingRight: 6, display: "flex" }}>
          {severity && iconsBySeverity[severity]}
        </div>
        <Typography style={{ ...typography.body.medium, color: palette.grayscale["100"] }}>
          {isEmpty(msgVars) ? t(msgKey) : t(msgKey, msgVars)}
        </Typography>
        <IconButton size="small" aria-label="close" color="inherit" onClick={close} style={{ padding: 0, paddingLeft: 24 }}>
          <CloseIcon20Gray />
        </IconButton>
      </div>
    </Snackbar>
  );
};

export default withTranslation('common')(Notification);
