import { makeStyles } from '@material-ui/core/styles';
import { colors, severityColors } from '../../../styles/theme';

const useStyles = makeStyles(() => ({
  snackbarRoot: {
    zIndex: 1900,
    "& > div": {
      minHeight: 0,
    }
  },
  anchorOriginBottomRight: {
    bottom: 4
  }
}));

export const useSnackbarContentStyles = makeStyles(() => ({
  root: {
    boxShadow: 'none',
    flexGrow: 0,
    background: ({ severity }) => severityColors[severity] || colors.leviee.main.white,
    display: 'flex',
    padding: 12,
    borderRadius: 12,
    minHeight: 44
  },
  message: {
    flex: 1,
    padding: 0,
    
  }
}));

export default useStyles;
