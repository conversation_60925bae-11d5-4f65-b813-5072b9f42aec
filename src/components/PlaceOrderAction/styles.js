import { makeStyles } from '@material-ui/core/styles';
import { fade } from '@material-ui/core';
import { colors } from '../../../styles/theme';

const useStyles = makeStyles((theme) => ({
  container: {
    // padding: theme.spacing(2, 2, 2, 2),
    width: '100%',
  },
  layout: {

  },
  description: {
    padding: theme.spacing(0, '4px', '16px', '4px'),
  },
  buttonContainer: {
    color: theme.palette.common.white,
    background: theme.palette.primary.main,
    height: 48,
    width: '100%',
    paddingLeft: 16,
    paddingRight: 20,
    '&&': {
      background: colors.leviee.secondary.yellow,
      '&:hover': {
        background: colors.leviee.secondary.yellow
      },
      '&:active': {
        background: colors.leviee.secondary.yellow
      }
    }
  },
  buttonLayout: {
    width: '100%',
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center'
  },
  countLabel: {
    marginLeft: 8,
    color: fade(colors.leviee.main.white, 0.72),
    flex: '1'
  },
  sidebar: {
    display: 'flex',
    alignItems: 'center'
  },
  divider: {
    borderLeft: `1px solid ${fade(colors.leviee.main.white, 0.12)}`,
    width: 1,
    height: 48
  },
  priceLabel: {
    paddingLeft: 20
  }
}));

export default useStyles;
