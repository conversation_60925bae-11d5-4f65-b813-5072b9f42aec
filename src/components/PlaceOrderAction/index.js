import React from 'react';
import { MainButton } from '../Buttons';
import { withTranslation } from '../../../i18n';
import useStyles from './styles';
import { googleTags } from '../../../gtm';
import typography from "../../../styles/typography";
import Typography from "@material-ui/core/Typography";
import palette from "../../../styles/palette";
import ButtonBase from "@material-ui/core/ButtonBase";
import {fade} from "@material-ui/core";

const PlaceOrderAction = ({ t, count, amount = 0, description, ...otherProps }) => {
  const classes = useStyles();

  return (
    <div className={classes.container}>
      <div className={classes.layout}>
        <div className={classes.description}>
          <Typography style={{ ...typography.body.regular }}>
            {description}
          </Typography>
        </div>
        <ButtonBase className={classes.buttonContainer} {...otherProps} style={{
          paddingLeft: 24,
          paddingRight: 24,
          background: palette.primary["500"],
          borderRadius: 12,
          width: "100%"
        }} id={googleTags.dineOut.orderItemsBtn.id}
        >
          <div className={classes.buttonLayout}>
            <div>
              <Typography style={{ ...typography.body.medium, color: palette.grayscale.white, display: "inline-block" }}>
                {t('order-current-order-order-items-modal-order-btn-label')}
              </Typography>
              <span style={{ ...typography.body.medium, color: fade(palette.grayscale.white, 0.72), }} className={classes.countLabel}>
                {t('order-current-order-order-items-modal-order-quantity-btn-label', { count })}
              </span>
            </div>
            <div className={classes.sidebar}>
              <div className={classes.divider} />
              <div className={classes.priceLabel} style={{ ...typography.body.medium, color: palette.grayscale.white }}>
                {`${amount.toFixed(2)} €`}
              </div>
            </div>
          </div>
        </ButtonBase>
      </div>
    </div>
  );
};

export default withTranslation('common')(PlaceOrderAction);
