import { makeStyles } from '@material-ui/core/styles';

const useStyles = makeStyles((theme) => ({
  absoluteCenter: {
    position: 'fixed',
    alignItems: 'center',
    justifyContent: 'center',
    left: 0,
    right: 0,
    bottom: 0,
    top: 0,
    zIndex: 1401,
    display: 'flex',
    flexDirection: 'column'
  },
  adjustTop: {
    top: 94,
    bottom: 'unset'
  },
  backdropRoot: {
    zIndex: 1400,
    color: '#333',
    background: 'white',
    display: 'flex',
    flexDirection: 'column'
  },
  weChatInstructions: {
    paddingTop: theme.spacing(3)
  },
  weChatContent: {
    paddingLeft: theme.spacing(2),
    paddingRight: theme.spacing(2),
  },
  weChatArrow: {
    transform: 'translate(125px, -20px) rotate(165deg)',
    maxWidth: 80,
    position: 'relative',
    right: 0
  },
  customItem: {
    paddingLeft: theme.spacing(7)
  },
  weChatImg: {
    marginTop: theme.spacing(2)
  },
  weChatOS: {
    fontWeight: 500
  }
}));

export default useStyles;
