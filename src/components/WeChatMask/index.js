import React from 'react';
import { Avatar, Backdrop, List, ListItem, ListItemAvatar, ListItemText } from '@material-ui/core';
import Container from '@material-ui/core/Container';
import clsx from 'clsx';
import { Paragraph, Title } from '../Text';
import useStyles from './styles';
import { getMobileOS } from '../../utilities/isMobile';

const WeChatMask = () => {
  const classes = useStyles();

  const mobileOS = getMobileOS();
  const ios = mobileOS === 'ios';
  const android = mobileOS === 'android';

  const overrideBackdropInlineStyles = {
    opacity: 0.6
  };

  return (
    <Container maxWidth="sm">
      <Backdrop open classes={{ root: classes.backdropRoot }} style={overrideBackdropInlineStyles} />
      <div className={clsx(classes.absoluteCenter, classes.weChatContent, { [classes.adjustTop]: ios || android })}>
        <img src="/assets/wechat/arrow_26.svg" alt="wechat-arrow-browser" className={classes.weChatArrow} />
        <Title variant="h3" gutterBottom align="center">
          欢迎使用Leviee扫码点餐
        </Title>
        <Paragraph variant="body2" gutterBottom align="center">
          为了您有一个更流畅的点餐体验，请在浏览器中打开本页
        </Paragraph>
        <div className={classes.weChatInstructions}>
          <List dense>
            <ListItem>
              <ListItemAvatar>
                <Avatar>1</Avatar>
              </ListItemAvatar>
              <ListItemText
                primary="请点击右上方箭头指引处[...]按钮"
              />
            </ListItem>
            <ListItem>
              <ListItemAvatar>
                <Avatar>2</Avatar>
              </ListItemAvatar>
              <ListItemText
                primary="然后请选择在浏览器中打开"
              />
            </ListItem>
            <ListItem>
              <ListItemText className={classes.customItem}>
                {!android && !ios && (
                  <div>
                    <img src="/assets/wechat/iphone.png" alt="wechat-iphone" className={classes.weChatImg} />
                    <Paragraph variant="caption" component="div" gutterBottom className={classes.weChatOS}>
                      苹果设备
                    </Paragraph>
                    <img src="/assets/wechat/android.png" alt="wechat-android" className={classes.weChatImg} />
                    <Paragraph variant="caption" component="div" gutterBottom className={classes.weChatOS}>
                      安卓设备
                    </Paragraph>
                  </div>
                )}
                {android && <img src="/assets/wechat/android.png" alt="wechat-android" className={classes.weChatImg} />}
                {ios && <img src="/assets/wechat/iphone.png" alt="wechat-iphone" className={classes.weChatImg} />}
              </ListItemText>
            </ListItem>
          </List>
        </div>
      </div>
    </Container>
  );
};

export default WeChatMask;
