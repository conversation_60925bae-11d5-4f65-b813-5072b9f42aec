import React, { useEffect } from 'react';
import Container from '@material-ui/core/Container';
import Drawer from 'react-drag-drawer';
import { useDispatch, useSelector } from 'react-redux';
import clsx from 'clsx';
import isEmpty from '../../utilities/isEmpty';
import ActivityBar from '../ActivityBar';
import { IconButton } from '../Buttons';
import {
  ArrowBackIcon, ReceiptCompletedIcon, ReceiptIcon,
  ReceiptInProgressIcon
} from '../Icons';
import ListItem from '../ListItem';
import {orderSelectors, uiSelectors} from '../../../redux/selectors';
import { Paragraph, SmallParagraph, Title } from '../Text';
import AvatarItem from '../AvatarItem';
import OrderItem from '../OrderItem';
// eslint-disable-next-line import/named
import { withTranslation } from '../../../i18n';
import { DisplayTitleContainer, TitleContainer } from '../Containers';
import { orderActions } from '../../../redux/actions';
import {Skeleton} from "@material-ui/lab";
import MenuItem from "../MenuItem";
import useStyles from './styles';

const OrderItemsCategory = ({ icon, text, amount = 0, status }) => {
  const classes = useStyles();

  return (
    <div className={classes.orderItemsCategoryWrapper}>
      <div className={classes.orderItemsCategoryLayout}>
        <ListItem icon={icon} label={text} labelStyles={clsx(classes.orderItemsCategoryLabel, classes[status])} />
        <div className={clsx(classes.orderItemsCategoryAmount, classes[status])}>
          <Paragraph>{`${amount.toFixed(2)}€`}</Paragraph>
        </div>
      </div>
    </div>
  );
};

const CurrentOrderDrawer = ({ t, open, onClose, onRemove }) => {
  const classes = useStyles();
  const dispatch = useDispatch();
  const { loading = false } = useSelector(uiSelectors.getCurrentOrder);
  const { order = {} } = useSelector(orderSelectors.getOrder);
  const resolvedAmount = (order.totalDue || 0).toFixed(2);
  const { summary = {} } = useSelector(orderSelectors.getOrderSummary);
  const { receipts = [] } = summary;
  const { participant = {} } = useSelector(orderSelectors.getParticipant);
  const { customerId: currentCustomerId } = (participant ?? {});

  useEffect(() => {
    if (!isEmpty(order)) {
      if (order.type === 'DINE_IN') {
        dispatch(orderActions.pullOrderSummary(order.id));
      } else {
        dispatch(orderActions.getOrderSummary(order.id));
      }
    }
    return () => {
      dispatch(orderActions.stopPollOrderSummary());
    };
  }, []);

  let customerReceipts = receipts.filter((r) => !!r.customer);
  if (!currentCustomerId && (order.type === 'PICKUP' || order.type === 'DELIVERY')) {
    customerReceipts = receipts.filter((r) => !r.offline);
  }

  const waiterReceipt = receipts.find((i) => isEmpty(i.customer)); // && !!i.offline);

  const canRemove = (customer, itemId) => {
    // if current customer owns the order item
    if (!isEmpty(customer)) {
      if (currentCustomerId === customer.id) {
        return () => onRemove(itemId);
      }
    }

    // if current user has no customer id and order item has no customer id
    if (!currentCustomerId) {
      if (isEmpty(customer)) {
        return () => onRemove(itemId);
      }
    }

    return null;
  };

  const canOrder = (item) => {
    if (order.type === 'DINE_IN' && !isEmpty(item) && item.ongoing && currentCustomerId === order.customerId) {
      return () => dispatch(orderActions.setItem(item));
    }
    return null;
  };

  const canViewMenuItem = (item) => {
    if (order.type === 'DINE_IN' && !isEmpty(item) && item.ongoing) {
      return () => dispatch(orderActions.setItem(item));
    }
    return null;
  };
  
  const getNestedModificationTime = (i) => {
    if (isEmpty(i)) {
      return null;
    }
    
    if (i.ongoing && !isEmpty(i.nestedOrderItems)) {
      const { nestedOrderItems = [] } = i;
      const customerNestedOrderItems = nestedOrderItems.filter(i => !i.offline)
      
      if (isEmpty(customerNestedOrderItems)) {
        return null
      }
      
      return customerNestedOrderItems[0].modificationTime
    }
    
    return null;
  }

  return (
    <Drawer
      open={open}
      onRequestClose={onClose}
      modalElementClass={classes.drawerModal}
      containerElementClass={classes.drawerContainer}
    >
      <ActivityBar
        title={t('activity-title-current-order')}
        color="transparent"
        back={(
          <IconButton component="a" edge="start" color="inherit" aria-label="menu" onClick={onClose}>
            <ArrowBackIcon />
          </IconButton>
        )}
      />
      <div className={classes.modalWrapper}>
        {loading && (
          <Container>
            <TitleContainer>
              <Skeleton width="200px" height="18px" style={{ transform: 'none' }}>
                <Title weight="medium">
                  -----
                </Title>
              </Skeleton>
            </TitleContainer>
            <MenuItem loading />
          </Container>
        )}
        {customerReceipts.map(({ confirmed, unconfirmed, customer = {} }, index) => (
          <div key={!isEmpty(customer) ? customer.id : 'guest'}>
            <Container>
              {!isEmpty(customer) && (customer.firstName || customer.lastName) && (
                <AvatarItem
                  firstName={customer.firstName ?? ''}
                  lastName={customer.lastName ?? ''}
                />
              )}
              {!isEmpty(unconfirmed) && (
                <>
                  <OrderItemsCategory
                    icon={<ReceiptInProgressIcon />}
                    text={t('order-current-order-drawer-unconfirmed-category-label')}
                    amount={unconfirmed.total}
                    status="inProgress"
                  />
                  <div className={classes.orderItemWrapper}>
                    {unconfirmed.items
                      .map((i) => (
                        <OrderItem
                          key={i.id}
                          unitPrice={i.unitPrice}
                          name={i.name}
                          qtd={i.qtd}
                          onRemove={canRemove(customer, i.id)}
                          {...i}
                        />
                      ))}
                  </div>
                </>
              )}
              {!isEmpty(confirmed) && (
                <>
                  <OrderItemsCategory
                    icon={<ReceiptCompletedIcon />}
                    text={t('order-current-order-drawer-confirmed-category-label')}
                    amount={confirmed.total}
                    status="done"
                  />
                  <div className={classes.orderItemWrapper}>
                    {confirmed.items
                      .map((i) => (
                        <OrderItem
                          key={i.id}
                          unitPrice={i.unitPrice}
                          name={i.name}
                          qtd={i.qtd}
                          {...i}
                        />
                      ))}
                  </div>
                  <div className={classes.userTotal}>
                    <OrderItemsCategory
                      icon={<ReceiptIcon />}
                      text={t('order-current-order-drawer-users-total-label', { name: customer.firstName })}
                      amount={confirmed ? confirmed.total : 0}
                    />
                  </div>
                </>
              )}
            </Container>
            {receipts.length - 1 > index && <TitleContainer divider withText />}
          </div>
        ))}
        {!isEmpty(waiterReceipt) && (order.type === 'DINE_IN') && (
          <div className={classes.waiterReceipt}>
            <Container>
              {!isEmpty(waiterReceipt.unconfirmed) && (
                <>
                  <OrderItemsCategory
                    icon={<ReceiptInProgressIcon />}
                    text={t('order-current-order-drawer-unconfirmed-category-label')}
                    amount={waiterReceipt.unconfirmed.total}
                    status="inProgress"
                  />
                  <div className={classes.orderItemWrapper}>
                    {waiterReceipt.unconfirmed.items
                      .map((i) => (
                        <OrderItem
                          key={i.id}
                          unitPrice={i.unitPrice}
                          name={i.name}
                          qtd={i.qtd}
                          {...i}
                        />
                      ))}
                  </div>
                </>
              )}
              {!isEmpty(waiterReceipt.confirmed) && (
                <>
                  <OrderItemsCategory
                    icon={<ReceiptCompletedIcon />}
                    text={t('order-current-order-drawer-confirmed-category-label')}
                    amount={waiterReceipt.confirmed.total}
                    status="done"
                  />
                  <div className={classes.orderItemWrapper}>
                    {waiterReceipt.confirmed.items
                      .map((i) => (
                        <>
                          <OrderItem
                            key={i.id}
                            unitPrice={i.unitPrice}
                            name={i.name}
                            qtd={i.qtd}
                            {...i}
                            onOrderOngoing={canOrder(i)}
                            onViewMenuItem={canViewMenuItem(i)}
                            nestedModificationTime={() => getNestedModificationTime(i)}
                          />
                          {i.ongoing && !isEmpty(i.nestedOrderItems) && i.nestedOrderItems.map((nested, index) => (
                            <OrderItem
                              index={(i.nestedOrderItems).length - index}
                              key={nested.id}
                              name={nested.name}
                              qtd={nested.qtd}
                              {...nested}
                            />
                          ))}
                        </>
                      ))}
                  </div>
                </>
              )}
            </Container>
          </div>
        )}
        {(resolvedAmount > 0) && (
          <>
            <TitleContainer withText divider>
              <Container>
                <div className={classes.totalRow}>
                  <Title weight="medium">
                    {t('pickup-success-order-summary-total-label')}
                  </Title>
                  <Title weight="medium">
                    {`${resolvedAmount}€`}
                  </Title>
                </div>
              </Container>
            </TitleContainer>
            <DisplayTitleContainer>
              <Container>
                <div className={classes.vatDisclaimer}>
                  <SmallParagraph>
                    {t('order-summary-vat-disclaimer-label')}
                  </SmallParagraph>
                </div>
              </Container>
            </DisplayTitleContainer>
          </>
        )}
      </div>
    </Drawer>
  );
};

export default withTranslation('common')(CurrentOrderDrawer);
