import { makeStyles } from '@material-ui/core/styles';
import { fade } from '@material-ui/core';
import { colors, drawerModalStyle } from '../../../styles/theme';

const useStyles = makeStyles((theme) => ({
  orderItemsCategoryWrapper: {
    marginTop: 16
  },
  orderItemsCategoryLayout: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center'
  },
  inProgress: {
    color: colors.leviee.secondary.yellow
  },
  done: {
    color: colors.leviee.main.green
  },
  orderItemsCategoryLabel: {},
  orderItemsCategoryAmount: {},
  drawerModal: {
    background: theme.palette.common.white,
    position: 'absolute',
    top: 200,
    width: '100%',
    maxWidth: '700px',
    borderTopLeftRadius: 8,
    borderTopRightRadius: 8,
    minHeight: '100%',
    bottom: 0
  },
  drawerContainer: {
    '&&': {
      ...drawerModalStyle
    }
  },
  modalWrapper: {
    marginTop: theme.spacing(4),
    background: theme.palette.common.white,
    paddingBottom: 180
  },
  orderItemWrapper: {
    marginTop: 8,
    '& > div:not(:first-child)': {
      marginTop: 8
    }
  },
  userTotal: {
    marginTop: theme.spacing(3),
    marginBottom: theme.spacing(2)
  },
  totalRow: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between'
  },
  vatDisclaimer: {
    color: fade(colors.leviee.main.dark, 0.48)
  }
}));

export default useStyles;
