import React from 'react';
import { LazyLoadImage } from 'react-lazy-load-image-component';
import ButtonBase from '@material-ui/core/ButtonBase';
import getConfig from 'next/config';
import Skeleton from '@material-ui/lab/Skeleton';
import { Paragraph } from '../Text';
import isEmpty from '../../utilities/isEmpty';
// eslint-disable-next-line import/named
import { withTranslation } from '../../../i18n';
import useStyles from './styles';
import typography from "../../../styles/typography";
import Typography from "@material-ui/core/Typography";
import Badge from "../_tags/Badge";

const { publicRuntimeConfig } = getConfig();

const defaultImg = `${publicRuntimeConfig.basePath}/assets/illustrations/menu-item-image.png`;

const MenuItem = ({
  t, view, thumbnailUrl, numeration, name = '', volume, unitPrice, salePrice, action, onClick, scrollPosition,
  options, description = '', tags = [], disabled, loading
}) => {
  const classes = useStyles({ view });
  unitPrice = (unitPrice || 0).toFixed(Number.isInteger(unitPrice) ? 0 : 2);
  salePrice = salePrice && salePrice.toFixed(Number.isInteger(salePrice) ? 0 : 2);

  const sliceText = (val = '', max) => (val ? `${val.substring(0, max - 1)}${val.length > max ? '..' : ''}` : '');

  name = `${numeration ? `${numeration} ` : ''}${name}`;
  description = `${volume ? `${volume} ` : ''}${description || ''}`;

  return (
    <ButtonBase onClick={onClick} className={classes.buttonWrapper} disableRipple disabled={disabled}>
      <div className={classes.container}>
        <div>
          <div className={classes.layout}>
            <div className={classes.imgWrapper}>
              {loading ? (
                <Skeleton width="92px" height="92px" style={{ transform: 'none' }} />
              ) : (
                <LazyLoadImage
                  alt={name}
                  src={thumbnailUrl || "https://storage.googleapis.com/leviee_public/allO/allo-eat-menu-item-img-placeholder-dark.png"}
                  // Make sure to pass down the scrollPosition,
                  // this will be used by the component to know
                  // whether it must track the scroll position or not
                  scrollPosition={scrollPosition}
                  // placeholderSrc="https://storage.googleapis.com/leviee_public/allO/allo-eat-menu-item-img-placeholder-dark.png"
                  className={classes.img}
                  onError={(e) => {
                    e.target.onerror = null;
                    e.target.src = "https://storage.googleapis.com/leviee_public/allO/allo-eat-menu-item-img-placeholder-dark.png";
                  }}
                />
              )}
              {!isEmpty(tags) && (
                <div className={classes.tagsWrapper}>
                  <div className={classes.tags}>
                    {tags.slice(0, 2).map((tag) => (
                      <div key={tag.id} className={classes.tag}>
                        <img className={classes.tagImg} src={`${publicRuntimeConfig.basePath}/icons/tags/${tag.identifier}.svg`} alt={tag.identifier} />
                      </div>
                    ))}
                    {tags.length > 2 && (
                      <div className={classes.tag}>
                        <Paragraph>{`+${tags.length - 2}`}</Paragraph>
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
            <div className={classes.text}>
              <div className={classes.name}>
                {loading ? (
                  <>
                    <Skeleton width="200px">
                      <Typography style={{ ...typography.body.medium }}>------</Typography>
                    </Skeleton>
                    <Skeleton width="100px">
                      <Typography style={{ ...typography.body.medium }}>------</Typography>
                    </Skeleton>
                  </>
                ) : (
                  <Typography style={{ ...typography.body.medium }}>{sliceText(name, 28)}</Typography>
                )}
              </div>
              {!loading && (
                <div className={classes.flex}>
                  <div className={classes.secondaries}>
                    {description && (
                      <div className={classes.secondary}>
                        <div className={classes.description}>
                          <Typography style={{ ...typography.body.regular, color: "inherit" }}>{sliceText(description, action ? 45 : 52)}</Typography>
                        </div>
                      </div>
                    )}
                    <div className={classes.secondary}>
                      <div className={classes.price}>
                        <Typography style={{ ...typography.body.medium, color: "inherit" }}>{`${!isEmpty(options) ? `${t('menu-item-price-from')} ` : ''}${salePrice || unitPrice}€`}</Typography>
                      </div>
                      {salePrice && (
                        <div className={classes.regularPrice}>
                          <Typography style={{ ...typography.body.medium, color: "inherit" }}>{`${unitPrice} €`}</Typography>
                        </div>
                      )}
                    </div>
                  </div>
                  <div className={classes.action}>
                    {disabled ? (
                      <div className={classes.disabledWrapper}>
                        <div className={classes.disabled}>
                          <Badge color="GREY_100_BG" label={t('common-sold-out')} />
                        </div>
                      </div>
                    ) : action}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </ButtonBase>
  );
};

export default withTranslation('common')(MenuItem);
