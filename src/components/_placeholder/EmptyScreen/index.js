import React from "react";
import { withTranslation } from '../../../../i18n';
import { ButtonBase, Typography } from "@material-ui/core";
import typography from "../../../../styles/typography";
import palette from "../../../../styles/palette";
import isEmpty from "../../../utilities/isEmpty";


const EmptyScreen = ({ t, icon, titleI18nKey, descriptionI18nKey, descriptionI18nVars, action = {} }) => {
  
  return (
    <div style={{ display: "flex", alignItems: "center", justifyContent: "center", height: "100%" }}>
      <div style={{ display: "flex", flexDirection: "column", alignItems: "center", maxWidth: 350, textAlign: "center" }}>
        {icon && (
          <div style={{ marginBottom: 20 }}>
            {icon}
          </div>
        )}
        <Typography style={{ ...typography.body.medium }}>{t(titleI18nKey)}</Typography>
        {descriptionI18nKey && (
          <Typography style={{ ...typography.body.regular, color: palette.grayscale["600"], marginTop: 4 }}>
            {t(descriptionI18nKey, descriptionI18nVars)}
          </Typography>
        )}
        {!isEmpty(action) && (
          <ButtonBase
            disableRipple
            disableTouchRipple
            onClick={action.onClick}
            style={{
              paddingTop: 12,
              paddingBottom: 12,
              paddingLeft: 24,
              paddingRight: 24,
              borderRadius: 12,
              background: palette.primary["500"],
              marginTop: 24
            }}
          >
            <Typography style={{ ...typography.body.medium, color: palette.grayscale["100"] }}>
              {t(action.i18nKey)}
            </Typography>
          </ButtonBase>
        )}
      </div>
    </div>
  )
}

export default withTranslation('common')(EmptyScreen);
