import { makeStyles } from '@material-ui/core/styles';
import { colors } from '../../../styles/theme';
import palette from "../../../styles/palette";
import shadows from "../../../styles/shadows";

const isCard = (v) => v === 'CARD';

const useStyles = makeStyles((theme) => ({
  buttonWrapper: {
    width: '100%',
    justifyContent: 'normal',
    textAlign: 'left',
    display: 'block',
    boxSizing: 'border-box',
    '&:disabled': {
      opacity: 0.6
    },
    background: palette.grayscale["100"],
    ...shadows.base,
    borderRadius: 12,
    paddingLeft: 12,
    paddingRight: 12,
  },
  container: {
    padding: theme.spacing(2, 0, 2, 0),
  },
  layout: {
    display: 'flex',
    flexDirection: ({ view }) => (isCard(view) ? 'column' : 'row'),
    // minHeight: 92,
    width: '100%',
  },
  imgWrapper: {
    position: 'relative',
    marginRight: ({ view }) => (isCard(view) ? 0 : theme.spacing(2))
  },
  tagsWrapper: {
    position: 'absolute',
    bottom: ({ view }) => (isCard(view) ? 10 : -5),
    left: ({ view }) => (isCard(view) ? 10 : 'auto'),
    width: '100%',
    display: 'flex',
    justifyContent: ({ view }) => (isCard(view) ? 'unset' : 'center')
  },
  img: {
    height: ({ view }) => (isCard(view) ? 140 : 48),
    width: ({ view }) => (isCard(view) ? '100%' : 48),
    minWidth: 48,
    objectFit: 'cover',
    borderRadius: 8
  },
  tags: {
    background: '#fff',
    display: 'flex',
    boxShadow: '0px 2px 4px rgba(47, 38, 34, 0.08)',
    borderRadius: theme.spacing(3),
    padding: theme.spacing('2px', '6px', '2px', '6px')
  },
  tag: {
    display: 'flex', // flex display fixes issue with parent height bigger than contained image,
    lineHeight: '18px', // corresponding to
    '&+&': {
      paddingLeft: theme.spacing(1)
    }
  },
  tagImg: {
    width: 18,
    height: 18
  },
  text: {
    flex: 1,
    display: 'flex',
    flexDirection: 'column',
    marginTop: ({ view }) => (isCard(view) ? theme.spacing(2) : 0),
    // minHeight: 92
  },
  name: {
    marginBottom: theme.spacing(1)
  },
  secondary: {
    display: 'flex',
    flexDirection: 'row',
    '& > p:not(:first-child)': {
      marginLeft: theme.spacing(1)
    },
    '&+&': {
      marginTop: 6
    },
    '&:last-child': {
      flex: 1
    }
  },
  description: {
    color: colors.leviee.greyscale.darkGray,
    whiteSpace: 'pre-line',
    maxHeight: 40,
    overflow: 'hidden'
  },
  regularPrice: {
    color: colors.leviee.greyscale.midGray,
    textDecoration: 'line-through'
  },
  flex: {
    display: 'flex',
    alignItems: 'center',
    flex: 1
  },
  secondaries: {
    alignSelf: 'flex-start'
  },
  action: {
    flex: 1,
    textAlign: 'right',
    alignSelf: 'flex-end'
  },
  disabledWrapper: {
    display: 'flex',
    justifyContent: 'flex-end'
  },
  disabled: {
    padding: '6px 8px',
    borderRadius: 3,
    background: '#FFF2DE',
    border: `1px solid ${colors.leviee.secondary.beige}`,
    display: 'flex',
    whiteSpace: 'nowrap'
  }
}));

export default useStyles;
