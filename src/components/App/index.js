import React, { useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { appActions } from '../../../redux/actions';
import {useRouter} from "next/router";
import {excludeFooterRouteOptions, excludeInitRouteOptions} from "../../const";
import Footer from "../Footer";

const App = ({ children }) => {
  const dispatch = useDispatch();
  const router = useRouter();
  const { pathname } = router;

  useEffect(() => {
    if (excludeInitRouteOptions.indexOf(pathname) === -1) {
      console.log('[INFO] Preparing application state');
      dispatch(appActions.init());
    }
  }, []);

  return (
    <main>
      {children}
      {excludeFooterRouteOptions.indexOf(pathname) === -1 && (
        <Footer />
      )}
    </main>
  );
};

export default App;
