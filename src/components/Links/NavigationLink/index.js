import React from 'react';
import Link from 'next/link';
import useStyles from './styles';

const NavigationLink = (props) => {
  const classes = useStyles(props);
  const { children, linkProps = {}, anchorProps = {} } = props;

  return (
    <Link href="/" shallow passHref {...linkProps}>
      <a className={classes.anchor} {...anchorProps}>
        {children}
      </a>
    </Link>
  );
};

export default NavigationLink;
