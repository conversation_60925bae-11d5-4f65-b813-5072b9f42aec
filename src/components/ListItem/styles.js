import { makeStyles } from '@material-ui/core/styles';

const useStyles = makeStyles((theme) => ({
  container: {
    padding: theme.spacing(1, 0, 1, 0)
  },
  disabled: {
    opacity: 0.4
  },
  layout: {
    display: 'flex',
    flexDirection: 'row',
  },
  icon: {
    marginRight: theme.spacing(1) + 2,
    lineHeight: '1px'
  },
  label: {
    marginTop: 2,
    marginBottom: 2
  }
}));

export default useStyles;
