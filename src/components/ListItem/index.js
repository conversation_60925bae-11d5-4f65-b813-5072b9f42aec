import React from 'react';
import clsx from 'clsx';
import { Paragraph } from '../Text';
import useStyles from './styles';

const ListItem = ({ icon, label, labelComponent, labelStyles, disabled, ...otherProps }) => {
  const classes = useStyles();

  return (
    <div className={clsx(classes.container, { [classes.disabled]: !!disabled })} {...otherProps}>
      <div>
        <div className={classes.layout}>
          {icon && (
            <div className={classes.icon}>
              {icon}
            </div>
          )}
          <div className={clsx(classes.label, { [labelStyles]: labelStyles })}>
            {labelComponent || <Paragraph>{label}</Paragraph>}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ListItem;
