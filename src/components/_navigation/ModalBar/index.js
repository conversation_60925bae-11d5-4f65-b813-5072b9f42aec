import React from "react";
import { withTranslation } from "../../../../i18n";
import { ButtonBase, Typography } from "@material-ui/core";
import typography from "../../../../styles/typography";
import palette from "../../../../styles/palette";
import {CloseIcon24} from "../../Icons";
import useStyles from "./styles";

const ModalBar = ({ t, title, onClose, onDone }) => {
  const classes = useStyles();
  
  return (
    <div>
      <div className={classes.content}>
        <div className={classes.left}>
          {!!onClose && (
            <ButtonBase
              disableRipple
              disableTouchRipple
              onClick={onClose}
              style={{
                padding: 0
              }}>
              <CloseIcon24 />
            </ButtonBase>
          )}
        </div>
        <div className={classes.center}>
          <Typography style={{ ...typography.body.medium, whiteSpace: "nowrap" }}>
            {title}
          </Typography>
        </div>
        <div className={classes.right}>
          {!!onDone && (
            <ButtonBase
              disableRipple
              disableTouchRipple
              onClick={onDone}
              style={{
                padding: 0,
                display: "flex",
                alignItems: "center",
                marginRight: 4
              }}
            >
              <Typography style={{ ...typography.body.medium, color: palette.primary["500"] }}>
                {t('done')}
              </Typography>
            </ButtonBase>
          )}
        </div>
      </div>
    </div>
  )
}

export default withTranslation("common")(ModalBar);
