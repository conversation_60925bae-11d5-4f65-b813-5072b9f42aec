import { makeStyles } from '@material-ui/core/styles';
import { appBarHeight } from "../../../../styles/theme";
import palette from "../../../../styles/palette";

const useStyles = makeStyles(() => ({
  content: {
    height: appBarHeight + 1,
    paddingLeft: 12,
    paddingRight: 12,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    display: "flex",
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    borderBottom: `1px solid ${palette.grayscale.divider}`,
    background: palette.grayscale["100"]
  },
  left: {
    display: 'flex',
    alignItems: "center",
    flexBasis: "30%"
  },
  center: {
    display: 'flex',
    alignItems: "center",
    flexBasis: "40%",
    justifyContent: "center"
  },
  right: {
    display: 'flex',
    justifyContent: 'flex-end',
    alignItems: 'center',
    flexBasis: "30%"
  },
}));

export default useStyles;
