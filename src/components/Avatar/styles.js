import { makeStyles } from '@material-ui/core/styles';
import palette from "../../../styles/palette";
import typography from "../../../styles/typography";

const useStyles = makeStyles(() => ({
  avatarRoot: {
    width: 32,
    height: 32,
    // border: `1px solid ${fade(colors.leviee.main.dark, 0.2)}`,
    ...typography.extraSmall.medium,
    textTransform: 'uppercase',
    backgroundColor: palette.grayscale["500"],
    color: palette.grayscale["100"],
  }
}));

export default useStyles;
