import React from 'react';
import { QRCodeIconLight } from '../Icons';
import { Button } from '../Buttons';
import { withTranslation } from '../../../i18n';
import useStyles from './styles';
import {Typography} from "@material-ui/core";
import palette from "../../../styles/palette";
import typography from "../../../styles/typography";

const InputCodeAction = ({ t, ...otherProps }) => {
  const classes = useStyles();
  delete otherProps.tReady;

  return (
    <Button
      startIcon={<QRCodeIconLight />}
      className={classes.btn}
      {...otherProps}
    >
      <Typography style={{ ...typography.body.medium, color: palette.grayscale["100"] }}>
        {t('scanning-input-qr-code-btn-label')}
      </Typography>
    </Button>
  );
};

export default withTranslation('common')(InputCodeAction);
