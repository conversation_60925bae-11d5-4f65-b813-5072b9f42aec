import React from 'react';
import { withTranslation } from "../../../i18n";
import Typography from "@material-ui/core/Typography";
import palette from "../../../styles/palette";
import typography from "../../../styles/typography";

const TermsDisclaimer = ({ t }) => {
  
  return (
    <div style={{ display: "flex", alignItems: "center", marginBottom: 24 }}>
      <Typography style={{ ...typography.body.medium, color: palette.grayscale["600"], whiteSpace: "break-spaces" }}>
        {t("by-proceeding-you-agree")}
        {` `}
        <a href="https://www.allo.restaurant/privacy-policy" target="_blank" style={{ textDecoration: "none", fontSize: "inherit", whiteSpace: "break-spaces", display: "inline-block" }}>
          <span
            style={{ ...typography.body.medium, color: palette.grayscale["600"], textDecorationLine: "underline", whiteSpace: "break-spaces" }}
          >
            {t("privacy-policy")}
          </span>
        </a>
        {` `}
        {t("and")}
        {` `}
        <a href="https://allo.restaurant/terms" target="_blank" style={{ textDecoration: "none", fontSize: "inherit", whiteSpace: "break-spaces", display: "inline-block" }}>
          <span style={{ ...typography.body.medium, color: palette.grayscale["600"], textDecorationLine: "underline", whiteSpace: "break-spaces" }}>
            {t("terms-and-conditions-to-agree-to")}
          </span>
        </a>
      </Typography>
    </div>
  );
};

export default withTranslation('common')(TermsDisclaimer)
