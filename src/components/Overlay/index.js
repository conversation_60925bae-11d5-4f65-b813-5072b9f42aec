import React from 'react';
import Modal from '@material-ui/core/Modal';
import useStyles from './styles';

const Overlay = ({ open, onClose, children }) => {
  const classes = useStyles();

  return (
    <Modal
      disableEnforceFocus
      disableAutoFocus
      open={open}
      onClose={onClose}
      aria-labelledby="view"
      aria-describedby="view-optimized-overlay"
      style={{ zIndex: 1701 }}
    >
      <main className={classes.overlay}>
        {children}
      </main>
    </Modal>
  );
};

export default Overlay;
