import React from 'react';
import clsx from 'clsx';
import useStyles from './styles';

const BottomPanel = (props) => {
  const classes = useStyles(props);
  const { transparent, children, size, wrapperStyle } = props;

  return (
    <>
      <div className={clsx(classes.spacer, { [classes.largeSpacer]: size === 'l' })} />
      <div className={clsx(classes.wrapper, { [classes.transparent]: transparent })} style={wrapperStyle}>
        {children}
      </div>
    </>
  );
};

export default BottomPanel;
