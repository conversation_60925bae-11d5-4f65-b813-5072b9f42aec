import { makeStyles } from '@material-ui/core/styles';

const useStyles = makeStyles((theme) => ({
  spacer: {
    minHeight: 72
  },
  largeSpacer: {
    minHeight: 180
  },
  wrapper: {
    position: 'fixed',
    bottom: 0,
    left: 0,
    right: 0,
    display: 'flex',
    alignItems: 'center',
    zIndex: ({ zIndex }) => zIndex || '1700',
    justifyContent: 'center',
    background: theme.palette.common.white,
    boxShadow: '0px -1px 3px -1px rgba(4, 23, 47, 0.12)',
    maxWidth: 500,
    margin: '0 auto'
  },
  transparent: {
    boxShadow: 'none',
    background: 'transparent'
  }
}));

export default useStyles;
