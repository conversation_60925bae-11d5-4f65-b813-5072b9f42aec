import React from 'react';
import Masonry from 'react-masonry-css';
import useStyles from './styles';

const MasonryList = ({ items = [] }) => {
  const classes = useStyles();

  return (
    <div className={classes.wrapper}>
      <Masonry
        breakpointCols={2}
        className={classes.masonry}
        columnClassName={classes.masonryColumn}
      >
        {items}
      </Masonry>
    </div>
  );
};

export default MasonryList;
