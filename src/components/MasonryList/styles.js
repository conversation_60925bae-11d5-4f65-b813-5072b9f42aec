import { makeStyles } from '@material-ui/core/styles';

const useStyles = makeStyles((theme) => ({
  wrapper: {},
  masonry: {
    display: 'flex',
    width: 'auto',
    marginLeft: -theme.spacing(1),
    marginRight: -theme.spacing(1),
  },
  masonryColumn: {
    padding: theme.spacing(1),
    backgroundClip: 'padding-box',
    '& > div:not(:last-child)': {
      marginBottom: theme.spacing(2) + 4,
    }
  }
}));

export default useStyles;
