import { makeStyles } from '@material-ui/core/styles';
import { fade } from '@material-ui/core';
import { colors } from '../../../styles/theme';

const useStyles = makeStyles((theme) => ({
  container: {
    paddingLeft: 16,
    paddingRight: 20
  },
  layout: {
    width: '100%',
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center'
  },
  extend: {
    flex: 1,
    textAlign: 'center'
  },
  countLabel: {
    marginLeft: 8,
    color: fade(colors.leviee.main.white, 0.72),
    flex: '1'
  },
  sidebar: {
    display: 'flex',
    alignItems: 'center'
  },
  divider: {
    borderLeft: `1px solid ${fade(colors.leviee.main.white, 0.12)}`,
    width: 1,
    height: 48
  },
  priceLabel: {
    paddingLeft: 20
  }
}));

export default useStyles;
