import React from 'react';
import clsx from 'clsx';
import { withTranslation } from '../../../i18n';
import useStyles from './styles';
import { googleTags } from '../../../gtm';
import palette from "../../../styles/palette";
import {ButtonBase, fade, Typography} from "@material-ui/core";
import typography from "../../../styles/typography";
import {useSelector} from "react-redux";
import {orderSelectors} from "../../../redux/selectors";

const CurrentOrderAction = ({ t, count, amount = 0, orderType, ...otherProps }) => {
  const classes = useStyles();
  delete otherProps.tReady;
  
  const { discount = {} } = useSelector(orderSelectors.getDiscount);
  const { percentage: discountPercentage } = (discount || {})

  return (
    <div style={{
      position: "absolute",
      width: "100%",
      bottom: 24,
      paddingLeft: 16,
      paddingRight: 16
    }}>
      <div style={{
        maxWidth: 400,
        margin: "0 auto",
        paddingTop: 12,
        paddingLeft: 16,
        paddingRight: 16,
        paddingBottom: 12,
        backgroundColor: palette.grayscale["800"],
        borderRadius: 12,
      }}>
        {!!count && (
          <div style={{ paddingBottom: 16 }}>
            <div style={{ display: "flex", alignItems: "center" }}>
              <Typography style={{ ...typography.body.medium, color: palette.grayscale.white }}>
                {t(orderType === "DINE_IN" ? 'send-items-to-kitchen' : 'your-order')}
              </Typography>
              {discountPercentage && (
                <Typography style={{ ...typography.small.medium, padding: '2px 8px', marginLeft: 4, color: palette.grayscale["100"], background: palette.primary["500"], borderRadius: 12 }}>
                  {t('percent-discount', { percent: discountPercentage })}
                </Typography>
              )}
            </div>
            <Typography style={{ ...typography.body.regular, color: palette.transparency.light["80"] }}>
              {t('you-have-items-waiting-in-your-basket')}
            </Typography>
          </div>
        )}
        <div style={{
          borderTop: !!count ? `1px dashed ${palette.transparency.light["20"]}` : null,
          paddingTop: !!count ? 12 : 0,
          display: "flex",
          justifyContent: !!count ? "flex-end" : "space-between",
          alignItems: "center",
          width: "100%"
        }}>
          {!count && (
            <Typography style={{ ...typography.body.medium, color: palette.grayscale.white }}>
              {t('enjoy-your-food')} 😋
            </Typography>
          )}
          <ButtonBase {...otherProps} id={googleTags.dineOut.viewItemsBtn.id} style={{
            height: 48,
            paddingLeft: 24,
            paddingRight: 24,
            background: palette.primary["500"],
            borderRadius: 12,
            width: !!count ? "100%" : null
          }}>
            <div className={classes.layout}>
              <div className={clsx({ [classes.extend]: !count && !amount })}>
                <Typography style={{ ...typography.body.medium, color: palette.grayscale["100"], display: "inline-block" }}>
                  {t('view-your-basket')}
                </Typography>
                {!!count && <span style={{ ...typography.body.regular, color: fade(palette.grayscale["100"], 0.72), }} className={classes.countLabel}>
                  {t('order-see-current-order-items-quantity-btn-label', { count })}
                </span>}
              </div>
              {!!amount && (
                <div className={classes.sidebar}>
                  <div className={classes.divider} />
                  <div className={classes.priceLabel} style={{ ...typography.body.medium, color: palette.grayscale["100"] }}>
                    {`${amount.toFixed(2)} €`}
                  </div>
                </div>
              )}
            </div>
          </ButtonBase>
        </div>
      </div>
    </div>
  );
};

export default withTranslation('common')(CurrentOrderAction);
