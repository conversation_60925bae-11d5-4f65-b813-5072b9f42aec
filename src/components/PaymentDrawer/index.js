import React, { useEffect, useState } from 'react';
import Container from '@material-ui/core/Container';
import Drawer from 'react-drag-drawer';
import { useSelector } from 'react-redux';
import clsx from 'clsx';
import Grid from '@material-ui/core/Grid';
import isEmpty from '../../utilities/isEmpty';
import ActivityBar from '../ActivityBar';
import { IconButton } from '../Buttons';
import {
  ArrowBackIcon, CardIllustration56,
  CardIllustrationGrayScale56, CashIllustration56,
  CashIllustrationGrayScale56, InAppIllustration56,
  InAppIllustrationGrayScale56,
  MyShareIllustration56,
  MyShareIllustrationGrayScale56, PayAllIllustration56,
  PayAllIllustrationGrayScale56,
  ReceiptIcon, SplitIllustration56,
  SplitIllustrationGrayScale56, TipActiveIcon, TipIcon
} from '../Icons';
import ListItem from '../ListItem';
import { orderSelectors } from '../../../redux/selectors';
import { Paragraph, SmallParagraph, Title } from '../Text';
import AvatarItem from '../AvatarItem';
import OrderItem from '../OrderItem';
import { withTranslation } from '../../../i18n';
import { DisplayTitleContainer, TextContainer, TitleContainer } from '../Containers';
import IconCard from '../Cards/IconCard';
import PayAction from '../PayAction';
import { getCheckout } from '../../../redux/api';
import formatNumber from '../../utilities/formatNumber';
import useStyles from './styles';

export const OrderItemsCategory = ({ icon, text, amount = 0, status }) => {
  const classes = useStyles();

  return (
    <div className={classes.orderItemsCategoryWrapper}>
      <div className={classes.orderItemsCategoryLayout}>
        <ListItem icon={icon} label={text} labelStyles={clsx(classes.orderItemsCategoryLabel, classes[status])} />
        <div className={clsx(classes.orderItemsCategoryAmount, classes[status])}>
          <Paragraph>{`${amount.toFixed(2)}€`}</Paragraph>
        </div>
      </div>
    </div>
  );
};

const PaymentDrawer = ({ t, open, onClose }) => {
  const classes = useStyles();
  const { order = {} } = useSelector(orderSelectors.getOrder);
  const { summary = {} } = useSelector(orderSelectors.getOrderSummary);
  const { receipts = [] } = summary;

  const customerReceipts = receipts.filter((r) => !!r.customer);
  const waiterReceipt = receipts.find((i) => isEmpty(i.customer));

  const { participant } = useSelector(orderSelectors.getParticipant);
  const { customerId: currentCustomerId } = participant;

  const [checkout, setCheckout] = useState({});

  const [selectedOption, setSelectedOption] = useState('ALL');
  const [selectedTip, setSelectedTip] = useState('TIP5');
  const [selectedMethod, setSelectedMethod] = useState('CASH');

  const {
    allAmount,
    allAmountTipValue = {},
    ownAmount,
    ownAmountTipValue = {},
    splitAmount,
    splitAmountTipValue = {}
  } = (checkout || {});

  const [customTipDrawer, setCustomTipDrawer] = useState(false);
  const [paymentMethodDrawer, setPaymentMethodDrawer] = useState(false);

  const [completingPayment, setCompletingPayment] = useState(false);

  useEffect(() => {
    getCheckout(order.id).then(({ data }) => setCheckout(data)).catch(() => {});
  }, [order.id]);

  const completePayment = () => {
    setCompletingPayment(true);
  };

  const paymentOptions = [
    {
      id: 'ALL',
      icon: <PayAllIllustrationGrayScale56 />,
      selectedIcon: <PayAllIllustration56 />,
      title: t('common-payment-option-all'),
      description: `${allAmount}€`,
      disabled: false
    }, {
      id: 'OWN',
      icon: <MyShareIllustrationGrayScale56 />,
      selectedIcon: <MyShareIllustration56 />,
      title: t('common-payment-option-own'),
      description: `${formatNumber(ownAmount)}€`,
      disabled: true
    }, {
      id: 'SPLIT',
      icon: <SplitIllustrationGrayScale56 />,
      selectedIcon: <SplitIllustration56 />,
      title: t('common-payment-option-split'),
      description: `${formatNumber(splitAmount || allAmount)}€`,
      disabled: true
    }
  ];

  const { TIP5, TIP10, TIP15, TIP20 } = allAmountTipValue;

  const tipOptions = [
    {
      id: 'TIP5',
      title: '5%',
      description: `${formatNumber(TIP5)}€`
    }, {
      id: 'TIP10',
      title: '10%',
      description: `${formatNumber(TIP10)}€`
    }, {
      id: 'TIP15',
      title: '15%',
      description: `${formatNumber(TIP15)}€`
    }, {
      id: 'TIP20',
      title: '20%',
      description: `${formatNumber(TIP20)}€`
    }
  ];

  const paymentMethods = [
    {
      id: 'CASH',
      icon: <CashIllustrationGrayScale56 />,
      selectedIcon: <CashIllustration56 />,
      title: t('common-payment-method-cash'),
    }, {
      id: 'CARD',
      icon: <CardIllustrationGrayScale56 />,
      selectedIcon: <CardIllustration56 />,
      title: t('common-payment-method-card'),
    }, {
      id: 'APP',
      icon: <InAppIllustrationGrayScale56 />,
      selectedIcon: <InAppIllustration56 />,
      title: t('common-payment-method-app'),
      disabled: true
    }
  ];

  const subTotal = order.totalDue;
  const resolvedSubtotal = formatNumber(subTotal);
  const total = subTotal + (selectedTip ? allAmountTipValue[selectedTip] : 0);
  const resolvedTotal = formatNumber(subTotal);

  return (
    <Drawer
      open={open}
      onRequestClose={onClose}
      modalElementClass={classes.drawerModal}
      containerElementClass={classes.drawerContainer}
    >
      <ActivityBar
        title={t('activity-title-payment')}
        position="sticky"
        back={(
          <IconButton component="a" edge="start" color="inherit" aria-label="menu" onClick={onClose}>
            <ArrowBackIcon />
          </IconButton>
        )}
      />
      <div className={classes.modalWrapper}>
        {/* <div className={classes.paymentOptions}> */}
        {/*  <Container> */}
        {/*    <TextContainer> */}
        {/*      <Title weight="medium"> */}
        {/*        {t('order-payment-drawer-split-bill-title')} */}
        {/*      </Title> */}
        {/*      <div className={classes.description}> */}
        {/*        <Paragraph> */}
        {/*          {t('order-payment-drawer-split-bill-description')} */}
        {/*        </Paragraph> */}
        {/*      </div> */}
        {/*    </TextContainer> */}
        {/*    <div className={classes.iconCardContainer}> */}
        {/*      <Grid container spacing={2} justify="center"> */}
        {/*        {paymentOptions.map(({ id, icon, selectedIcon, title, description, disabled }) => ( */}
        {/*          <Grid item xs={4} key={id}> */}
        {/*            <IconCard */}
        {/*              showBadge={selectedOption === id} */}
        {/*              icon={selectedOption === id ? selectedIcon : icon} */}
        {/*              title={title} */}
        {/*              description={description} */}
        {/*              onClick={() => setSelectedOption(id)} */}
        {/*              disabled={disabled} */}
        {/*            /> */}
        {/*          </Grid> */}
        {/*        ))} */}
        {/*      </Grid> */}
        {/*    </div> */}
        {/*  </Container> */}
        {/* </div> */}
        <div className={classes.receipt}>
          <TitleContainer withText divider={false}>
            <Container>
              <TextContainer>
                <Title weight="medium">
                  {t('order-payment-drawer-order-summary-title')}
                </Title>
                <div className={classes.description}>
                  <Paragraph>
                    {t('order-payment-drawer-order-summary-description')}
                  </Paragraph>
                </div>
              </TextContainer>
            </Container>
          </TitleContainer>
          {customerReceipts.map(({ confirmed, customer = {} }, index) => (
            <div key={customer.id}>
              <Container>
                {!isEmpty(customer) && (
                  <AvatarItem
                    firstName={customer.firstName}
                    lastName={customer.lastName || ''}
                  />
                )}
                {!isEmpty(confirmed) && (
                  <>
                    <div className={classes.orderItemWrapper}>
                      {confirmed.items
                        .map((i) => (
                          <OrderItem
                            unitPrice={i.unitPrice}
                            name={i.name}
                            qtd={i.qtd}
                            {...i}
                          />
                        ))}
                    </div>
                    <div className={classes.userTotal}>
                      <OrderItemsCategory
                        icon={<ReceiptIcon />}
                        text={t('order-current-order-drawer-users-total-label', { name: customer.firstName })}
                        amount={confirmed ? confirmed.total : 0}
                      />
                    </div>
                  </>
                )}
              </Container>
              {receipts.length - 1 > index && <TitleContainer withText />}
            </div>
          ))}
          {!isEmpty(waiterReceipt) && (
            <div>
              <Container>
                {!isEmpty(waiterReceipt.confirmed) && (
                  <>
                    <div className={classes.orderItemWrapper}>
                      {waiterReceipt.confirmed.items
                        .map((i) => (
                          <OrderItem
                            unitPrice={i.unitPrice}
                            name={i.name}
                            qtd={i.qtd}
                            {...i}
                          />
                        ))}
                    </div>
                  </>
                )}
              </Container>
            </div>
          )}
        </div>
        {/* <div className={classes.tip}> */}
        {/*  <TitleContainer withText divider> */}
        {/*    <Container> */}
        {/*      <TextContainer> */}
        {/*        <Title weight="medium"> */}
        {/*          {t('order-payment-drawer-tip-title')} */}
        {/*        </Title> */}
        {/*        <div className={classes.description}> */}
        {/*          <Paragraph> */}
        {/*            {t('order-payment-drawer-tip-description')} */}
        {/*          </Paragraph> */}
        {/*        </div> */}
        {/*      </TextContainer> */}
        {/*      <div className={classes.iconCardContainer}> */}
        {/*        <Grid container spacing={2} justify="center"> */}
        {/*          {tipOptions.map(({ id, title, description }) => ( */}
        {/*            <Grid item xs={3} key={id}> */}
        {/*              <IconCard */}
        {/*                showBadge={id === selectedTip} */}
        {/*                title={title} */}
        {/*                description={description} */}
        {/*                onClick={() => setSelectedTip(id)} */}
        {/*              /> */}
        {/*            </Grid> */}
        {/*          ))} */}
        {/*        </Grid> */}
        {/*      </div> */}
        {/*      <div className={classes.customTipContainer}> */}
        {/*        <div className={classes.customTipBtn}> */}
        {/*          <IconButton edge="start" onClick={() => setSelectedTip(null)} label={t('order-payment-drawer-no-tip-btn-label')}> */}
        {/*            <TipIcon /> */}
        {/*          </IconButton> */}
        {/*        </div> */}
        {/*        {!(selectedTip === 'TIPCUSTOM') && ( */}
        {/*          <div className={classes.customTipBtn}> */}
        {/*            <IconButton edge="start" onClick={() => setCustomTipDrawer(true)} label={t('order-payment-drawer-custom-tip-btn-label')}> */}
        {/*              <TipIcon /> */}
        {/*            </IconButton> */}
        {/*          </div> */}
        {/*        )} */}
        {/*        {(selectedTip === 'TIPCUSTOM') && ( */}
        {/*          <div className={classes.customTipBtn}> */}
        {/*            <IconButton edge="start" disabled onClick={() => setCustomTipDrawer(true)} label={t('order-payment-drawer-current-tip-label', { amount: '3.00' })}> */}
        {/*              <TipActiveIcon /> */}
        {/*            </IconButton> */}
        {/*          </div> */}
        {/*        )} */}
        {/*      </div> */}
        {/*    </Container> */}
        {/*  </TitleContainer> */}
        {/* </div> */}
        {/* <div className={classes.paymentMethods}> */}
        {/*  <TitleContainer withText divider> */}
        {/*    <Container> */}
        {/*      <TextContainer> */}
        {/*        <Title weight="medium"> */}
        {/*          {t('order-payment-drawer-payment-method-title')} */}
        {/*        </Title> */}
        {/*        <div className={classes.description}> */}
        {/*          <Paragraph> */}
        {/*            {t('order-payment-drawer-payment-method-description')} */}
        {/*          </Paragraph> */}
        {/*        </div> */}
        {/*      </TextContainer> */}
        {/*      <div className={classes.iconCardContainer}> */}
        {/*        <Grid container spacing={2} justify="center"> */}
        {/*          {paymentMethods.map(({ id, icon, selectedIcon, title, disabled }) => ( */}
        {/*            <Grid item xs={4} key={id}> */}
        {/*              <IconCard */}
        {/*                showBadge={id === selectedMethod} */}
        {/*                icon={(id === selectedMethod) ? selectedIcon : icon} */}
        {/*                title={title} */}
        {/*                onClick={() => setSelectedMethod(id)} */}
        {/*                disabled={disabled} */}
        {/*              /> */}
        {/*            </Grid> */}
        {/*          ))} */}
        {/*        </Grid> */}
        {/*      </div> */}
        {/*      <div className={classes.paymentMethodContainer}> */}
        {/*        {(selectedMethod === 'APP') && ( */}
        {/*          <div className={classes.addPaymentMethodBtn}> */}
        {/*            <IconButton edge="start" onClick={() => setPaymentMethodDrawer(true)} label={t('payment-add-payment-method-btn-label')}> */}
        {/*              <TipIcon /> */}
        {/*            </IconButton> */}
        {/*          </div> */}
        {/*        )} */}
        {/*      </div> */}
        {/*    </Container> */}
        {/*  </TitleContainer> */}
        {/* </div> */}
        <TitleContainer withText divider>
          <Container>
            {/* <div className={classes.subtotalRow}> */}
            {/*  <Paragraph> */}
            {/*    {t('order-payment-drawer-total-breakdown-subtotal-label')} */}
            {/*  </Paragraph> */}
            {/*  <Paragraph> */}
            {/*    {`${resolvedSubtotal}€`} */}
            {/*  </Paragraph> */}
            {/* </div> */}
            {/* <div className={classes.tipRow}> */}
            {/*  <Paragraph> */}
            {/*    {t('order-payment-drawer-total-breakdown-tip-label')} */}
            {/*  </Paragraph> */}
            {/*  <Paragraph> */}
            {/*    {`${selectedTip ? formatNumber(allAmountTipValue[selectedTip]) : '0.00'}€`} */}
            {/*  </Paragraph> */}
            {/* </div> */}
            <div className={classes.totalRow}>
              <Title weight="medium">
                {t('pickup-success-order-summary-total-label')}
              </Title>
              <Title weight="medium">
                {`${resolvedTotal}€`}
              </Title>
            </div>
          </Container>
        </TitleContainer>
        <DisplayTitleContainer>
          <Container>
            <div className={classes.vatDisclaimer}>
              <SmallParagraph>
                {t('order-summary-vat-disclaimer-label')}
              </SmallParagraph>
            </div>
          </Container>
        </DisplayTitleContainer>
        {/* <div className={classes.payAction}> */}
        {/*  <PayAction */}
        {/*    amount={total} */}
        {/*    loading={completingPayment} */}
        {/*    disabled={completingPayment} */}
        {/*    onClick={completePayment} */}
        {/*  /> */}
        {/* </div> */}
      </div>
    </Drawer>
  );
};

export default withTranslation('common')(PaymentDrawer);
