import { makeStyles } from '@material-ui/core/styles';
import { fade } from '@material-ui/core';
import { colors, drawerModalStyle } from '../../../styles/theme';

const useStyles = makeStyles((theme) => ({
  orderItemsCategoryWrapper: {
    marginTop: 8
  },
  orderItemsCategoryLayout: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center'
  },
  inProgress: {
    color: colors.leviee.secondary.yellow
  },
  done: {
    color: colors.leviee.main.green
  },
  orderItemsCategoryLabel: {},
  orderItemsCategoryAmount: {},
  drawerModal: {
    background: theme.palette.common.white,
    position: 'absolute',
    top: 0,
    width: '100%',
    maxWidth: '700px',
    borderTopLeftRadius: 0,
    borderTopRightRadius: 0,
    minHeight: '100%',
    bottom: 0
  },
  drawerContainer: {
    '&&': {
      ...drawerModalStyle
    }
  },
  modalWrapper: {
    marginTop: theme.spacing(4),
    background: theme.palette.common.white,
    // paddingBottom: theme.spacing(22)
  },
  orderItemWrapper: {
    marginTop: 8,
    '& > div:not(:first-child)': {
      marginTop: 8
    }
  },
  customTipBtn: {
    paddingTop: 10
  },
  addPaymentMethodBtn: {
    paddingTop: 10
  },
  userTotal: {
    marginTop: theme.spacing(3),
    marginBottom: theme.spacing(2)
  },
  subtotalRow: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingTop: 6,
    paddingBottom: 6,
  },
  tipRow: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingTop: 6,
    paddingBottom: 6,
    marginBottom: 10
  },
  totalRow: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between'
  },
  vatDisclaimer: {
    color: fade(colors.leviee.main.dark, 0.48)
  },
  description: {
    color: fade(colors.leviee.main.dark, 0.48),
    marginTop: 2
  },
  payAction: {
    marginTop: theme.spacing(6)
  },
  iconCardContainer: {
    marginTop: 16,
    marginBottom: 16
  }
}));

export default useStyles;
