import React from 'react';
import MuiIconButton from '@material-ui/core/IconButton';
import { withStyles } from '@material-ui/core';
import useStyles from './styles';
import palette from "../../../../styles/palette";
import typography from "../../../../styles/typography";
import Typography from "@material-ui/core/Typography";
import {MinusQuantity20Black, PlusQuantity20Black} from "../../../utilities/icons";

const StyledMuiIconButton = withStyles({
  root: {
    padding: 6,
    borderRadius: 12,
    width: 32,
    height: 32
  },
})(MuiIconButton);

const QuantityButton = (props) => {
  const classes = useStyles();
  const { counter = 0, disabled, onMore, onLess, ...otherProps } = props;

  return (
    <div>
      <div className={classes.layout}>
        {!!counter && (counter > 0) && (
          <>
            <StyledMuiIconButton
              {...otherProps}
              disabled={disabled || counter === 0}
              onClick={onLess}
            >
              <MinusQuantity20Black />
            </StyledMuiIconButton>
            <div className={classes.counter}>
              <Typography style={{ ...typography.body.regular }}>{counter}</Typography>
            </div>
          </>
        )}
        <StyledMuiIconButton
          {...otherProps}
          onClick={onMore}
        >
          <PlusQuantity20Black />
        </StyledMuiIconButton>
      </div>
    </div>
  );
};

export default QuantityButton;
