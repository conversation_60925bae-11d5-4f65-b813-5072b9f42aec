import React from 'react';
import Link from 'next/link';
import Button from '../Button';
import useStyles from './styles';

const LinkButton = ({ href, as, shallow = true, ...otherProps }) => {
  const classes = useStyles();

  return (
    <Link
      href={href}
      as={as}
      shallow={shallow}
      passHref
    >
      <Button
        component="a"
        className={classes.btn}
        {...otherProps}
      />
    </Link>
  );
};

export default LinkButton;
