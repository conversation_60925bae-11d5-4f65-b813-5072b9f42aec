import React from 'react';
import MuiButton from '@material-ui/core/Button';
import { fade, withStyles } from '@material-ui/core';
import CircularProgress from '@material-ui/core/CircularProgress';
import { colors, fontStyles } from '../../../../styles/theme';

const StyledMuiButton = withStyles({
  root: {
    boxShadow: 'none',
    textTransform: 'none',
    fontSize: 15,
    fontWeight: 400,
    padding: ({ normal }) => (normal ? '10px 18px' : '6px 10px'),
    ...fontStyles.paragraphRegular,
    '@media (min-width: 901px)': {
      fontSize: 16,
      fontWeight: 500,
    },
    '&:hover': {
      boxShadow: 'none',
      background: colors.leviee.main.green
    },
    '&:active': {
      boxShadow: 'none',
      background: colors.leviee.main.green
    }
  },
  contained: {
    color: colors.leviee.main.white,
    background: colors.leviee.main.green,
    '&$disabled': {
      background: colors.leviee.greyscale.lightGray,
      color: colors.leviee.greyscale.midGray
    },
    '&:hover': {
      '&$disabled': {
        background: colors.leviee.greyscale.lightGray,
        color: colors.leviee.greyscale.midGray
      },
    },
    '&:active': {
      '&$disabled': {
        background: colors.leviee.greyscale.lightGray,
        color: colors.leviee.greyscale.midGray
      },
    }
  },
  disabled: {},
  text: {
    borderRadius: 3,
    padding: '6px 10px',
    '&:hover': {
      boxShadow: 'none',
      background: colors.leviee.greyscale.lightGray
    },
    '&:active': {
      boxShadow: 'none',
      background: colors.leviee.greyscale.lightGray
    }
  },
  outlined: {
    borderRadius: 3,
    border: `1px solid ${colors.leviee.greyscale.lightGray}`,
    padding: '6px 10px',
    color: 'inherit',
    ...fontStyles.paragraphRegular,
    '&:hover': {
      boxShadow: 'none',
      background: colors.leviee.greyscale.lightGray
    },
    '&:active': {
      boxShadow: 'none',
      background: colors.leviee.greyscale.lightGray
    }
  }
})(MuiButton);

const StyledCircularProgress = withStyles({
  root: {
    color: (props) => (props.disabled ? colors.leviee.main.dark : fade(colors.leviee.main.white, 0.72)),
    marginRight: (props) => (props.disabled ? 8 : 0),
    marginLeft: 6
  },
})(CircularProgress);

const Button = (props) => {
  const { children, disabled, variant, loading, startIcon } = props;
  const propagatedProps = { ...props };
  delete propagatedProps.loading;

  return (
    <StyledMuiButton
      {...propagatedProps}
      variant={variant || 'contained'}
      startIcon={loading ? <StyledCircularProgress size={16} disabled={disabled} /> : startIcon}
    >
      {children}
    </StyledMuiButton>
  );
};

export default Button;
