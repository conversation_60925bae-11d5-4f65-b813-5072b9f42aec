import React from 'react';
import MuiIconButton from '@material-ui/core/IconButton';
import {ButtonBase, withStyles} from '@material-ui/core';
import { colors } from '../../../../styles/theme';
import { PlusIconLight16 } from '../../Icons';
import Paragraph from '../../Text/Paragraph';
import palette from "../../../../styles/palette";
import {PlusQuantity20Black} from "../../../utilities/icons";
import typography from "../../../../styles/typography";
import Typography from "@material-ui/core/Typography";

const CounterButton = (props) => {
  const { counter, onChange, isExtended, ...otherProps } = props;

  return (
    <ButtonBase
      {...otherProps}
      style={{
        padding: 6,
        border: `1px solid ${palette.grayscale["350"]}`,
        borderRadius: 12
        // backgroundColor: !!counter ? palette.grayscale["800"] : null,
        // borderColor: !!counter ? palette.grayscale["800"] : null,
        // color: !!counter ? palette.grayscale["100"] : null
      }}
    >
      <div style={{ display: "flex", lineHeight: 20 }}>
        {!!counter && <Typography style={{ ...typography.body.regular }}>{counter}</Typography>}
        {!counter && <PlusQuantity20Black />}
      </div>
    </ButtonBase>
  );
};

export default CounterButton;
