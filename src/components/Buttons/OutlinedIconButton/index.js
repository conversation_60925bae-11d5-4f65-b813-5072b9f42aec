import React from 'react';
import MuiIconButton from '@material-ui/core/IconButton';
import { withStyles } from '@material-ui/core';
import { colors } from '../../../../styles/theme';

const StyledMuiIconButton = withStyles({
  root: {
    padding: 6,
    border: `1px solid ${colors.leviee.greyscale.gray}`,
    borderRadius: 3,
    width: 32,
    height: 32
  },
})(MuiIconButton);

const OutlinedIcon = (props) => {
  const { counter, onChange, isExtended, icon, ...otherProps } = props;

  return (
    <StyledMuiIconButton
      {...otherProps}
      style={counter ? {
        backgroundColor: colors.leviee.main.dark,
        borderColor: colors.leviee.main.dark,
        color: colors.leviee.main.white
      } : null}
    >
      {icon}
    </StyledMuiIconButton>
  );
};

export default OutlinedIcon;
