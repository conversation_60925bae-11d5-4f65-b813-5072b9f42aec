import React from 'react';
import MuiIconButton from '@material-ui/core/IconButton';
import { withStyles } from '@material-ui/core';
import { colors } from '../../../../styles/theme';
import { CheckIconWhite16 } from '../../Icons';

const StyledMuiIconButton = withStyles({
  root: {
    padding: 6,
    border: `1px solid ${colors.leviee.greyscale.gray}`,
    borderRadius: '100%',
    width: 32,
    height: 32
  },
})(MuiIconButton);

const RadioButton = (props) => {
  const { checked, onChange, isExtended, ...otherProps } = props;

  return (
    <StyledMuiIconButton
      {...otherProps}
      style={checked ? {
        backgroundColor: colors.leviee.main.dark,
        borderColor: colors.leviee.main.dark,
        color: colors.leviee.main.white
      } : null}
    >
      {checked && (
        <CheckIconWhite16 />
      )}
    </StyledMuiIconButton>
  );
};

export default RadioButton;
