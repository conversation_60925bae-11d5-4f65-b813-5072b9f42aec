import React, { useEffect, useState } from 'react';
import { LazyLoadImage } from 'react-lazy-load-image-component';
import ButtonBase from '@material-ui/core/ButtonBase';
import getConfig from 'next/config';
import clsx from 'clsx';
import { Paragraph, Title } from '../Text';
// eslint-disable-next-line import/named
import { withTranslation } from '../../../i18n';
import formatNumber from '../../utilities/formatNumber';
import CheckButton from '../Buttons/CheckButton';
import { TitleContainer } from '../Containers';
import RadioButton from '../Buttons/RadioButton';
import { QuantityButton } from '../Buttons';
import useStyles from './styles';
import byId from '../../utilities/byId';
import ids from '../../utilities/ids';
import isEmpty from '../../utilities/isEmpty';
import preventEventPropagation from '../../utilities/preventEventPropagation';

const { publicRuntimeConfig } = getConfig();

const MenuItemAdditionElement = withTranslation('common')(({
  id, thumbnailUrl, name, numeration, description, volume, unitPrice, onClick, max, multi, qtd, setQtd, limitReached, tags = []
}) => {
  const classes = useStyles();
  unitPrice = `+ ${unitPrice ? formatNumber(unitPrice) : 0}€`;

  // eslint-disable-next-line consistent-return
  const getClickAction = () => {
    if (multi) {
      if (max <= 1) {
        return () => setQtd(id, qtd ? 0 : 1);
      }
    } else {
      return () => setQtd(id, 1);
    }
  };

  // eslint-disable-next-line no-unused-vars
  const clickAction = setQtd ? getClickAction() : onClick;

  const getAction = () => {
    let action = null;
    if (multi) {
      action = max > 1
        ? (
          <QuantityButton
            counter={qtd}
            onMore={(e) => {
              if (limitReached) {
                preventEventPropagation(e);
                return;
              }
              setQtd(id, qtd === max ? qtd : qtd + 1);
              preventEventPropagation(e);
            }}
            onLess={(e) => {
              setQtd(id, qtd - 1);
              preventEventPropagation(e);
            }}
          />
        )
        : (
          <CheckButton
            checked={qtd > 0}
            onClick={(e) => {
              setQtd(id, qtd ? 0 : 1);
              preventEventPropagation(e);
            }}
          />
        );
    } else {
      action = (
        <RadioButton
          checked={qtd > 0}
          onClick={(e) => {
            setQtd(id, 1);
            preventEventPropagation(e);
          }}
        />
      );
    }
    return action;
  };

  const sliceText = (val = '', mx) => (val ? `${val.substring(0, mx - 1)}${val.length > mx ? '..' : ''}` : '');

  name = `${numeration ? `${numeration} ` : ''}${name}`;
  description = `${volume ? `${volume} ` : ''}${description || ''}`;

  return (
    <ButtonBase onClick={onClick} className={classes.buttonWrapper} disableRipple>
      <div className={classes.container}>
        <div>
          <div className={classes.layout}>
            <div className={classes.imgWrapper}>
              <LazyLoadImage
                alt={name}
                src={thumbnailUrl || "https://storage.googleapis.com/leviee_public/allO/allo-eat-menu-item-img-placeholder-dark.png"}
                className={classes.img}
                onError={(e) => {
                  e.target.onerror = null;
                  e.target.src = "https://storage.googleapis.com/leviee_public/allO/allo-eat-menu-item-img-placeholder-dark.png";
                }}
              />
              {!isEmpty(tags) && (
                <div className={classes.tagsWrapper}>
                  <div className={classes.tags}>
                    {tags.slice(0, 1).map((tag) => (
                      <div key={tag.id} className={classes.tag}>
                        <img className={classes.tagImg} src={`${publicRuntimeConfig.basePath}/icons/tags/${tag.identifier}.svg`} alt={tag.identifier} />
                      </div>
                    ))}
                    {tags.length > 1 && (
                      <div className={classes.tag}>
                        <Paragraph>{`+${tags.length - 1}`}</Paragraph>
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
            <div className={classes.text}>
              <div className={classes.name}>
                <Paragraph>{sliceText(name, 28)}</Paragraph>
              </div>
              {description && (
                <div className={classes.secondary}>
                  <div className={classes.description}>
                    <Paragraph>{sliceText(description, (qtd && max > 1) ? 18 : 28)}</Paragraph>
                  </div>
                </div>
              )}
              <div className={classes.secondary}>
                <div className={classes.price}>
                  <Paragraph>{unitPrice}</Paragraph>
                </div>
              </div>
            </div>
            {setQtd && (
              <div className={classes.action}>
                {getAction()}
              </div>
            )}
          </div>
        </div>
      </div>
    </ButtonBase>
  );
});

// eslint-disable-next-line no-unused-vars
const MenuItemAddition = ({ id, name, description, min, max, qtd, limitReached, base, items = [], update, open, readOnly }) => {
  const classes = useStyles();
  const multi = max > 0 || qtd > 1;

  const itemsById = readOnly ? {} : byId(items.map((i) => ({ id: i.id, unitPrice: i.unitPrice, qtd: 0, base })));
  const itemIds = readOnly ? [] : ids(items);

  if (!multi && !readOnly) {
    itemsById[itemIds[0]].qtd = 1;
  }

  const [state, setState] = useState(itemsById);

  const fulfilledQtd = () => {
    const amount = Object.values(state).reduce((a, b) => a + (b.qtd || 0), 0);
    return qtd ? amount === (qtd) : true;
  };

  useEffect(() => {
    if (!readOnly) {
      update(id, state, fulfilledQtd());
    }
  }, [state]);

  const validQtd = (itemId, itemQtd) => {
    let currentState = state;
    currentState = { ...currentState, [itemId]: { ...currentState[itemId], qtd: itemQtd } };
    const amount = Object.values(currentState).reduce((a, b) => a + (b.qtd || 0), 0);
    return amount <= (max || qtd);
  };

  const updateState = (itemId, itemQtd) => {
    if (!multi) {
      setState({ [itemId]: { ...itemsById[itemId], qtd: itemQtd } });
      return;
    }
    if (validQtd(itemId, itemQtd)) {
      setState({ ...state, [itemId]: { ...state[itemId], qtd: itemQtd } });
    }
  };

  const currentAmount = qtd ? Object.values(state).reduce((a, b) => a + (b.qtd || 0), 0) : 0;

  return (
    <TitleContainer divider>
      <Title weight="medium">
        {name}
        {!readOnly && qtd && (qtd > 1) && <span className={clsx({ [classes.red]: qtd > currentAmount })}>{` (${currentAmount}/${qtd})`}</span>}
      </Title>
      {(description || qtd) && (
        <div className={classes.categoryDescription}>
          <Paragraph>
            {description}
          </Paragraph>
        </div>
      )}
      <div className={classes.item} key={id}>
        {items.map((item) => (
          <MenuItemAdditionElement
            key={item.id}
            {...item}
            multi={multi}
            qtd={(state[item.id] || {}).qtd || 0}
            setQtd={readOnly ? null : updateState}
            onClick={(e) => {
              open(item);
              preventEventPropagation(e);
            }}
            limitReached={limitReached}
          />
        ))}
      </div>
    </TitleContainer>
  );
};

export default withTranslation('common')(MenuItemAddition);
