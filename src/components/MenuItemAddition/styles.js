import { makeStyles } from '@material-ui/core/styles';
import { fade } from '@material-ui/core';
import { colors } from '../../../styles/theme';

const useStyles = makeStyles((theme) => ({
  buttonWrapper: {
    width: '100%',
    justifyContent: 'normal',
    textAlign: 'left',
    display: 'block'
  },
  container: {
    padding: theme.spacing(1, 0, 1, 0)
  },
  layout: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    minHeight: 48
  },
  imgWrapper: {
    position: 'relative',
    marginRight: theme.spacing(2)
  },
  tagsWrapper: {
    position: 'absolute',
    bottom: -5,
    left: 'auto',
    width: '100%',
    display: 'flex',
    justifyContent: 'center'
  },
  tags: {
    background: '#fff',
    display: 'flex',
    boxShadow: '0px 2px 4px rgba(47, 38, 34, 0.08)',
    borderRadius: theme.spacing(3),
    padding: theme.spacing('2px', '6px', '2px', '6px')
  },
  tag: {
    display: 'flex', // flex display fixes issue with parent height bigger than contained image,
    lineHeight: '18px', // corresponding to
    '&+&': {
      paddingLeft: theme.spacing(1)
    }
  },
  tagImg: {
    width: 18,
    height: 18
  },
  img: {
    height: 64,
    width: 64,
    objectFit: 'cover',
    borderRadius: 12
  },
  text: {
    flex: 1
  },
  name: {
    marginBottom: theme.spacing(1) - 2
  },
  secondary: {
    display: 'flex',
    flexDirection: 'row',
    '& > p:not(:first-child)': {
      marginLeft: theme.spacing(1)
    },
    '&+&': {
      marginTop: 6
    },
    '&:last-child': {
      flex: 1
    }
  },
  description: {
    color: colors.leviee.greyscale.darkGray
  },
  regularPrice: {
    color: colors.leviee.greyscale.midGray,
    textDecoration: 'line-through'
  },
  action: {
    textAlign: 'right'
  },
  categoryDescription: {
    marginTop: 2,
    color: fade(colors.leviee.main.dark, 0.48)
  },
  item: {
    marginTop: 12
  },
  red: {
    color: colors.leviee.secondary.wine
  }
}));

export default useStyles;
