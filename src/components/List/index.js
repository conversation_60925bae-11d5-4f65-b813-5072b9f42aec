import React from 'react';
import Container from '@material-ui/core/Container';
import Grid from '@material-ui/core/Grid';
import Link from 'next/link';
import Thumbnail from '../Thumbnail';
import Carousel from '../Carousel';
import MasonryList from '../MasonryList';
import { Title } from '../Text';
import { TitleContainer } from '../Containers';
import { withTranslation } from '../../../i18n';
import useStyles from './styles';

const List = ({ t, restaurants = [] }) => {
  const classes = useStyles();

  return (
    <Container maxWidth="lg">
      <div>
        <TitleContainer>
          <Title weight="medium">
            {t('landing-restaurant-list-featured-label')}
          </Title>
        </TitleContainer>
        <Carousel
          items={restaurants.map(({ id, name, tags, thumbnailUrl, slug }) => (
            <Link
              key={id}
              href={`/restaurant/${slug}`}
              shallow
            >
              <a className={classes.anchor}>
                <Thumbnail key={id} alt={name} src={thumbnailUrl} line1={name} line2={(tags || []).map((i) => i.label).join(' · ')} variant="s" />
              </a>
            </Link>
          ))}
          slideClassName={classes.carouselSlide}
        />
      </div>
      <div>
        <TitleContainer divider>
          <Title weight="medium">
            {t('landing-restaurant-list-near-by-label')}
          </Title>
        </TitleContainer>
        <MasonryList
          items={restaurants.map(({ id, slug, tags, name, thumbnailUrl }) => (
            <Link
              key={id}
              href={`/restaurant/${slug}`}
              shallow
            >
              <a className={classes.anchor}>
                <Thumbnail key={id} alt={name} src={thumbnailUrl} line1={name} line2={(tags || []).map((i) => i.label).join(' · ')} variant="l" />
              </a>
            </Link>
          ))}
        />
      </div>
      <div>
        <TitleContainer divider>
          <Title weight="medium">
            {t('landing-restaurant-list-all-label')}
          </Title>
        </TitleContainer>
        <Grid container spacing={3}>
          {restaurants.map(({ id, slug, name, tags, thumbnailUrl }) => (
            <Grid item xs={12} sm={6} md={3} key={id}>
              <Link
                key={id}
                href={`/restaurant/${slug}`}
                shallow
              >
                <a className={classes.anchor}>
                  <Thumbnail alt={name} src={thumbnailUrl} line1={name} line2={(tags || []).map((i) => i.label).join(' · ')} />
                </a>
              </Link>
            </Grid>
          ))}
        </Grid>
      </div>
    </Container>
  );
};

export default withTranslation('common')(List);
