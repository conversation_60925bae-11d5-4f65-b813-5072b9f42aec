import { makeStyles } from '@material-ui/core/styles';
import { fade } from '@material-ui/core';
import { colors } from '../../../styles/theme';
import palette from "../../../styles/palette";

const useStyles = makeStyles((theme) => ({
  wrapper: {
    // margin: "0 auto",
    // display: "flex",
    // flexDirection: "row"
  },
  wrapperMobile: {
    display: "block"
  },
  tabBarWrapper: {
    // boxShadow: '0px 2px 3px rgba(4, 23, 47, 0.08)',
    position: 'sticky',
    top: 0,
    backgroundColor: palette.grayscale["200"],
    zIndex: 1300,
    alignSelf: "flex-start",
    flexBasis: "15%",
    // paddingRight: 24,
    // display: "flex",
    // overflow: "hidden",
    maxWidth: 1140,
    margin: "0 auto",
    // boxSizing: "content-box",
    paddingLeft: 12,
    paddingRight: 12,
    '@media (min-width: 600px)': {
      paddingLeft: 18,
      paddingRight: 18
    }
  },
  promotionDisclaimerWrapper: {
    maxWidth: 1140,
    margin: "12px auto 0",
    paddingLeft: 12,
    paddingRight: 12,
    '@media (min-width: 600px)': {
      paddingLeft: 18,
      paddingRight: 18
    }
  },
  tabBarWrapperMobile: {
    display: "block",
    paddingRight: 0
  },
  tabPanelsWrapper: {
    // background: fade(colors.leviee.main.dark, 0.02),
    paddingBottom: theme.spacing(3),
    minHeight: 800,
    flexBasis: "85%"
  },
  carouselSlide: {
    width: '70%',
    maxWidth: 234
  },
  layout: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    minHeight: 64
  },
  ongoingItemsWrapper: {
    paddingTop: theme.spacing(2)
  },
  menuItem: {
    boxSizing: "content-box",
    width: "100%"
  },
  menuItemMobile: {}
}));

export default useStyles;
