/* eslint-disable max-len */
import React, {useEffect, useState} from 'react';
import Container from '@material-ui/core/Container';
import throttle from 'lodash/throttle';
import { trackWindowScroll } from 'react-lazy-load-image-component';
import { useSelector } from 'react-redux';
import { Skeleton } from '@material-ui/lab';
import isEmpty from '../../utilities/isEmpty';
import TabBar from '../TabBar';
import { TitleContainer } from '../Containers';
import MenuItem from '../MenuItem';
import { CounterButton, QuantityButton } from '../Buttons';
import preventEventPropagation from '../../utilities/preventEventPropagation';
import { menusSelectors, orderSelectors } from '../../../redux/selectors';
import { getQuantity, noop, textToHash } from './utils';
import { googleTags } from '../../../gtm';
import Carousel from '../Carousel';
import OngoingOrderingAction from "../OngoingOrderingAction";
import useStyles from './styles';
import {useMediaQuery} from "@material-ui/core";
import clsx from "clsx";
import typography from "../../../styles/typography";
import Typography from "@material-ui/core/Typography";
import MenuItemDetailsModal from "../_popup/MenuItemDetailsModal";
import PromotionDisclaimer from "../PromotionDisclaimer";

const tabHeight = 96;

function useThrottledOnScroll(callback, delay) {
  const throttledCallback = React.useMemo(
    () => (callback ? throttle(callback, delay) : noop),
    [callback, delay]
  );

  React.useEffect(() => {
    if (throttledCallback === noop) return undefined;
    if (typeof window === 'undefined') {
      // eslint-disable-next-line consistent-return
      return;
    }

    // eslint-disable-next-line no-undef
    window.addEventListener('scroll', throttledCallback);
    return () => {
      // eslint-disable-next-line no-undef
      window.removeEventListener('scroll', throttledCallback);
      throttledCallback.cancel();
    };
  }, [throttledCallback]);
}

const Menu = ({ addOrderItem, removeOrderItem, readOnly, detailsOpen, openDetails, closeDetails, ...otherProps }) => {
  const classes = useStyles();
  const { menus = [] } = useSelector(menusSelectors.getMenus);
  const { byCode } = useSelector(orderSelectors.getUnconfirmed);
  
  const isMobile = useMediaQuery('(max-width:600px)');
  const isDesktop = useMediaQuery('(min-width:601px)');

  /**
   * Wrapping menu with trackWindowScroll, the deepest common parent of those components with a HOC
   * to track scroll events for the menu item images
   */
  const { scrollPosition, noAddButton, configuration = {} } = otherProps;
  const { hideMenuItemNotesFromCustomers } = (configuration || {})

  const [activeState, setActiveState] = useState(null);
  const [activeMenuItem, setActiveMenuItem] = useState(null);
  
  useEffect(() => {
    if (!detailsOpen) {
      setActiveMenuItem(null);
    }
  }, [detailsOpen])
  
  const updateActiveMenuItem = (val) => {
    setActiveMenuItem(val);
    if (val) {
      openDetails();
    } else {
      closeDetails();
    }
  }

  const menuCategories = menus.map((i) => {
    const hash = textToHash(i.title);
    const items = i.items
    const itemSize = items.length;
    return {
      id: i.id,
      label: i.title,
      hash,
      // eslint-disable-next-line no-undef
      node: document.getElementById(hash),
      size: itemSize,
      emoji: i.emoji
    };
  });

  const menuCategoriesRef = React.useRef([]);
  React.useEffect(() => {
    menuCategoriesRef.current = menuCategories;
  }, [menuCategories]);

  const clickedRef = React.useRef(false);
  const unsetClickedRef = React.useRef(null);

  const findActiveIndex = React.useCallback(() => {
    // set default if activeState is null
    if (activeState === null) setActiveState(menuCategories[0].hash);

    // Don't set the active index based on scroll if a link was just clicked
    if (clickedRef.current) return;

    let active;
    for (let i = menuCategoriesRef.current.length - 1; i >= 0; i -= 1) {
      // No hash if we're near the top of the page
      // eslint-disable-next-line no-undef
      if (document.documentElement.scrollTop < 0) {
        active = { hash: null };
        break;
      }

      const item = menuCategoriesRef.current[i];

      if (
        item.node
        && item.node.offsetTop
        // eslint-disable-next-line no-undef
        < document.documentElement.scrollTop
        // eslint-disable-next-line no-undef
        + document.documentElement.clientHeight / 8
        + tabHeight
      ) {
        active = item;
        break;
      }
    }

    if (active && activeState !== active.hash) {
      setActiveState(active.hash);
    }
  }, [activeState, menuCategories]);

  useThrottledOnScroll(menuCategories.length > 0 ? findActiveIndex : null, 166);

  const handleClick = (hash) => () => {
    // Used to disable findActiveIndex if the page scrolls due to a click
    clickedRef.current = true;
    unsetClickedRef.current = setTimeout(() => {
      clickedRef.current = false;
    }, 1000);

    if (activeState !== hash) {
      setActiveState(hash);

      if (typeof window !== 'undefined') {
        // eslint-disable-next-line no-undef
        window.scrollTo({
          top:
          // eslint-disable-next-line no-undef
            document.getElementById(hash).getBoundingClientRect().top
            // eslint-disable-next-line no-undef
            + window.pageYOffset - tabHeight,
          behavior: 'smooth'
        });
      }
    }
  };

  React.useEffect(
    () => () => {
      clearTimeout(unsetClickedRef.current);
    },
    []
  );

  const tabs = menuCategories.map((m) => ({
    key: m.id,
    label: m.label,
    onClick: handleClick(m.hash),
    value: m.hash,
    size: m.size,
    emoji: m.emoji
  }));

  const onOrder = (data) => {
    updateActiveMenuItem(null);
    addOrderItem(data);
  };

  const isLoading = isEmpty(menus);
  
  const getAction = (i) => {
    const orderItemsByCode = byCode[i.code] || [];
    const qtd = getQuantity(orderItemsByCode)
    const hasAdditions = !isEmpty(i.options) || !isEmpty(i.extras);
    
    const orderItem = isEmpty(orderItemsByCode) ? null : orderItemsByCode[0]
    const additionsCrossCategoryMax = isEmpty(orderItem) ? null : orderItem.ongoingExtrasQuantityMax
    const hasAdditionsCrossCategoryMax = !!additionsCrossCategoryMax && (additionsCrossCategoryMax < 99)
    
    if (qtd && orderItem) {
      return (
        <QuantityButton
          counter={qtd}
          onMore={(e) => {
            if (hasAdditionsCrossCategoryMax && hasAdditions) {
              updateActiveMenuItem(i);
              preventEventPropagation(e);
              return;
            }
            onOrder({code: i.code, qtd: orderItem.qtd + 1, orderItemId: orderItem.id, ignoreNull: true })
            preventEventPropagation(e);
          }}
          onLess={(e) => {
            const newQtd = orderItem.qtd - 1;
            if (newQtd) {
              onOrder({code: i.code, qtd: newQtd, orderItemId: orderItem.id, ignoreNull: true})
            } else {
              removeOrderItem(orderItem.id)
            }
            preventEventPropagation(e);
          }}
        />
      )
    }
    
    return (
      <CounterButton
        id={googleTags.menu.addItemBtn.id}
        counter={qtd}
        onClick={(e) => {
          if (hasAdditions) {
            updateActiveMenuItem(i);
          } else {
            onOrder({ code: i.code, qtd: 1 });
          }
          preventEventPropagation(e);
        }}
      />
    )
  }

  return (
    <div style={{ paddingBottom: 80 }}>
      <div className={clsx(classes.wrapper, {[classes.wrapperMobile]: !!isMobile})} style={{ maxWidth: 1200, margin: '0 auto' }}>
        <div className={clsx(classes.tabBarWrapper, { [classes.tabBarWrapperMobile]: !!isMobile })}>
          {!isEmpty(menuCategories) && (
            <TabBar tabs={tabs} value={activeState || menuCategories[0].hash} />
          )}
        </div>
        {readOnly && (
          <div className={classes.promotionDisclaimerWrapper}>
            <PromotionDisclaimer />
          </div>
        )}
        <Container>
          <div className={classes.tabPanelsWrapper}>
            {isLoading ? (
              <>
                <TitleContainer>
                  <Skeleton width="200px" height="18px" style={{ transform: 'none' }}>
                    <Typography component="h2" style={{ ...typography.medium.semiBold }}>
                      -----
                    </Typography>
                  </Skeleton>
                </TitleContainer>
                <div style={isDesktop
                  ? { display: "grid", gridTemplateColumns: "repeat(2,minmax(160px, 1fr))", gap: 12, marginTop: 12 }
                  : { display: "grid", gridTemplateColumns: "repeat(1,minmax(160px, 1fr))", gap: 12, marginTop: 12 }
                }>
                  <div className={clsx({ [classes.menuItem]: !isMobile }, { [classes.menuItemMobile]: isMobile })}>
                    <MenuItem loading />
                  </div>
                  <div className={clsx({ [classes.menuItem]: !isMobile }, { [classes.menuItemMobile]: isMobile })}>
                    <MenuItem loading />
                  </div>
                  <div className={clsx({ [classes.menuItem]: !isMobile }, { [classes.menuItemMobile]: isMobile })}>
                    <MenuItem loading />
                  </div>
                  <div className={clsx({ [classes.menuItem]: !isMobile }, { [classes.menuItemMobile]: isMobile })}>
                    <MenuItem loading />
                  </div>
                  <div className={clsx({ [classes.menuItem]: !isMobile }, { [classes.menuItemMobile]: isMobile })}>
                    <MenuItem loading />
                  </div>
                  <div className={clsx({ [classes.menuItem]: !isMobile }, { [classes.menuItemMobile]: isMobile })}>
                    <MenuItem loading />
                  </div>
                </div>
              </>
            ) : (
              <>
                <OngoingOrderingAction />
                {(menus || [])
                  .map((menu, index) => {
                    const isCard = (menu.displayType === 'CARD');
                    const menuItems = (menu.items || []).map((i) => (
                      <div
                        className={clsx({ [classes.menuItem]: !isCard && !isMobile }, { [classes.menuItemMobile]: isMobile && !isCard })}
                        key={i.id}>
                        <MenuItem
                          key={i.id}
                          view={menu.displayType}
                          {...i}
                          disabled={i.disabled && !readOnly}
                          price={i.unitPrice}
                          action={!readOnly && !noAddButton && getAction(i)}
                          onClick={() => ((i.disabled && !readOnly) ? {} : updateActiveMenuItem(i))}
                          scrollPosition={scrollPosition}
                        />
                      </div>
                    ));
                    return (
                      <div key={menu.id} id={textToHash(menu.title)}>
                        <TitleContainer divider={index >= 0}>
                          <div style={{ display: "flex", alignItems: "center" }}>
                            <Typography component="h2" style={{ ...typography.medium.semiBold }}>
                              {menu.title}
                            </Typography>
                            {!!menu.emoji && <span style={{ marginLeft: 4, fontSize: 24 }}>{menu.emoji}</span>}
                          </div>
                
                        </TitleContainer>
                        {(menu.displayType === 'CARD') ? (
                          <Carousel
                            items={menuItems}
                            slideClassName={classes.carouselSlide}
                            noAddButto={noAddButton}
                          />
                        ) : (
                          <div style={isMobile
                            ? { display: "grid", gridTemplateColumns: "repeat(1,minmax(160px, 1fr))", gap: 12 }
                            : { display: "grid", gridTemplateColumns: "repeat(2,minmax(160px, 1fr))", gap: 12 }}>
                            {menuItems}
                          </div>
                        )}
                      </div>
                    );
                  })}
              </>
            )}
          </div>
        </Container>
        {activeMenuItem && (
          <MenuItemDetailsModal
            onClose={() => {
              updateActiveMenuItem(null)
            }}
            activeMenuItem={activeMenuItem}
            addOrderItem={onOrder}
            readOnly={readOnly}
            showNotes={!hideMenuItemNotesFromCustomers}
          />
          // <Overlay open={!!activeMenuItem}>
          //   <ActivityBar
          //     title={activeMenuItem.name}
          //     close={(
          //       <IconButton component="a" edge="end" className={classes.menuButton} color="inherit" aria-label="menu" onClick={() => setActiveMenuItem(null)}>
          //         <CloseIcon />
          //       </IconButton>
          //     )}
          //   />
          //   <Container>
          //     <MenuItemDetails
          //       {...activeMenuItem}
          //       addOrderItem={onOrder}
          //       readOnly={readOnly}
          //     />
          //   </Container>
          // </Overlay>
        )}
      </div>
    </div>
  );
};

export default trackWindowScroll(Menu);
