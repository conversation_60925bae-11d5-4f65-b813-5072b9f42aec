import { makeStyles } from '@material-ui/core/styles';
import { colors } from '../../../styles/theme';

const useStyles = makeStyles(() => ({
  formControlRoot: {
    width: '100%',
    padding: (props) => `${props.label ? 10 : 6}px 0px ${props.label ? 0 : 6}px 0px`,
    borderRadius: 6,
    position: 'relative',
    backgroundColor: colors.leviee.greyscale.lightestGray,
    '&:focus': {
      boxShadow: 'none',
      backgroundColor: colors.leviee.greyscale.lightestGray,
      border: `1px solid ${colors.leviee.greyscale.darkGray}`,
      borderRadius: 6,
    },
    '&:hover': {
      backgroundColor: colors.leviee.greyscale.lightestGray,
      // border: `1px solid ${colors.leviee.greyscale.darkGray}`,
      borderRadius: 6,
    },
    '&$focused': {
      backgroundColor: colors.leviee.greyscale.lightestGray,
      // boxShadow: `${fade(theme.palette.primary.main, 0.25)} 0 0 0 2px`,
      border: `1px solid ${colors.leviee.greyscale.darkGray}`,
      borderRadius: 6,
    },
  },
  inputLabelRoot: {
    color: colors.leviee.greyscale.midGray,
    '&&': {
      color: colors.leviee.greyscale.midGray,
    },
    '&:hover': {
      color: colors.leviee.greyscale.midGray,
    },
    '&$focused': {
      color: colors.leviee.main.dark,
      '&&': {
        color: colors.leviee.main.dark
      }
    },
    '&:focus': {
      color: colors.leviee.main.dark,
    }
  },
  nativeSelectRoot: {
    backgroundColor: colors.leviee.greyscale.lightestGray
  },
  nativeSelectFilled: {
    backgroundColor: colors.leviee.greyscale.lightestGray
  }
}));

export default useStyles;
