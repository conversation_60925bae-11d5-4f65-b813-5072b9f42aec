import React from 'react';
import { withStyles } from '@material-ui/core';
import InputBase from '@material-ui/core/InputBase';
import NativeSelect from '@material-ui/core/NativeSelect';
import FormControl from '@material-ui/core/FormControl';
import InputLabel from '@material-ui/core/InputLabel';
import { fontStyles } from '../../../styles/theme';
import useStyles from './styles';

const StyledInputBase = withStyles((theme) => ({
  root: {
    'label + &': {
      marginTop: theme.spacing(2) + 2,
      background: 'transparent'
    },
  },
  input: {
    ...fontStyles.paragraphRegular,
    padding: '10px 26px 10px 12px',
    transition: theme.transitions.create(['border-color', 'box-shadow']),
    background: 'transparent',
    '&:hover': {
      background: 'transparent',
    },
    '&:focus': {
      background: 'transparent',
    },
    '&$focused': {
      background: 'transparent',
    }
  },
}))(InputBase);

const SelectField = (props) => {
  const { label, value, onChange, options, InputLabelProps = {} } = props;
  const classes = useStyles(props);

  return (
    <FormControl variant="filled" classes={{ root: classes.formControlRoot }}>
      {label && <InputLabel classes={{ root: classes.inputLabelRoot }} htmlFor="my-input" {...InputLabelProps}>{label}</InputLabel>}
      <NativeSelect
        classes={{ root: classes.nativeSelectRoot, filled: classes.nativeSelectFilled }}
        value={value}
        onChange={onChange}
        input={<StyledInputBase />}
        {...props}
      >
        {options
          .map(
            ({
              id,
              value: optionValue,
              label: optionLabel,
              disabled
            }) => <option key={id} value={optionValue} disabled={disabled}>{optionLabel}</option>
          )}
      </NativeSelect>
    </FormControl>
  );
};

export default SelectField;
