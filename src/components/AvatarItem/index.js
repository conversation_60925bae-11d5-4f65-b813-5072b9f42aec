import React from 'react';
import initials from 'initials';
import { Paragraph } from '../Text';
import Avatar from '../Avatar';
import useStyles from './styles';

const AvatarItem = ({ firstName, lastName }) => {
  const classes = useStyles();
  const fullName = `${firstName} ${lastName}`;

  return (
    <div className={classes.container}>
      <div>
        <div className={classes.layout}>
          <Avatar
            alt={fullName}
          >
            {initials(fullName)}
          </Avatar>
          <div className={classes.name}>
            <Paragraph>{fullName}</Paragraph>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AvatarItem;
