import { makeStyles } from '@material-ui/core/styles';
import { colors } from '../../../styles/theme';
import {fade} from '@material-ui/core';

const useStyles = makeStyles((theme) => ({
  container: {
    padding: theme.spacing(1, 0, 1, 0)
  },
  layout: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
  },
  avatarRoot: {
    width: 32,
    height: 32,
    backgroundColor: colors.leviee.greyscale.lightestGray,
    color: colors.leviee.greyscale.darkGray,
    border: `1px solid ${fade(colors.leviee.main.dark, 0.2)}`,
    fontSize: 11,
    lineHeight: '12px',
    fontWeight: 400
  },
  name: {
    marginLeft: 8
  },
}));

export default useStyles;
