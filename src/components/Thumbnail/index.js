import React from 'react';
import CardMedia from '@material-ui/core/CardMedia';
import CardContent from '@material-ui/core/CardContent';
import Typography from '@material-ui/core/Typography';
import Card from '@material-ui/core/Card';
import useStyles from './styles';

const height = {
  s: 140,
  m: 180,
  l: 200
};

const borderRadius = {
  s: 4,
  m: 4,
  l: 4
};

const Thumbnail = ({ id, src, alt, line1, line2, variant = 'm' }) => {
  const classes = useStyles();

  return (
    <div className={classes.wrapper} id={`thumbnail-${id}`}>
      <Card
        elevation={0}
        classes={{
          root: classes.cardRoot
        }}
      >
        <CardMedia
          component="img"
          alt={alt}
          height={height[variant]}
          image={src}
          classes={{
            root: classes.cardMediaRoot
          }}
          style={{ borderRadius: borderRadius[variant] }}
        />
        <CardContent classes={{
          root: classes.cardContentRoot
        }}
        >
          {line1 && <Typography className={classes.line1}>{line1}</Typography>}
          {line2 && <Typography className={classes.line2}>{line2}</Typography>}
        </CardContent>
      </Card>
    </div>
  );
};

export default Thumbnail;
