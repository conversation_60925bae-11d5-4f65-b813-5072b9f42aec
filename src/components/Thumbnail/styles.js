import { makeStyles } from '@material-ui/core/styles';
import { colors, fontStyles } from '../../../styles/theme';

const useStyles = makeStyles((theme) => ({
  wrapper: {
    paddingTop: theme.spacing(1),
    paddingBottom: theme.spacing(1)
  },
  line1: {
    ...fontStyles.paragraphRegular,
    marginTop: theme.spacing(2) - 4,
    marginBottom: 2
  },
  line2: {
    ...fontStyles.paragraphRegular,
    color: colors.leviee.greyscale.darkGray
  },
  cardRoot: {
    background: 'transparent'
  },
  cardContentRoot: {
    '&&': {
      padding: 0
    }
  },
  cardMediaRoot: {
    borderRadius: 4,
  }
}));

export default useStyles;
