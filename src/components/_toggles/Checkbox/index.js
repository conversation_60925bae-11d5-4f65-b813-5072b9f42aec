import React from "react";
import { ButtonBase} from "@material-ui/core";
import useStyles from "./styles";
import {CheckboxOffIcon, CheckboxOnIcon} from "../../../utilities/icons";

function Checkbox(props) {
  const classes = useStyles();
  
  const { checked } = props;
  
  return (
    <ButtonBase disableRipple disableTouchRipple className={classes.button} {...props}>
      {checked ? <CheckboxOnIcon /> : <CheckboxOffIcon />}
    </ButtonBase>
  );
}

export default Checkbox;
