import { makeStyles } from "@material-ui/core/styles";

const useStyles = makeStyles((theme) => ({
  root: {
    padding: 0,
    '&:hover': {
      backgroundColor: 'transparent',
    },
  },
  icon: {
    borderRadius: '50%',
    width: 16,
    height: 16,
    border: "1px solid #D8D7D6",
    backgroundColor: theme.palette.common.white,
    '$root.Mui-focusVisible &': {
      outlineOffset: 2,
    },
    'input:disabled ~ &': {
      boxShadow: 'none',
      background: 'rgba(206,217,224,.5)',
    },
  },
  checkedIcon: {
    border: "none",
    backgroundColor: '#FF7C5C',
    '&:before': {
      display: 'block',
      width: 16,
      height: 16,
      backgroundImage: `radial-gradient(${theme.palette.common.white},${theme.palette.common.white} 25%,transparent 27%)`,
      content: '""',
    },
  },
}));

export default useStyles
