import React from "react";
import clsx from "clsx";
import { Radio as MuiRadio } from "@material-ui/core";
import useStyles from "./styles";

function Radio(props) {
  const classes = useStyles();
  
  return (
    <MuiRadio
      className={classes.root}
      disableRipple
      color="default"
      checkedIcon={<span className={clsx(classes.icon, classes.checkedIcon)} />}
      icon={<span className={classes.icon} />}
      {...props}
    />
  );
}

export default Radio;
