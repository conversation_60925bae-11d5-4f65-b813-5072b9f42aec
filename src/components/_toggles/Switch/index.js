import React from "react";
import { ButtonBase} from "@material-ui/core";
import useStyles from "./styles";
import {SwitchOffIcon, SwitchOnIcon} from "../../../utilities/icons";

function Switch(props) {
  const classes = useStyles();
  const { checked } = props;
  
  return (
    <ButtonBase disableRipple disableTouchRipple className={classes.button} {...props}>
      {checked ? <SwitchOnIcon /> : <SwitchOffIcon />}
    </ButtonBase>
  );
}

export default Switch;
