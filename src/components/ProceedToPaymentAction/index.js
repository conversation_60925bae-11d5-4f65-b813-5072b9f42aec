import React from 'react';
import { MainButton } from '../Buttons';
import { withTranslation } from '../../../i18n';
import useStyles from './styles';

const ProceedToPaymentAction = ({ t, count, amount = 0, ...otherProps }) => {
  const classes = useStyles();
  delete otherProps.tReady;

  return (
    <div className={classes.container}>
      <div className={classes.layout}>
        <div className={classes.description}>
          {t('order-current-order-payment-modal-description')}
        </div>
        <MainButton className={classes.buttonContainer} {...otherProps}>
          <div className={classes.buttonLayout}>
            <div>
              {t('order-current-order-payment-modal-proceed-to-payment-btn-label')}
            </div>
            <div className={classes.sidebar}>
              <div className={classes.divider} />
              <div className={classes.priceLabel}>
                {`${amount.toFixed(2)} €`}
              </div>
            </div>
          </div>
        </MainButton>
      </div>
    </div>
  );
};

export default withTranslation('common')(ProceedToPaymentAction);
