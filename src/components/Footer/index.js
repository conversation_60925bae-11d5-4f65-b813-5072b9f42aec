import React from 'react';
import { Typography, useMediaQuery } from "@material-ui/core";
import { withTranslation } from '../../../i18n';
import { AlloAvatar32 } from "../../utilities/icons";
import palette from "../../../styles/palette";
import typography from "../../../styles/typography";
import LanguageField from "../_input/LanguageField";

const Footer = () => {
  const isMobile = useMediaQuery('(max-width:600px)');
  
  return (
    <div style={{
      background: palette.grayscale["800"],
      paddingTop: 32,
      paddingBottom: 32,
      paddingLeft: isMobile ? 16 : 32,
      paddingRight: isMobile ? 16 : 32,
    }}>
      <div style={{ maxWidth: 1140, margin: '0 auto' }}>
        <AlloAvatar32 />
        <Typography style={{ ...typography.body.regular, color: palette.grayscale["100"], marginTop: 16 }}>
          You are interacting with restaurants in this platform digitally via allO, which is their software provider.
          allO assists the communication between you and the restaurant within the GDPR data protection framework.
          You always establish contractual relationship with the restaurant and for any questions, please contact the restaurant directly.
        </Typography>
        <div style={{ marginTop: 16, maxWidth: 150 }}>
          <LanguageField />
        </div>
        <Typography style={{
          paddingTop: 16,
          marginTop: 16,
          borderTop: `1px dashed ${palette.transparency.light["20"]}`,
          ...typography.small.regular,
          color: palette.transparency.light["50"]
        }}>
          allO Technology GmbH, Hopfenstraße 8, 80335 Munich | Managing Directors: Cancan Liu, Teodor Rupi | Commercial Register: HRB 258146, Amtsgericht München
        </Typography>
        <div style={{ marginTop: 12, display: "flex", alignItems: "center" }}>
          <a
            style={{ ...typography.small.regular, color: palette.grayscale["100"] }}
            href="https://www.allo.restaurant/impressum"
            target="_blank">
            Imprint
          </a>
          <Typography style={{ ...typography.small.regular, color: palette.grayscale["100"], marginLeft: 6, marginRight: 6 }}>·</Typography>
          <a
            style={{ ...typography.small.regular, color: palette.grayscale["100"] }}
            href="https://www.allo.restaurant/terms"
            target="_blank">
            Terms & Conditions
          </a>
          <Typography style={{ ...typography.small.regular, color: palette.grayscale["100"], marginLeft: 6, marginRight: 6 }}>·</Typography>
          <a
            style={{ ...typography.small.regular, color: palette.grayscale["100"] }}
            href="https://www.allo.restaurant/privacy-policy"
            target="_blank">
            Privacy Policy
          </a>
        </div>
        <Typography style={{ ...typography.small.regular, color: palette.grayscale["100"], marginTop: 12 }}>
          Made with ❤️ in Germany 🇩🇪
        </Typography>
      </div>
    </div>
  );
};

export default withTranslation('common')(Footer);
