import React from 'react';
import PropTypes from 'prop-types';
import clsx from 'clsx';
import useStyles from './styles';

const ButtonContainer = ({ withMargin, ...otherProps }) => {
  const classes = useStyles();
  const { children } = otherProps;

  return (
    <div className={clsx(
      classes.container,
      { [classes.containerWithMargin]: withMargin }
    )}
    >
      {children}
    </div>
  );
};

ButtonContainer.propTypes = {
  withMargin: PropTypes.bool,
};

ButtonContainer.defaultProps = {
  withMargin: false
};

export default ButtonContainer;
