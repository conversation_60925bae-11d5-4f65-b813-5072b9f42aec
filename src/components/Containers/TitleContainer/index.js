import React from 'react';
import PropTypes from 'prop-types';
import clsx from 'clsx';
import useStyles from './styles';

const TitleContainer = ({ divider, withText, ...otherProps }) => {
  const classes = useStyles();
  const { children } = otherProps;

  return (
    <>
      {divider && (
      <div className={clsx({
        [classes.dividerContainer]: !withText,
        [classes.dividerContainerWithText]: withText
      })}
      />
      )}
      <div className={clsx(
        { [classes.container]: !withText && !divider },
        { [classes.containerWithDividerAndText]: withText && divider },
        { [classes.containerWithDivider]: !withText && divider },
        { [classes.containerWithText]: withText && !divider },
      )}
      >
        {children}
      </div>
    </>
  );
};

TitleContainer.propTypes = {
  divider: PropTypes.bool,
  withText: PropTypes.bool,
};

TitleContainer.defaultProps = {
  divider: false,
  withText: false
};

export default TitleContainer;
