import { makeStyles } from '@material-ui/core/styles';
import { containerStyles } from '../../../../styles/theme';

const useStyles = makeStyles(() => ({
  container: {
    ...containerStyles.titleMediumContainer,
    display: 'inline-block'
  },
  containerWithText: {
    ...containerStyles.titleMediumWithTextContainer
  },
  containerWithDivider: {
    ...containerStyles.titleMediumWithDividerContainer
  },
  containerWithDividerAndText: {
    ...containerStyles.titleMediumWithDividerContainer
  },
  dividerContainer: {
    ...containerStyles.titleMediumDivider
  },
  dividerContainerWithText: {
    ...containerStyles.titleMediumWithTextDivider
  }
}));

export default useStyles;
