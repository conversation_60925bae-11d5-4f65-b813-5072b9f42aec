import React from 'react';
import dynamic from 'next/dynamic';
import useStyles from './styles';

/* eslint-disable-next-line */
const Camera = dynamic(() => import("../../components/Camera"), { ssr: false });

const Scanner = ({ onScan, onError }) => {
  const classes = useStyles();

  return (
    <div className={classes.wrapper}>
      <Camera onScan={onScan} onError={onError} />
    </div>
  );
};

export default Scanner;
