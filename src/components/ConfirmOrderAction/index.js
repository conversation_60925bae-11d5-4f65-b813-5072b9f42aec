import React from 'react';
import { MainButton } from '../Buttons';
import { withTranslation } from '../../../i18n';
import useStyles from './styles';
import typography from "../../../styles/typography";
import palette from "../../../styles/palette";
import Typography from "@material-ui/core/Typography";
import {fade} from "@material-ui/core";
import ButtonBase from "@material-ui/core/ButtonBase";

const ConfirmOrderAction = ({ t, count, amount = 0, orderType, ...otherProps }) => {
  const classes = useStyles();
  delete otherProps.tReady;

  return (
    <div className={classes.container}>
      <div className={classes.layout}>
        <div className={classes.description}>
          <Typography style={{ ...typography.body.regular }}>
            {`${orderType === "DINE_IN" ? "⚠️ " : ""}`}<span style={{ ...typography.body.medium }}>{`${t('you-have-pending-items')} `}</span>{t('order-current-order-order-items-modal-description')}
          </Typography>
        </div>
        <ButtonBase className={classes.buttonContainer} {...otherProps} style={{
          paddingLeft: 24,
          paddingRight: 24,
          height: 48,
          background: palette.primary["500"],
          borderRadius: 12,
          width: "100%"
        }}>
          <div className={classes.buttonLayout}>
            <div>
              <Typography style={{ ...typography.body.medium, color: palette.grayscale.white, display: "inline-block" }}>
                {t(orderType === "DINE_IN" ? 'send-to-kitchen' : 'order-current-order-order-items-modal-order-btn-label')}
              </Typography>
              <span style={{ ...typography.body.medium, color: fade(palette.grayscale.white, 0.72), }} className={classes.countLabel}>
                {t('order-current-order-order-items-modal-order-quantity-btn-label', { count })}
              </span>
            </div>
            {amount > 0 && (
              <div className={classes.sidebar}>
                <div className={classes.divider} />
                <div className={classes.priceLabel} style={{ ...typography.body.medium, color: palette.grayscale.white }}>
                  {`${amount.toFixed(2)} €`}
                </div>
              </div>
            )}
          </div>
        </ButtonBase>
      </div>
    </div>
  );
};

export default withTranslation('common')(ConfirmOrderAction);
