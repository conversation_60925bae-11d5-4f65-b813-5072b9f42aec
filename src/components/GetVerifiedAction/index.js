import React from 'react';
import { TableVerificationIconLight20 } from '../Icons';
import { withTranslation } from '../../../i18n';
import useStyles from './styles';
import palette from "../../../styles/palette";
import shadows from "../../../styles/shadows";
import {Typography} from "@material-ui/core";
import typography from "../../../styles/typography";
import ButtonBase from "@material-ui/core/ButtonBase";

const GetVerifiedAction = ({ t, onClick }) => {
  const classes = useStyles();
  
  return (
    <div style={{
      position: "sticky",
      width: "100%",
      bottom: 24,
      marginBottom: 24,
      zIndex: 1700,
      paddingLeft: 16,
      paddingRight: 16
    }}>
      <div style={{
        maxWidth: 400,
        margin: "0 auto",
        paddingTop: 16,
        paddingLeft: 16,
        paddingRight: 16,
        paddingBottom: 12,
        backgroundColor: palette.grayscale["800"],
        borderRadius: 12,
        ...shadows.large
      }}>
        <div style={{ paddingBottom: 16 }}>
          <div className={classes.header}>
            <TableVerificationIconLight20 />
            <div className={classes.title}>
              <Typography style={{ ...typography.body.medium, color: palette.grayscale.white }}>
                {t('order-current-order-table-verification-modal-title')}
              </Typography>
            </div>
          </div>
          <Typography style={{ ...typography.body.regular, color: palette.transparency.light["80"] }}>
            {t('order-current-order-table-verification-modal-description')}
          </Typography>
        </div>
        <div style={{
          borderTop: `1px dashed ${palette.transparency.light["20"]}`,
          paddingTop: 12,
          display: "flex",
          justifyContent: "flex-end",
          width: "100%"
        }}>
          <ButtonBase
            onClick={onClick}
            disableRipple
            disableTouchRipple
            style={{
              paddingTop: 12,
              paddingBottom: 12,
              paddingLeft: 24,
              paddingRight: 24,
              background: palette.primary["500"],
              borderRadius: 12
            }}
          >
            <Typography style={{ ...typography.body.medium, color: palette.grayscale["100"] }}>
              {t('order-current-order-table-verification-modal-verify-btn-label')}
            </Typography>
          </ButtonBase>
        </div>
      </div>
    </div>
  )
};

export default withTranslation('common')(GetVerifiedAction);
