import { makeStyles } from '@material-ui/core/styles';
import { colors } from '../../../styles/theme';

const useStyles = makeStyles((theme) => ({
  container: {
    padding: theme.spacing(1, 1, '8px', 1),
    margin: 8,
    borderRadius: theme.spacing(2),
    background: colors.leviee.main.dark,
    boxShadow: '0px 4px 6px rgba(4, 23, 47, 0.12)'
  },
  layout: {
    width: '100%'
  },
  headerWrapper: {
    paddingBottom: 16,
  },
  header: {
    width: '100%',
    display: 'flex',
    alignItems: 'center',
    marginBottom: 2
  },
  title: {
    // color: theme.palette.common.white,
    marginLeft: 8
  },
  description: {
    color: colors.leviee.greyscale.lightGray
  }
}));

export default useStyles;
