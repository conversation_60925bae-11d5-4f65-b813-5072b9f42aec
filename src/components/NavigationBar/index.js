import React from 'react';
import AppBar from '@material-ui/core/AppBar';
import Toolbar from '@material-ui/core/Toolbar';
import Container from '@material-ui/core/Container';
import { NavigationLink } from '../Links';
import useStyles from './styles';
import { Button } from '../Buttons';

const NavigationBar = () => {
  const classes = useStyles();

  return (
    <div className={classes.root}>
      <AppBar position="absolute" color="transparent" elevation={0}>
        <Toolbar>
          <Container maxWidth="lg">
            <div className={classes.layout}>
              <NavigationLink anchorProps={{ image: true }}>
                <img alt="leviee-logo-white" src="assets/logo/leviee-logo.png" className={classes.logo} />
              </NavigationLink>
              <div className={classes.left}>
                <NavigationLink>Why Leviee</NavigationLink>
                <NavigationLink linkProps={{ href: '/features' }}>Features</NavigationLink>
                <NavigationLink linkProps={{ href: '/pricing' }}>Pricing</NavigationLink>
              </div>
              <Button normal>
                Try for free
              </Button>
            </div>
          </Container>
        </Toolbar>
      </AppBar>
    </div>
  );
};

export default NavigationBar;
