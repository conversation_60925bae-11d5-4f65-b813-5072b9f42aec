import { makeStyles } from '@material-ui/core/styles';

const useStyles = makeStyles((theme) => ({
  root: {
    flexGrow: 1
  },
  layout: {
    display: 'flex',
    width: '100%',
    alignItems: 'center',
    margin: '0 auto'
  },
  menuButton: {
    marginRight: theme.spacing(2),
  },
  title: {
    flexGrow: 1,
  },
  logo: {
    maxWidth: 80,
    marginRight: theme.spacing(2)
  },
  left: {
    display: 'flex',
    flex: 1,
    marginTop: theme.spacing(1),
    '& > a:not(first-child)': {
      marginLeft: theme.spacing(4)
    }
  }
}));

export default useStyles;
