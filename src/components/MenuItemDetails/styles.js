import { makeStyles } from '@material-ui/core/styles';
import { fade } from '@material-ui/core';
import { colors } from '../../../styles/theme';
import palette from "../../../styles/palette";

const useStyles = makeStyles((theme) => ({
  container: {
    marginTop: 8,
    height: "100%",
    overflow: "auto"
    // marginBottom: 72
  },
  layout: {},
  img: {
    height: 280,
    marginBottom: 16,
    width: '100%',
    objectFit: 'cover',
    borderRadius: 12,
    '@media (min-width:499px)': {
      height: 320
    }
  },
  imgError: {
    height: 0,
    width: 0,
    display: 'none'
  },
  tags: {
    marginTop: 8,
    display: 'flex',
    flexWrap: 'wrap'
  },
  tag: {
    display: 'flex',
    padding: theme.spacing(1, 2, 1, 0),
    '&:first-child': {
      paddingLeft: 0
    }
  },
  tagImg: {
    marginRight: theme.spacing(1) / 2,
    width: 18,
    height: 18
  },
  text: {},
  name: {
    marginTop: 8
  },
  description: {
    marginTop: 8,
    color: colors.leviee.greyscale.darkGray,
    whiteSpace: 'pre-line',
  },
  pricing: {
    marginTop: 12,
    display: 'flex',
    flexDirection: 'row',
    '& > p:not(:first-child)': {
      marginLeft: theme.spacing(1)
    }
  },
  price: {
    color: colors.leviee.greyscale.darkGray
  },
  regularPrice: {
    color: colors.leviee.greyscale.midGray,
    textDecoration: 'line-through'
  },
  action: {
    flex: 1,
    textAlign: 'right'
  },
  notesDescription: {
    marginTop: 2,
    color: fade(colors.leviee.main.dark, 0.48)
  },
  remarksDescription: {
    marginTop: 2,
    color: fade(colors.leviee.main.dark, 0.48)
  },
  remarkDescription: {
    color: colors.leviee.greyscale.darkGray
  },
  remarks: {},
  remarkList: {
    marginTop: 8,
  },
  orderActionLayout: {
    width: '100%',
    // minHeight: 20
  },
  divider: {
    borderBottom: `1px solid ${fade(colors.leviee.main.dark, 0.06)}`,
    marginTop: 8,
    marginBottom: 8
  },
  orderQtdLayout: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingLeft: 20, paddingRight: 20, paddingTop: 16, paddingBottom: 4,
  }
}));

export default useStyles;
