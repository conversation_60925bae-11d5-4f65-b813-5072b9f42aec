import React, { useEffect, useState } from 'react';
import { LazyLoadImage } from 'react-lazy-load-image-component';
import clsx from 'clsx';
import getConfig from 'next/config';
import isEmpty from '../../utilities/isEmpty';
import { DisplayTitle, Paragraph, Title } from '../Text';
import { TitleContainer } from '../Containers';
import MenuItemDetailsAction from '../MenuItemDetailsAction';
import QuantityButton from '../Buttons/QuantityButton';
import MenuItemAddition from '../MenuItemAddition';
// eslint-disable-next-line import/named
import { withTranslation } from '../../../i18n';
import MessageDrawer from '../MessageDrawer';
import MenuItemAdditionElementDetails from '../MenuItemAdditionElementDetails';
import useStyles from './styles';
import Field from "../_input/Field";
import palette from "../../../styles/palette";
import Modal from "../_popup/Modal";
import ModalBar from "../_navigation/ModalBar";
import shadows from "../../../styles/shadows";
import ButtonBase from "@material-ui/core/ButtonBase";
import {googleTags} from "../../../gtm";
import {Typography} from "@material-ui/core";
import typography from "../../../styles/typography";

const { publicRuntimeConfig } = getConfig();

const factorOngoingAdditions = (additions, factor) => {
  if (!isEmpty(additions) && factor > 1) {
    additions.map((addition) => {
      if (addition.qtd) {
        addition.qtd *= factor;
      }
      if (addition.max) {
        addition.max *= factor;
      }

      if (!isEmpty(addition.items)) {
        addition.items = addition.items.map((i) => Object.assign(i, { max: i.max * factor }));
      }
      return addition;
    });
  }
  return additions;
};

const MenuItemDetails = ({
  t, thumbnailUrl, name, code, description, remarks, unitPrice, salePrice, volume, addOrderItem, readOnly,
  options, extras, tags = [], orderItem = {}, ongoingExtrasQuantityMax: additionsCrossCategoryMax, showNotes
}) => {
  const classes = useStyles();

  const [isMenuItemAdditionElementDetailsOpen, setMenuItemAdditionElementDetails] = useState(null);
  const showMenuItemAdditionElementDetails = (menuItemAdditionElem) => setMenuItemAdditionElementDetails(menuItemAdditionElem);
  const hideMenuItemAdditionElementDetails = () => setMenuItemAdditionElementDetails(null);

  const [imgError, setImgError] = useState(false);
  unitPrice = (unitPrice || 0).toFixed(Number.isInteger(unitPrice) ? 0 : 2);
  salePrice = salePrice && salePrice.toFixed(Number.isInteger(salePrice) ? 0 : 2);

  const [notes, setNotes] = useState('');
  const [qtd, setQtd] = useState(1);

  const onMore = () => setQtd(qtd + 1);
  const onLess = () => setQtd(qtd > 0 ? qtd - 1 : 0);

  const updateNotes = (e) => setNotes(e.target.value);

  const [excludedAdditionalItemIds, setExcludedAdditionalItemIds] = useState([]);
  const { qtd: orderItemQtd = 1, ongoing } = (orderItem ?? {});
  
  const [canAddAddition, setCanAddAddition] = useState(true);
  const additionsCrossCategoryMaxForQtd = additionsCrossCategoryMax * (ongoing ? orderItemQtd : qtd);

  let resolvedOptions = (!ongoing || orderItemQtd === 1)
    ? options
    : factorOngoingAdditions(JSON.parse(JSON.stringify(options)), orderItemQtd);
  let resolvedExtras = (!ongoing || orderItemQtd === 1)
    ? extras
    : factorOngoingAdditions(JSON.parse(JSON.stringify(extras)), orderItemQtd);

  useEffect(() => {
    const additionIds = [];
    if (ongoing && (!isEmpty(resolvedOptions) || !isEmpty(resolvedExtras))) {
      const { nestedOrderItems = [] } = orderItem;
      if (!isEmpty(nestedOrderItems)) {
        nestedOrderItems.forEach(({ options = [], extras = [] }) => {
          if (!isEmpty(options)) {
            options.forEach(({ id, rounds }) => {
              if (rounds === 1) {
                additionIds.push(id);
              }
            });
          }
          if (!isEmpty(extras)) {
            extras.forEach(({ id, rounds }) => {
              if (rounds === 1) {
                additionIds.push(id);
              }
            });
          }
        });
        setExcludedAdditionalItemIds(additionIds);
      }
    }
  }, []);

  resolvedOptions = ongoing
    ? resolvedOptions.filter((i) => excludedAdditionalItemIds.indexOf(i.id) === -1 && !isEmpty(i.items))
    : resolvedOptions;

  resolvedExtras = ongoing
    ? resolvedExtras.filter((i) => excludedAdditionalItemIds.indexOf(i.id) === -1 && !isEmpty(i.items))
    : resolvedExtras;

  const [selectedOptions, setOptions] = useState({});
  const [selectedExtras, setExtras] = useState({});
  const [validAdditionsForQtd, setValidAdditionsForQtd] = useState(true);
  
  const calculateQtd = (additionItems = {}) => {
    if (isEmpty(additionItems)) {
      return 0;
    }
    
    return Object.values(additionItems).reduce((acc, next = {}) => {
      const nextTotal = (Object.values(next) || []).reduce((acm, nxt) => {
        acm += nxt.qtd;
        return acm;
      }, 0);
      acc += nextTotal;
      return acc;
    }, 0)
  };
  
  useEffect(() => {
    if (!additionsCrossCategoryMaxForQtd || (isEmpty(selectedExtras) && isEmpty(selectedOptions))) {
      setCanAddAddition(true);
    } else {
      const selectedExtrasAmount = calculateQtd(selectedExtras);
      const selectedOptionsAmount = calculateQtd(selectedOptions);
      const selectedAdditionsAmount = selectedExtrasAmount + selectedOptionsAmount
    
      if (selectedAdditionsAmount > additionsCrossCategoryMaxForQtd) {
        setValidAdditionsForQtd(false)
      } else {
        setValidAdditionsForQtd(true)
      }
    
      if (additionsCrossCategoryMaxForQtd && ((selectedAdditionsAmount + 1) > additionsCrossCategoryMaxForQtd)) {
        setCanAddAddition(false);
      } else {
        setCanAddAddition(true);
      }
    }
  }, [JSON.stringify(selectedExtras), JSON.stringify(selectedOptions), qtd])

  // order is valid if there are no options
  // only option qtd amount could invalidate ordering
  const [optionsState, setOptionsState] = useState(isEmpty(resolvedOptions) ? null : {});
  const [isValid, setValid] = useState(false);

  const updateOptions = (optionId, items, isOptionValid) => {
    setOptions((prevOptions) => ({ ...prevOptions, ...{ [optionId]: items } }));
    setOptionsState((prevOptionsState) => ({ ...prevOptionsState, ...{ [optionId]: isOptionValid } }));
  };

  const updateExtras = (optionId, items) => {
    setExtras({ ...selectedExtras, [optionId]: items });
  };

  const [optionsPrice, setOptionsPrice] = useState(0);
  const [extrasPrice, setExtrasPrice] = useState(0);

  const calculatePrice = (additionItems = {}) => (isEmpty(additionItems) ? 0 : (
    Object.values(additionItems).reduce((acc, next = {}) => {
      const nextTotal = (Object.values(next) || []).reduce((acm, nxt) => {
        acm += nxt.qtd * nxt.unitPrice;
        return acm;
      }, 0);
      acc += nextTotal;
      return acc;
    }, 0)
  ));

  const calculateValid = (additionItems = {}) => (isEmpty(additionItems) ? true : (
    Object.values(additionItems).reduce((acc, b) => {
      acc = acc && b;
      return acc;
    }, true)
  ));
  
  const calculateEmpty = (additionItems = {}) => {
    if (isEmpty(additionItems)) {
      return true;
    }
  
    return Object.values(additionItems).reduce((acc, values = {}) => {
      const hasQtd = Object.values(values).some(val => val.qtd > 0 && !val.base)
      acc = acc && (!hasQtd);
      return acc;
    }, true)
  };

  useEffect(() => {
    setOptionsPrice(calculatePrice(selectedOptions));
  }, [JSON.stringify(selectedOptions)]);

  useEffect(() => {
    setExtrasPrice(calculatePrice(selectedExtras));
  }, [JSON.stringify(selectedExtras)]);

  useEffect(() => {
    setValid(calculateValid(optionsState));
  }, [JSON.stringify(optionsState)]);

  const onAddOrderItem = () => {
    addOrderItem({ notes, code, qtd, selectedOptions, selectedExtras });
  };

  return (
    <>
      <div style={{
        height: "100%", overflow: "auto", background: palette.grayscale["100"],
      }}>
        <div style={{
          paddingLeft: 20, paddingRight: 20, paddingTop: 20, paddingBottom: 20 }}>
          <div className={classes.layout}>
            {thumbnailUrl && (
              <LazyLoadImage
                alt={name}
                src={thumbnailUrl}
                className={clsx(classes.img, { [classes.imgError]: imgError })}
                onError={() => setImgError(true)}
              />
            )}
            <div className={classes.text}>
              <div className={classes.name}>
                <DisplayTitle>{name}</DisplayTitle>
              </div>
              {!isEmpty(tags) && (
                <div className={classes.tags}>
                  {tags.map((tag) => (
                    <div className={classes.tag} key={tag.identifier}>
                      <img className={classes.tagImg} src={`${publicRuntimeConfig.basePath}/icons/tags/${tag.identifier}.svg`} alt={tag.identifier} />
                      <Paragraph>{tag.label}</Paragraph>
                    </div>
                  ))}
                </div>
              )}
              {description && (
                <div className={classes.description}>
                  <Paragraph>{description}</Paragraph>
                </div>
              )}
              {volume && (
                <div className={classes.description}>
                  <Paragraph>{volume}</Paragraph>
                </div>
              )}
              <div className={classes.pricing}>
                <div className={classes.price}>
                  <Paragraph>{`${!isEmpty(resolvedOptions) ? `${t('menu-item-price-from')} ` : ''}${salePrice || unitPrice}€`}</Paragraph>
                </div>
                {salePrice && (
                  <div className={classes.regularPrice}>
                    <Paragraph>{`${unitPrice}€`}</Paragraph>
                  </div>
                )}
              </div>
            </div>
          </div>
          {!isEmpty(resolvedOptions) && (
            <div>
              <div>
                {resolvedOptions.map((option) => (
                  <MenuItemAddition
                    key={option.id}
                    {...option}
                    update={updateOptions}
                    readOnly={readOnly}
                    open={showMenuItemAdditionElementDetails}
                    limitReached={!canAddAddition}
                  />
                ))}
              </div>
            </div>
          )}
          {!isEmpty(resolvedExtras) && (
            <div>
              <div>
                {resolvedExtras.map((extra) => (
                  <MenuItemAddition
                    key={extra.id}
                    {...extra}
                    update={updateExtras}
                    readOnly={readOnly}
                    open={showMenuItemAdditionElementDetails}
                    limitReached={!canAddAddition}
                  />
                ))}
              </div>
            </div>
          )}
          {!isEmpty(remarks) && (
            <div className={classes.remarks}>
              <div className={classes.remarksHeader}>
                <TitleContainer divider>
                  <Title weight="medium">
                    {t('order-item-details-remarks-section-title')}
                  </Title>
                  <div className={classes.remarksDescription}>
                    <Paragraph>
                      {t('order-item-details-remarks-section-descriptions')}
                    </Paragraph>
                  </div>
                  <div className={classes.remarkList}>
                    <ul>
                      {remarks.map(({ id, description: remarkDescription }) => (
                        <li className={classes.remarkDescription} key={id}>
                          <Paragraph key={id}>
                            {remarkDescription}
                          </Paragraph>
                        </li>
                      ))}
                    </ul>
                  </div>
                </TitleContainer>
              </div>
            </div>
          )}
          {!readOnly && showNotes && (
            <div className={classes.notes}>
              <div className={classes.notesHeader}>
                <TitleContainer divider>
                  <Title weight="medium">
                    {t('order-item-details-notes-section-title')}
                  </Title>
                  <div className={classes.notesDescription}>
                    <Paragraph>
                      {t('order-item-details-notes-section-descriptions')}
                    </Paragraph>
                  </div>
                </TitleContainer>
              </div>
              <div className={classes.notesInput}>
                <Field
                  placeholder={t('order-item-details-notes-section-notes-filed-placeholder')}
                  name="note"
                  value={notes}
                  onChange={updateNotes}
                  variant="filled"
                  id="leviee-welcome-name-input"
                  autoComplete="off"
                />
              </div>
            </div>
          )}
        </div>
      </div>
      <div className={classes.orderActionLayout} style={{
        background: palette.grayscale["100"],
        borderBottomLeftRadius: 20,
        borderBottomRightRadius: 20,
        borderTop: readOnly ? null : `1px solid ${palette.grayscale.divider}`,
        paddingBottom: readOnly ? 20 : 0
      }}>
        {!readOnly && !ongoing && (
          <div className={classes.orderQtdLayout}>
            <Paragraph>
              {t('order-item-details-quantity-label')}
            </Paragraph>
            <QuantityButton counter={qtd} onMore={onMore} onLess={onLess} />
          </div>
        )}
        {!readOnly && (
          <div style={{ paddingLeft: 8, paddingRight: 8, paddingBottom: 4, paddingTop: 4 }}>
            <MenuItemDetailsAction
              amount={ongoing ? null : (unitPrice * qtd) + (optionsPrice * qtd) + (extrasPrice * qtd)}
              onClick={onAddOrderItem}
              // disable if qtd is 0, is not valid according to extra/options validation constraints or
              // no options/extras are selected for ongoing item (new round ordering)
              disabled={!qtd || !isValid || (ongoing && calculateEmpty(selectedOptions) && calculateEmpty(selectedExtras)) || !validAdditionsForQtd}
            />
          </div>
        )}
      </div>
      {!!isMenuItemAdditionElementDetailsOpen && (
        <Modal
          open={!!isMenuItemAdditionElementDetailsOpen}
          onClose={hideMenuItemAdditionElementDetails}
          maxWidth={false}
          PaperProps={{ style: { background: "transparent",  ...shadows.large, width: 500, maxWidth: "95%", margin: 0 } }}
        >
          <ModalBar onClose={hideMenuItemAdditionElementDetails} />
          <div style={{ height: "100%", overflow: "auto", background: palette.grayscale["100"], borderBottomLeftRadius: 20,
            borderBottomRightRadius: 20, }}>
            <div style={{ paddingLeft: 20, paddingRight: 20, paddingTop: 20, paddingBottom: 20 }}>
              <MenuItemAdditionElementDetails {...isMenuItemAdditionElementDetailsOpen} />
            </div>
            <div style={{
              paddingTop: 16, paddingBottom: 16, paddingLeft: 20, paddingRight: 20,
              display: "flex",
              flexDirection: "row",
              justifyContent: "flex-end",
              alignItems: "center",
              borderTop: `1px solid ${palette.grayscale.divider}`,
              background: palette.grayscale["100"]
            }}>
              <ButtonBase
                onClick={hideMenuItemAdditionElementDetails}
                disableRipple
                disableTouchRipple
                style={{
                  paddingTop: 12,
                  paddingBottom: 12,
                  paddingLeft: 24,
                  paddingRight: 24,
                  background: palette.primary["500"],
                  borderRadius: 12
                }}
              >
                <Typography style={{ ...typography.body.medium, color: palette.grayscale["100"] }}>
                  {t('close')}
                </Typography>
              </ButtonBase>
            </div>
          </div>
        </Modal>
      )}
    </>
  );
};

export default withTranslation('common')(MenuItemDetails);
