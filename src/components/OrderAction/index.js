import React from 'react';
import { ButtonContainer } from '../Containers';
// eslint-disable-next-line import/named
import { withTranslation } from '../../../i18n';
import { MainButton } from '../Buttons';
import { googleTags } from '../../../gtm';
import shadows from "../../../styles/shadows";
import palette from "../../../styles/palette";
import {Typography} from "@material-ui/core";
import typography from "../../../styles/typography";
import ButtonBase from "@material-ui/core/ButtonBase";

const OrderAction = ({ t, onClick, loading, labelKey, ...otherProps }) => {
  const { disabled } = otherProps;
  delete otherProps.tReady;

  if (loading) {
    return null;
  }
  
  return (
    <div style={{
      position: "absolute",
      width: "100%",
      bottom: 24,
      paddingLeft: 16,
      paddingRight: 16
    }}>
      <div style={{
        maxWidth: 400,
        margin: "0 auto",
        paddingTop: 16,
        paddingLeft: 16,
        paddingRight: 16,
        paddingBottom: 12,
        backgroundColor: palette.grayscale["800"],
        borderRadius: 12,
      }}>
        <div style={{ paddingBottom: 16 }}>
          <Typography style={{ ...typography.body.medium, color: palette.grayscale.white }}>
            {t('your-order')}
          </Typography>
          <Typography style={{ ...typography.body.regular, color: palette.transparency.light["80"] }}>
            {t('you-have-an-order-in-progress-at-restaurant', { name })}
          </Typography>
        </div>
        <div style={{
          borderTop: `1px dashed ${palette.transparency.light["20"]}`,
          paddingTop: 12,
          display: "flex",
          justifyContent: "flex-end",
          width: "100%"
        }}>
          <ButtonBase
            onClick={onClick}
            disableRipple
            disableTouchRipple
            loading={loading}
            disabled={loading || disabled}
            id={googleTags.dineOut.orderBtn.id}
            style={{
              paddingTop: 12,
              paddingBottom: 12,
              paddingLeft: 24,
              paddingRight: 24,
              background: palette.primary["500"],
              borderRadius: 12
            }}
          >
            <Typography style={{ ...typography.body.medium, color: palette.grayscale["100"] }}>
              {!loading && t(labelKey)}
            </Typography>
          </ButtonBase>
        </div>
      </div>
    </div>
  )
};

export default withTranslation('common')(OrderAction);
