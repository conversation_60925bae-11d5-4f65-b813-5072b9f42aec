import React, { useEffect, useState } from 'react';

const ButtonTimer = ({ children, starting, limit = 15 }) => {
  const [label, setLabel] = useState(null);
  const [disabled, setDisabled] = useState(false);

  let timer;

  const zeroPad = (num, places) => String(num).padStart(places, '0');

  useEffect(() => {
    const diffInMilliSeconds = new Date() - new Date(starting);
    let minutes = Math.floor(diffInMilliSeconds / 60000);
    // eslint-disable-next-line no-bitwise
    let seconds = ~~((diffInMilliSeconds % 60000) / 1000);

    if (minutes < limit) {
      minutes = limit - 1 - minutes;
      seconds = 59 - seconds;
      setDisabled(true);
      setLabel(`${zeroPad(minutes, 2)}:${zeroPad(seconds, 2)}`);
      timer = setInterval(() => {
        if (minutes === 0 && seconds === 0) {
          clearInterval(timer);
          setLabel(null);
          setDisabled(false);
        } else {
          if (seconds > 0) {
            seconds -= 1;
          } else {
            minutes -= 1;
            seconds = 59;
          }
          setLabel(`${zeroPad(minutes, 2)}:${zeroPad(seconds, 2)}`);
        }
      }, 1000);
    }
    return () => clearInterval(timer);
  }, [starting]);

  return (
    <>
      {children(label, disabled)}
    </>
  );
};

export default ButtonTimer;
