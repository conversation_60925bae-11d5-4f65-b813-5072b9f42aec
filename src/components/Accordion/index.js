import React, { useState } from 'react';
import { withStyles } from '@material-ui/core/styles';
import MuiAccordion from '@material-ui/core/Accordion';
import MuiAccordionSummary from '@material-ui/core/AccordionSummary';
import MuiAccordionDetails from '@material-ui/core/AccordionDetails';
import { Header, Paragraph } from '../Text';
import { ArrowBackIcon } from '../Icons';

const StyledAccordion = withStyles({
  root: {
    background: 'transparent',
    boxShadow: 'none',
    '&:not(:last-child)': {
      borderBottom: 0,
    },
    '&:before': {
      display: 'none',
    },
    '&$expanded': {
      margin: 'auto',
    },
  },
  expanded: {},
})(MuiAccordion);
//
const StyledAccordionSummary = withStyles({
  root: {
    borderBottom: '1px solid rgba(0, 0, 0, .125)',
    marginBottom: -1,
    minHeight: 78,
    '&$expanded': {
      minHeight: 78,
      borderBottom: 0
    },
    '&  h4': {
      marginLeft: 20
    }
  },
  content: {
    '&$expanded': {
      margin: '12px 0',
    },
  },
  expanded: {},
})(MuiAccordionSummary);
//
const StyledAccordionDetails = withStyles((theme) => ({
  root: {
    borderBottom: '1px solid rgba(0, 0, 0, .125)',
    padding: theme.spacing(2),
  },
}))(MuiAccordionDetails);

const Accordion = () => {
  const [expanded, setExpanded] = useState('1');

  const handleExpand = (panel) => () => {
    setExpanded(panel);
  };

  return (
    <div>
      <StyledAccordion square expanded={expanded === '1'} onChange={handleExpand('1')}>
        <StyledAccordionSummary aria-controls="panel1d-content" id="panel1d-header">
          <ArrowBackIcon />
          <Header>Stay close to your customers</Header>
        </StyledAccordionSummary>
        <StyledAccordionDetails>
          <Paragraph>
            Know your customers and stay in touch with them via our deep customer
            analytics, marketing tool and loyalty program tool
          </Paragraph>
        </StyledAccordionDetails>
      </StyledAccordion>
      <StyledAccordion square expanded={expanded === '2'} onChange={handleExpand('2')}>
        <StyledAccordionSummary aria-controls="panel1d-content" id="panel1d-header">
          <ArrowBackIcon />
          <Header>Increase Revenue</Header>
        </StyledAccordionSummary>
        <StyledAccordionDetails>
          <Paragraph>
            Know your customers and stay in touch with them via our deep customer
            analytics, marketing tool and loyalty program tool
          </Paragraph>
        </StyledAccordionDetails>
      </StyledAccordion>
    </div>
  );
};

export default Accordion;
