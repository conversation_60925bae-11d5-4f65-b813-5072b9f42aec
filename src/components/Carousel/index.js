// Import Swiper styles
import 'swiper/swiper-bundle.min.css';

import React from 'react';
import { Swiper, SwiperSlide } from 'swiper/react';
import SwiperCore, { Mousewheel, A11y, Controller, Pagination } from 'swiper';
import useStyles from './styles';

SwiperCore.use([Mousewheel, Controller, Pagination, A11y]);

const Carousel = ({ items = [], slideClassName }) => {
  const classes = useStyles();

  return (
    <div className={classes.wrapper}>
      <Swiper
        spaceBetween={12}
        slidesPerView="auto"
        centeredSlides
        centeredSlidesBounds
        mousewheel={{
          enabled: true,
          forceToAxis: true
        }}
        cssMode
      >
        {items.map((item) => (
          <SwiperSlide className={slideClassName} key={item.key}>
            {item}
          </SwiperSlide>
        ))}
      </Swiper>
    </div>
  );
};

export default Carousel;
