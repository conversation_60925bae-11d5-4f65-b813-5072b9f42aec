import { makeStyles } from '@material-ui/core/styles';
import {colors} from '../../../styles/theme';

const useStyles = makeStyles((theme) => ({
  wrapper: {
    minHeight: '30rem',
    background: colors.leviee.greyscale.lighterGray,
    display: 'flex',
    alignItems: 'center',
    borderRadius: theme.spacing(2)
  },
  img: {
    marginBottom: theme.spacing(2)
  },
  callToActionBtn: {
    background: theme.palette.primary.main,
    color: 'rgba(255, 255, 255, 0.84)',
    marginTop: theme.spacing(2)
  }
}));

export default useStyles;
