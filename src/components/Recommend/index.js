import React from 'react';
import Container from '@material-ui/core/Container';
import Typography from '@material-ui/core/Typography';
import Box from '@material-ui/core/Box';
import Img from '../Img';
import Button from '../Buttons/Button';
import useStyles from './styles';

const Recommend = () => {
  const classes = useStyles();

  return (
    <Container maxWidth="lg">
      <div className={classes.wrapper}>
        <Box
          pt={8}
          pb={8}
          display="flex"
          flexDirection="column"
          justifyContent="space-between"
          alignItems="center"
          maxWidth={600}
          margin="0 auto"
        >
          <Img src="/assets/illustrations/restaurant-not-found.png" alt="" className={classes.img} />
          <Typography gutterBottom>Couldn’t find a Restaurant</Typography>
          <Typography gutterBottom color="textSecondary" align="center">
            If you couldn’t find the Restaurant you were looking for,
            feel free to recommend it. We’ll work hard to get it on Leviee
          </Typography>
          <Button variant="contained" className={classes.callToActionBtn}>Recommend</Button>
        </Box>
      </div>
    </Container>
  );
};

export default Recommend;
