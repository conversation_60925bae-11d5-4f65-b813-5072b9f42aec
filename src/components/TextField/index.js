import React, { useRef } from 'react';
import MuiTextField from '@material-ui/core/TextField';
import useStyles, { useInputLabelStyles, useInputStyles } from './styles';

const TextField = (props) => {
  const classes = useStyles(props);
  const inputClasses = useInputStyles(props);
  const inputLabelClasses = useInputLabelStyles();

  const { InputProps, withRef } = props;
  const myRef = useRef(null);
  const toElement = () => {
    if (!myRef || !withRef) return;
    // Get element coords from Ref
    // eslint-disable-next-line no-undef
    const element = myRef.current.getBoundingClientRect().top + window.scrollY - 200;

    // eslint-disable-next-line no-undef
    window.scroll({
      top: element,
      behavior: 'smooth'
    });
  };

  const toTop = () => {
    if (!myRef || !withRef) return;
    // eslint-disable-next-line no-undef
    window.scroll({
      top: 0,
      behavior: 'smooth'
    });
  };

  return (
    <div
      ref={withRef ? myRef : null}
      onFocus={toElement}
      onBlur={toTop}
    >
      <MuiTextField
        {...props}
        classes={classes}
        InputLabelProps={{ classes: inputLabelClasses, shrink: true }}
        InputProps={{ classes: inputClasses, disableUnderline: true, ...InputProps }}
      />
    </div>
  );
};

export default TextField;
