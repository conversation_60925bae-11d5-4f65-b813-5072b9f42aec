import { makeStyles } from '@material-ui/core/styles';
import { colors, fontStyles } from '../../../styles/theme';

const useStyles = makeStyles((theme) => ({
  root: {
    width: '100%',
    borderRadius: 6,
    backgroundColor: colors.leviee.greyscale.lightestGray,
    '& input': {
      padding: (props) => theme.spacing(props.label ? '28px' : '8px', '12px', '8px', '12px')
    }
  }
}));

export const useInputStyles = makeStyles((theme) => ({
  root: {
    width: '100%',
    overflow: 'hidden',
    borderRadius: 6,
    backgroundColor: colors.leviee.greyscale.lightestGray,
    transition: theme.transitions.create(['border-color', 'box-shadow']),
    border: '1px solid transparent',
    ...fontStyles.paragraphRegular,
    '&:hover': {
      backgroundColor: colors.leviee.greyscale.lightestGray,
      // border: `1px solid ${colors.leviee.greyscale.darkGray}`,
    },
    '&$focused': {
      backgroundColor: colors.leviee.greyscale.lightestGray,
      // boxShadow: `${fade(theme.palette.primary.main, 0.25)} 0 0 0 2px`,
      border: `1px solid ${colors.leviee.greyscale.darkGray}`,
    },
  },
  focused: {
    backgroundColor: colors.leviee.greyscale.lightestGray,
  },
  inputAdornedStart: {
    '&&': {
      paddingLeft: 0
    }
  }
}));

export const useInputLabelStyles = makeStyles(() => ({
  root: {
    color: colors.leviee.greyscale.midGray,
    '&:hover': {
      color: colors.leviee.greyscale.midGray,
    },
    '&$focused': {
      color: colors.leviee.main.dark,
      '&&': {
        color: colors.leviee.main.dark
      }
    },
  },
  focused: {
    '&&': {
      color: colors.leviee.main.dark
    }
  }
}));

export default useStyles;
