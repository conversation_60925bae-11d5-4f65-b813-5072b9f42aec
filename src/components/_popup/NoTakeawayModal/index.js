import React from "react";
import { withTranslation } from '../../../../i18n';
import Modal from "../Modal";
import ModalBar from "../../_navigation/ModalBar";
import shadows from "../../../../styles/shadows";
import palette from "../../../../styles/palette";
import {Typography} from "@material-ui/core";
import typography from "../../../../styles/typography";
import ButtonBase from "@material-ui/core/ButtonBase";
import {googleTags} from "../../../../gtm";
import Img from "../../Img";

const NoTakeawayModal = ({ t, open, onClose, name }) => {
  
  return (
    <Modal
      open={open}
      onClose={onClose}
      maxWidth={false}
      PaperProps={{ style: { background: "transparent",  ...shadows.large, width: 500, maxWidth: "95%", margin: 0 } }}
    >
      <ModalBar title={t('activity-title-pickup-disabled')} onClose={onClose} />
      <div style={{ height: "100%", overflow: "auto", background: palette.grayscale["200"] }}>
        <div style={{ paddingLeft: 20, paddingRight: 20, paddingTop: 20, paddingBottom: 20 }}>
          <div style={{
            alignItems: "center",
            justifyContent: "center",
            textAlign: "center",
            background: palette.grayscale["100"],
            width: "100%",
            padding: 12,
            ...shadows.base,
            borderRadius: 12,
          }}>
            <div style={{ marginTop: 24 }}>
              <Img src="/assets/illustrations/restaurant-not-found.png" alt="" />
            </div>
            <div style={{ marginTop: 24 }}>
              <Typography style={{ ...typography.body.medium }}>
                {t('pickup-disabled-title')}
              </Typography>
            </div>
            <div style={{ marginTop: 8 }}>
              <Typography style={{ ...typography.body.regular }}>
                {t('pickup-disabled-message', { name })}
              </Typography>
            </div>
          </div>
        </div>
      </div>
      <div style={{
        paddingTop: 16, paddingBottom: 16, paddingLeft: 20, paddingRight: 20,
        borderBottomLeftRadius: 20,
        borderBottomRightRadius: 20,
        display: "flex",
        flexDirection: "row",
        justifyContent: "flex-end",
        alignItems: "center",
        borderTop: `1px solid ${palette.grayscale.divider}`,
        background: palette.grayscale["100"]
      }}>
        <ButtonBase
          onClick={onClose}
          disableRipple
          disableTouchRipple
          id={googleTags.dineOut.orderBtn.id}
          style={{
            paddingTop: 12,
            paddingBottom: 12,
            paddingLeft: 24,
            paddingRight: 24,
            background: palette.primary["500"],
            borderRadius: 12
          }}
        >
          <Typography style={{ ...typography.body.medium, color: palette.grayscale["100"] }}>
            {t('close')}
          </Typography>
        </ButtonBase>
      </div>
    </Modal>
  )
}

export default withTranslation("common")(NoTakeawayModal);
