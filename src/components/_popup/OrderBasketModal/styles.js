import { makeStyles } from '@material-ui/core/styles';
import { fade } from '@material-ui/core';
import { colors, drawerModalStyle } from '../../../../styles/theme';
import palette from "../../../../styles/palette";
import shadows from "../../../../styles/shadows";

const useStyles = makeStyles((theme) => ({
  orderItemsCategoryWrapper: {
    marginTop: 16
  },
  orderItemsCategoryLayout: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center'
  },
  inProgress: {
    color: colors.leviee.secondary.yellow
  },
  done: {
    color: colors.leviee.main.green
  },
  orderItemsCategoryLabel: {},
  orderItemsCategoryAmount: {},
  drawerModal: {
    background: theme.palette.common.white,
    position: 'absolute',
    top: 200,
    width: '100%',
    maxWidth: '700px',
    borderTopLeftRadius: 8,
    borderTopRightRadius: 8,
    minHeight: '100%',
    bottom: 0
  },
  drawerContainer: {
    '&&': {
      ...drawerModalStyle
    }
  },
  modalWrapper: {
    paddingBottom: 20,
    background: palette.grayscale["100"],
    width: "100%",
    padding: 12,
    ...shadows.base,
    borderRadius: 12,
  },
  orderItemWrapper: {
    marginTop: 8,
    '& > div:not(:first-child)': {
      marginTop: 8
    }
  },
  userTotal: {
    marginTop: theme.spacing(3),
    marginBottom: theme.spacing(2)
  },
  totalRow: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between'
  },
  vatDisclaimer: {
    color: fade(colors.leviee.main.dark, 0.48)
  }
}));

export default useStyles;
