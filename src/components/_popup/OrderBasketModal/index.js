import React, {useEffect} from "react";
import { withTranslation } from '../../../../i18n';
import Modal from "../Modal";
import ModalBar from "../../_navigation/ModalBar";
import shadows from "../../../../styles/shadows";
import palette from "../../../../styles/palette";
import {useDispatch, useSelector} from "react-redux";
import {orderSelectors, uiSelectors} from "../../../../redux/selectors";
import {Typography} from "@material-ui/core";
import typography from "../../../../styles/typography";
import ButtonBase from "@material-ui/core/ButtonBase";
import {googleTags} from "../../../../gtm";
import isEmpty from "../../../utilities/isEmpty";
import useStyles from "./styles";
import {orderActions} from "../../../../redux/actions";
import {Display<PERSON>it<PERSON>Container, TitleContainer} from "../../Containers";
import {Skeleton} from "@material-ui/lab";
import {Paragraph, SmallParagraph, Title} from "../../Text";
import MenuItem from "../../MenuItem";
import AvatarItem from "../../AvatarItem";
import {ReceiptCompletedIcon, ReceiptIcon, ReceiptInProgressIcon} from "../../Icons";
import OrderItem from "../../OrderItem";
import ListItem from "../../ListItem";
import clsx from "clsx";
import ConfirmOrderAction from "../../ConfirmOrderAction";
import PlaceOrderAction from "../../PlaceOrderAction";
import formatNumber from "../../../utilities/formatNumber";

const OrderItemsCategory = ({ icon, text, amount = 0, status }) => {
  const classes = useStyles();
  
  return (
    <div className={classes.orderItemsCategoryWrapper}>
      <div className={classes.orderItemsCategoryLayout}>
        <ListItem icon={icon} label={text} labelStyles={clsx(classes.orderItemsCategoryLabel, classes[status])} />
        <div className={clsx(classes.orderItemsCategoryAmount, classes[status])}>
          <Paragraph>{`${amount.toFixed(2)}€`}</Paragraph>
        </div>
      </div>
    </div>
  );
};

const OrderBasketModal = ({ t, open, onClose, onRemove, onConfirmOrderItems, onOpenReceipt, onCheckout }) => {
  const classes = useStyles();
  const dispatch = useDispatch();
  const { loading = false } = useSelector(uiSelectors.getCurrentOrder);
  const { order = {} } = useSelector(orderSelectors.getOrder);
  const resolvedAmount = (order.totalDue || 0).toFixed(2);
  const { summary = {} } = useSelector(orderSelectors.getOrderSummary);
  const { receipts = [] } = summary;
  const { participant = {} } = useSelector(orderSelectors.getParticipant);
  const { customerId: currentCustomerId } = (participant ?? {});
  const isPickup = order.type === 'PICKUP'

  
  useEffect(() => {
    if (!isEmpty(order)) {
      dispatch(orderActions.getOrderForReceipt(order.id))
      if (order.type === 'DINE_IN') {
        dispatch(orderActions.pullOrderSummary(order.id));
      } else {
        dispatch(orderActions.getOrderSummary(order.id));
      }
    }
    return () => {
      dispatch(orderActions.stopPollOrderSummary());
    };
  }, []);
  
  let customerReceipts = receipts.filter((r) => !!r.customer);
  if (!currentCustomerId && (order.type === 'PICKUP' || order.type === 'DELIVERY')) {
    customerReceipts = receipts.filter((r) => !r.offline);
  }
  
  const waiterReceipt = receipts.find((i) => isEmpty(i.customer)); // && !!i.offline);
  
  const canRemove = (customer, itemId) => {
    // if current customer owns the order item
    if (!isEmpty(customer)) {
      if (currentCustomerId === customer.id) {
        return () => onRemove(itemId);
      }
    }
    
    // if current user has no customer id and order item has no customer id
    if (!currentCustomerId) {
      if (isEmpty(customer)) {
        return () => onRemove(itemId);
      }
    }
    
    return null;
  };
  
  const canOrder = (item) => {
    const canBeOrdered = ["CANCELLED", "PAID", "REMOVED", "UNCONFIRMED"].indexOf(item.status) === -1
    if (order.type === 'DINE_IN' && !isEmpty(item) && item.ongoing && currentCustomerId === order.customerId && canBeOrdered) {
      return () => dispatch(orderActions.setItem(item));
    }
    return null;
  };
  
  const canViewMenuItem = (item) => {
    const canBeOrdered = ["CANCELLED", "PAID", "REMOVED", "UNCONFIRMED"].indexOf(item.status) === -1
    if (order.type === 'DINE_IN' && !isEmpty(item) && item.ongoing && canBeOrdered) {
      return () => dispatch(orderActions.setItem(item));
    }
    return null;
  };
  
  const { status, totalDue, mergedInto, type } = order;
  
  const { discount = {} } = useSelector(orderSelectors.getDiscount);
  const { percentage: discountPercentage } = (discount || {})
  
  const { quantity: unconfirmedQtd, total: unconfirmedTotalWithoutDiscount, discountTotal: unconfirmedTotalDiscount } = useSelector(orderSelectors.getUnconfirmed);
  const unconfirmedTotal = unconfirmedTotalWithoutDiscount - unconfirmedTotalDiscount
  const { quantity: confirmedQtd } = useSelector(orderSelectors.getConfirmed);
  
  const { approvalId, customer = {} } = participant;
  const participantIsApproved = !!approvalId;
  
  const hasUnconfirmedItems = unconfirmedQtd > 0;
  
  const getDineInActions = () => (
    <div>
      {!participantIsApproved && (
        <ButtonBase
          onClick={onClose}
          disableRipple
          disableTouchRipple
          id={googleTags.dineOut.orderBtn.id}
          style={{
            paddingTop: 12,
            paddingBottom: 12,
            paddingLeft: 24,
            paddingRight: 24,
            background: palette.primary["500"],
            borderRadius: 12
          }}
        >
          <Typography style={{ ...typography.body.medium, color: palette.grayscale["100"] }}>
            {t('close')}
          </Typography>
        </ButtonBase>
      )}
      {participantIsApproved && hasUnconfirmedItems && (
        <div>
          <ConfirmOrderAction
            amount={unconfirmedTotal}
            count={unconfirmedQtd}
            onClick={onConfirmOrderItems}
            orderType={type}
          />
        </div>
      )}
      {participantIsApproved && !hasUnconfirmedItems && (
        <div>
          <ButtonBase
            onClick={onClose}
            disableRipple
            disableTouchRipple
            id={googleTags.dineOut.orderBtn.id}
            style={{
              paddingTop: 12,
              paddingBottom: 12,
              paddingLeft: 24,
              paddingRight: 24,
              borderRadius: 12
            }}
          >
            <Typography style={{ ...typography.body.medium, color: palette.grayscale["800"] }}>
              {t('close')}
            </Typography>
          </ButtonBase>
          <ButtonBase
            onClick={onOpenReceipt}
            disableRipple
            disableTouchRipple
            id={googleTags.dineOut.orderBtn.id}
            style={{
              paddingTop: 12,
              paddingBottom: 12,
              paddingLeft: 24,
              paddingRight: 24,
              background: palette.primary["500"],
              borderRadius: 12,
              marginLeft: 8
            }}
          >
            <Typography style={{ ...typography.body.medium, color: palette.grayscale.white }}>
              {t('view-receipt')}
            </Typography>
          </ButtonBase>
          {/*<ProceedToPaymentAction*/}
          {/*  amount={totalDue}*/}
          {/*  count={confirmedQtd}*/}
          {/*  onClick={onOpenReceipt}*/}
          {/*/>*/}
        </div>
      )}
    </div>
  )
  
  const getTakeawayActions = () => {
    return (
      <PlaceOrderAction
        onClick={onCheckout}
        amount={unconfirmedTotal}
        count={unconfirmedQtd}
        description={t('order-current-order-order-items-pickup-modal-description')}
      />
    )
  }
  
  const getNestedModificationTime = (i) => {
    if (isEmpty(i)) {
      return null;
    }
    
    const { ongoing, nestedOrderItems } = i;
    
    if (ongoing && !isEmpty(nestedOrderItems)) {
      const customerNestedOrderItems = nestedOrderItems.filter(it => !it.offline)
      
      if (isEmpty(customerNestedOrderItems)) {
        return null
      }
      
      return customerNestedOrderItems[0].modificationTime
    }
    
    return null;
  }
  
  return (
    <Modal
      open={open}
      onClose={onClose}
      PaperProps={{ style: { background: "transparent",  ...shadows.large, width: 500, maxWidth: "95%", margin: 0 } }}
    >
      <ModalBar title={`${t('activity-title-current-order')} 📝`} onClose={onClose} />
      <div style={{ height: "100%", overflow: "auto", background: palette.grayscale["200"] }}>
        <div style={{ paddingLeft: 20, paddingRight: 20, paddingTop: 20, paddingBottom: 20 }}>
          <div className={classes.modalWrapper}>
            {loading && (
              <div>
                <TitleContainer>
                  <Skeleton width="200px" height="18px" style={{ transform: 'none' }}>
                    <Title weight="medium">
                      -----
                    </Title>
                  </Skeleton>
                </TitleContainer>
                <MenuItem loading />
              </div>
            )}
            {customerReceipts.map(({ confirmed, unconfirmed, customer = {} }, index) => (
              <div key={!isEmpty(customer) ? customer.id : 'guest'}>
                <div>
                  {!isEmpty(customer) && (customer.firstName || customer.lastName) && (
                    <AvatarItem
                      firstName={customer.firstName ?? ''}
                      lastName={customer.lastName ?? ''}
                    />
                  )}
                  {!isEmpty(unconfirmed) && (
                    <>
                      <OrderItemsCategory
                        icon={<ReceiptInProgressIcon />}
                        text={t('order-current-order-drawer-unconfirmed-category-label')}
                        amount={unconfirmed.total}
                        status="inProgress"
                      />
                      <div className={classes.orderItemWrapper}>
                        {unconfirmed.items
                          .map((i) => (
                            <OrderItem
                              key={i.id}
                              unitPrice={i.unitPrice}
                              name={i.name}
                              qtd={i.qtd}
                              onRemove={canRemove(customer, i.id)}
                              {...i}
                            />
                          ))}
                      </div>
                    </>
                  )}
                  {!isEmpty(confirmed) && (
                    <>
                      <OrderItemsCategory
                        icon={<ReceiptCompletedIcon />}
                        text={t('order-current-order-drawer-confirmed-category-label')}
                        amount={confirmed.total}
                        status="done"
                      />
                      <div className={classes.orderItemWrapper}>
                        {confirmed.items
                          .map((i) => (
                            <OrderItem
                              key={i.id}
                              unitPrice={i.unitPrice}
                              name={i.name}
                              qtd={i.qtd}
                              {...i}
                            />
                          ))}
                      </div>
                      <div className={classes.userTotal}>
                        <OrderItemsCategory
                          icon={<ReceiptIcon />}
                          text={t('order-current-order-drawer-users-total-label', { name: customer.firstName })}
                          amount={confirmed ? confirmed.total : 0}
                        />
                      </div>
                    </>
                  )}
                </div>
                {receipts.length - 1 > index && <TitleContainer divider withText />}
              </div>
            ))}
            {!isEmpty(waiterReceipt) && (order.type === 'DINE_IN') && (
              <div className={classes.waiterReceipt}>
                <div>
                  {!isEmpty(waiterReceipt.unconfirmed) && (
                    <>
                      <OrderItemsCategory
                        icon={<ReceiptInProgressIcon />}
                        text={t('order-current-order-drawer-unconfirmed-category-label')}
                        amount={waiterReceipt.unconfirmed.total}
                        status="inProgress"
                      />
                      <div className={classes.orderItemWrapper}>
                        {waiterReceipt.unconfirmed.items
                          .map((i) => (
                            <OrderItem
                              key={i.id}
                              unitPrice={i.unitPrice}
                              name={i.name}
                              qtd={i.qtd}
                              {...i}
                            />
                          ))}
                      </div>
                    </>
                  )}
                  {!isEmpty(waiterReceipt.confirmed) && (
                    <>
                      <OrderItemsCategory
                        icon={<ReceiptCompletedIcon />}
                        text={t('order-current-order-drawer-confirmed-category-label')}
                        amount={waiterReceipt.confirmed.total}
                        status="done"
                      />
                      <div className={classes.orderItemWrapper}>
                        {waiterReceipt.confirmed.items
                          .map((i) => (
                            <>
                              <OrderItem
                                key={i.id}
                                unitPrice={i.unitPrice}
                                name={i.name}
                                qtd={i.qtd}
                                {...i}
                                onOrderOngoing={canOrder(i)}
                                onViewMenuItem={canViewMenuItem(i)}
                                nestedModificationTime={getNestedModificationTime(i)}
                              />
                              {i.ongoing && !isEmpty(i.nestedOrderItems) && i.nestedOrderItems.map((nested, index) => (
                                <OrderItem
                                  index={(i.nestedOrderItems).length - index}
                                  key={nested.id}
                                  name={nested.name}
                                  qtd={nested.qtd}
                                  {...nested}
                                />
                              ))}
                            </>
                          ))}
                      </div>
                    </>
                  )}
                </div>
              </div>
            )}
            {(resolvedAmount > 0) && (
              <>
                <TitleContainer withText divider>
                  <div>
                    <div className={classes.totalRow}>
                      <Title weight="medium">
                        {t('pickup-success-order-summary-total-label')}
                      </Title>
                      <Title weight="medium">
                        {`${resolvedAmount}€`}
                      </Title>
                    </div>
                  </div>
                </TitleContainer>
                <DisplayTitleContainer>
                  <div>
                    <div className={classes.vatDisclaimer}>
                      <SmallParagraph>
                        {t('order-summary-vat-disclaimer-label')}
                      </SmallParagraph>
                    </div>
                  </div>
                </DisplayTitleContainer>
              </>
            )}
          </div>
        </div>
        {discountPercentage && Boolean(unconfirmedTotalWithoutDiscount) && isPickup && (
          <div style={{ paddingLeft: 20, paddingRight: 20, paddingTop: 0, paddingBottom: 20 }}>
            <div style={{
              background: palette.grayscale["100"],
              width: "100%",
              padding: '20px 12px',
              ...shadows.base,
              borderRadius: 12,
            }}>
              <div style={{ display: "flex", alignItems: "center", justifyContent: "space-between" }}>
                <Typography style={{ ...typography.body.regular }}>
                  {t('percent-discount', { percent: discountPercentage })}
                </Typography>
                <Typography style={{ ...typography.body.regular }}>
                  -{formatNumber(unconfirmedTotalDiscount)}€
                </Typography>
              </div>
            </div>
          </div>
        )}
      </div>
      <div style={{
        paddingTop: 16, paddingBottom: 16, paddingLeft: 20, paddingRight: 20,
        borderBottomLeftRadius: 20,
        borderBottomRightRadius: 20,
        display: "flex",
        flexDirection: "row",
        justifyContent: "flex-end",
        alignItems: "center",
        borderTop: `1px solid ${palette.grayscale.divider}`,
        background: palette.grayscale["100"]
      }}>
        {type === "DINE_IN" && getDineInActions()}
        {type !== "DINE_IN" && getTakeawayActions()}
      </div>
    </Modal>
  )
}

export default withTranslation("common")(OrderBasketModal);
