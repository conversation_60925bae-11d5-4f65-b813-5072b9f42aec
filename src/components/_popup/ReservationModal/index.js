import React, { Fragment, useEffect, useState } from 'react';
import Container from '@material-ui/core/Container';
import moment from 'moment';
import MomentAdapter from '@material-ui/pickers/adapter/moment';
import { LocalizationProvider, StaticDateRangePicker } from '@material-ui/pickers';
import { useRouter } from 'next/router';
import ButtonBase from '@material-ui/core/ButtonBase';
import Typography from '@material-ui/core/Typography';
import { NativeSelect } from '@material-ui/core';
import axios from 'axios';
import { useSelector } from 'react-redux';
import { i18n, withTranslation } from '../../../../i18n';
import OrderAction from '../../OrderAction';
import palette from '../../../../styles/palette';
import typography from '../../../../styles/typography';
import useStyles from './styles';
import isEmpty from '../../../utilities/isEmpty';
import ModalBar from '../../_navigation/ModalBar';
import {
  cancelReservation,
  createReservation,
  getReservableTimes,
  getReservation,
  getReservationConfig
} from '../../../../redux/api';
import Loader from '../../Loader';
import { restaurantsSelectors } from '../../../../redux/selectors';
import { noop } from '../../Menu/utils';
import isValidEmail from '../../../utilities/isValidEmail';
import shadows from '../../../../styles/shadows';
import Modal from '../Modal';
// import Field from "../../Form/Field";
import Field from '../../_input/Field';
import Checkbox from '../../_toggles/Checkbox';

let request = null;

const customerData = {
  id: null,
  firstName: '',
  lastName: '',
  phone: '',
  email: '',
  address: null
};

const removeUndefined = (o) => Object.entries(o)
  .filter(([, val]) => val !== undefined && val !== null)
  .reduce((result, [key, val]) => {
    result[key] = val;
    return result;
  }, {});

const ReservationModal = ({ t, restaurantId, open, onClose }) => {
  const router = useRouter();
  const classes = useStyles();

  const inputRef = React.useRef();

  const { restaurant = {} } = useSelector(restaurantsSelectors.getRestaurant);

  const { reservationId, token, timestamp } = router.query;
  const [reservation, setReservation] = useState({});
  const [configuration, setConfiguration] = useState({});
  const { maxGuestsPerReservation = 1, allowCustomerReservations, reservationTerms = [], blockedDates = [] } = configuration;

  const [isSelectedDateBlocked, setIsSelectedDateBlocked] = useState(false);

  const [loading, setLoading] = useState(false);
  const [submitting, setSubmitting] = useState(false);

  const fetchReservationConfig = () => {
    setLoading(true);
    getReservationConfig(restaurantId).then(({ data = {} }) => {
      setConfiguration(data);
    }).catch(() => {}).finally(() => setLoading(false));
  };

  const fetchReservation = () => {
    if (reservationId) {
      setLoading(true);
      getReservation(restaurantId, reservationId, token).then(({ data = {} }) => {
        const { getReservation = {} } = (data.data ?? {});
        setReservation(getReservation);
      }).catch(() => {}).finally(() => setLoading(false));
    }
  };

  useEffect(() => {
    fetchReservationConfig();
  }, []);

  useEffect(() => {
    fetchReservation();
  }, [reservationId]);


  const [people, setPeople] = useState(1);
  const updatePeople = (count) => setPeople(count);

  const [date, setDate] = useState(new Date());
  const [isCalendarOpen, setIsCalendarOpen] = useState(false);
  const openCalendar = (e) => setIsCalendarOpen(true);
  const closeCalendar = () => setIsCalendarOpen(false);

  useEffect(() => {
    const dateIsBlocked = blockedDates.some((obj) => obj.date === moment(date).format('YYYY-MM-DD'));
    setIsSelectedDateBlocked(dateIsBlocked);
  }, [date, JSON.stringify(blockedDates)]);

  const [reservableTimes, setReservableTimes] = useState([]);
  const fetchReservableTimes = () => {
    if (request) {
      request.cancel();
    }

    request = axios.CancelToken.source();
    getReservableTimes(restaurantId, request.token, people, moment(date).format('YYYY-MM-DD')).then(({ data }) => setReservableTimes(data.slots ?? [])).catch(() => {});
  };

  const [tableId, setTableId] = useState(null);

  const [time, setTime] = useState(null);
  const [startTime, setStartTime] = useState(null);
  const updateTime = (e) => {
    const val = e.target.value;
    const slot = reservableTimes.find((s) => s.time === val);
    if (slot) {
      setTime(val);
      setStartTime(slot.fullTime);
      setTableId(slot.tableId);
    }
  };

  const [customer, setCustomer] = useState({
    id: null,
    firstName: '',
    lastName: '',
    phone: '',
    email: '',
    address: null
  });
  const updateCustomer = (e) => {
    let val = e.target.value;
    if (e.target.name === 'phone') {
      val = val.replace(/[^0-9,+]+/g, '');
    }
    setCustomer({ ...customer, [e.target.name]: val });
  };

  const [notes, setNotes] = useState('');
  const updateNotes = (e) => setNotes(e.target.value);

  const reset = () => {
    setNotes('');
    setCustomer({
      id: null,
      firstName: '',
      lastName: '',
      phone: '',
      email: '',
      address: null
    });
    setStartTime(null);
    setTime(null);
    setTableId(null);
    setReservableTimes([]);
    setDate(new Date());
    setPeople(1);
  };

  useEffect(() => {
    setTime(null);
    setStartTime(null);
    setReservableTimes([]);
    if (restaurantId) {
      fetchReservableTimes();
    }
  }, [restaurantId, people, date]);

  const [acceptedPlatformTerms, setAcceptedPlatformTerms] = useState(false);
  const [selectedTerms, setSelectedTerms] = useState([]);
  const acceptedRestaurantTerms = isEmpty(reservationTerms) || ((selectedTerms || []).length === (reservationTerms || []).length);
  const toggleTerm = (index) => {
    if (selectedTerms && selectedTerms.includes(index)) {
      setSelectedTerms(selectedTerms.filter((s) => s !== index));
    } else {
      setSelectedTerms(selectedTerms.concat([index]));
    }
  };

  const canReserve = !!people
    && !!date
    && !!time
    && !isEmpty(customer)
    && !!customer.firstName
    && !!customer.phone
    && (!isEmpty(customer.email)
    && isValidEmail(customer.email))
    && acceptedPlatformTerms
    && acceptedRestaurantTerms;

  const onSubmit = () => {
    if (!canReserve) {
      return;
    }
    setSubmitting(true);
    const reservation = { people, startTime, tableIds: [tableId], note: notes };

    parseCustomerFullName(customer);

    const customers = [customer];
    createReservation(restaurantId, reservation, customers)
      .then(({ data = {} }) => {
        const { id, customerToken } = data;
        const { pathname, query } = router;
        router.push({
          pathname,
          query: removeUndefined({
            ...query,
            reservationId: id,
            token: customerToken,
            timestamp: Date.now()
          }),
        });
      })
      .catch(() => {})
      .finally(() => setSubmitting(false));
  };

  const parseCustomerFullName = (customer) => {
    const nameChunks = (customer?.firstName || '').split(/\s+/);
    if (nameChunks.length > 1) {
      customer.firstName = nameChunks.shift();
      customer.lastName = nameChunks.join(' ');
    }
  };

  const onCancel = () => {
    cancelReservation(restaurantId, reservationId, token).then(() => {
      fetchReservation();
    }).catch(noop);
  };

  const onRedirectToReservations = () => {
    setReservation({});
    const { pathname, query } = router;
    reset();
    router.push({
      pathname,
      query: removeUndefined({
        ...query,
        reservationId: null,
        token: null,
        booking: true
      }),
    });
  };

  if (loading) {
    return <Loader />;
  }

  if (!isEmpty(configuration) && !allowCustomerReservations) {
    return null;
  }

  if (reservationId && !isEmpty(reservation)) {
    const { formattedCustomerStartLocalTime, customers = [], status, people } = reservation;
    const flatCustomers = customers.map((c) => c.firstName).join(', ');
    const { address = {}, name } = restaurant;
    const flatRestaurantAddress = `${address.street} ${address.number}, ${address.zipCode} ${address.city}`;
    const cancelled = status === 'CANCELLED';
    const canCancel = status === 'CONFIRMED' || status === 'UNCONFIRMED';
    const justCreated = !!timestamp;
    return (
      <>
        <Modal
          open={open}
          onClose={onClose}
          maxWidth={false}
          PaperProps={{ style: { background: 'transparent', ...shadows.large, width: 500, maxWidth: '95%', margin: 0 } }}
        >
          <ModalBar title={t('your-reservation-at', { restaurant: name })} onClose={onClose} />
          <div className={classes.modalWrapper} style={{ height: '100%', overflow: 'auto' }}>
            <Container>
              <div style={{ height: '100%', overflow: 'auto' }}>
                <div className={classes.background}>
                  <div className={classes.layout}>
                    <div className={classes.checkout}>
                      <div className={classes.reservationOverview}>
                        <Typography style={{ marginBottom: 16, ...typography.large.medium }}>{t(cancelled ? 'reservation-cancelled' : 'your-upcoming-reservation')}</Typography>
                        <Typography style={{ marginBottom: 32, ...typography.body.regular }}>{t(cancelled ? 'cancelled-reservation-see-details-below' : 'thank-you-for-reserving-see-details-below', { restaurant: name })}</Typography>
                        <div className={classes.reservationCard}>
                          <Typography style={{ ...typography.body.medium, paddingBottom: 12, borderBottom: `1px dashed ${palette.grayscale['400']}` }}>
                            {t('your-reservation')}
                          </Typography>
                          <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginTop: 12 }}>
                            <Typography style={{ ...typography.body.regular }}>{t('location')}</Typography>
                            <Typography style={{ ...typography.body.regular }}>{flatRestaurantAddress}</Typography>
                          </div>
                          <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginTop: 12 }}>
                            <Typography style={{ ...typography.body.regular }}>{t('date-and-time')}</Typography>
                            <Typography style={{ ...typography.body.regular }}>{formattedCustomerStartLocalTime}</Typography>
                          </div>
                          <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginTop: 12 }}>
                            <Typography style={{ ...typography.body.regular }}>{t('reserved-under')}</Typography>
                            <Typography style={{ ...typography.body.regular }}>{flatCustomers}</Typography>
                          </div>
                          <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginTop: 12 }}>
                            <Typography style={{ ...typography.body.regular }}>{t('number-of-guests')}</Typography>
                            <Typography style={{ ...typography.body.regular }}>{people}</Typography>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </Container>
          </div>
          <div style={{
            paddingTop: 16,
            paddingBottom: 16,
            paddingLeft: 20,
            paddingRight: 20,
            borderBottomLeftRadius: 20,
            borderBottomRightRadius: 20,
            display: 'flex',
            flexDirection: 'row',
            justifyContent: 'flex-end',
            width: '100%',
            borderTop: `1px solid ${palette.grayscale.divider}`,
            background: palette.grayscale['100']
          }}
          >
            {canCancel && !justCreated && (
              <ButtonBase
                onClick={onCancel}
                disableRipple
                disableTouchRipple
                disabled={submitting}
                style={{
                  paddingTop: 12,
                  paddingBottom: 12,
                  paddingLeft: 24,
                  paddingRight: 24,
                  background: submitting ? palette.grayscale['400'] : palette.primary['500'],
                  borderRadius: 12
                }}
              >
                <Typography style={{ ...typography.body.medium, color: palette.grayscale['100'] }}>
                  {t('cancel-reservation')}
                </Typography>
              </ButtonBase>
            )}
            {cancelled && (
              <ButtonBase
                onClick={onRedirectToReservations}
                disableRipple
                disableTouchRipple
                disabled={submitting}
                style={{
                  paddingTop: 12,
                  paddingBottom: 12,
                  paddingLeft: 24,
                  paddingRight: 24,
                  background: submitting ? palette.grayscale['400'] : palette.primary['500'],
                  borderRadius: 12
                }}
              >
                <Typography style={{ ...typography.body.medium, color: palette.grayscale['100'] }}>
                  {t('create-another-reservation')}
                </Typography>
              </ButtonBase>
            )}
          </div>
        </Modal>
      </>
    );
  }

  const getReservationSummary = () => {
    if (!people) {
      return <span>{t('please-select-number-of-people')}</span>;
    }

    if (!date) {
      return <span>{t('please-select-day')}</span>;
    }

    if (!time) {
      return <span>{t('please-select-time')}</span>;
    }

    const customerDataCompleted = !isEmpty(customer) && !!customer.firstName && !!customer.phone && (!isEmpty(customer.email) && isValidEmail(customer.email));

    if (!customerDataCompleted) {
      return <span>{t('please-fill-your-info')}</span>;
    }

    const termsAccepted = acceptedPlatformTerms && acceptedRestaurantTerms;

    if (!termsAccepted) {
      return <span>{t('please-accept-terms')}</span>;
    }

    if (!canReserve) {
      return <span>{t('oh-something-missing')}</span>;
    }

    const formattedDate = moment(date).format('DD MMMM');
    const formattedTime = moment(time, 'HH:mm:ss').format('HH:mm');
    return (
      <span>{`${people} ${t('guests')} • ${formattedDate} • ${formattedTime}`}</span>
    );
  };

  return (
    <>
      <Modal
        open={open}
        onClose={onClose}
        maxWidth={false}
        PaperProps={{ style: { background: 'transparent', ...shadows.large, width: 500, maxWidth: '95%', margin: 0 } }}
      >
        <ModalBar title={t('reserve-table')} onClose={onClose} />
        <div className={classes.modalWrapper} style={{ height: '100%', overflow: 'auto' }}>
          <div style={{ paddingLeft: 20, paddingRight: 20, paddingTop: 20, paddingBottom: 20 }}>
            <div style={{ height: '100%', overflow: 'auto' }}>
              <div className={classes.background}>
                <div className={classes.layout}>
                  <div className={classes.checkout}>
                    <div className={classes.reservationForm}>
                      <div className={classes.people}>
                        <Typography style={{ ...typography.body.medium }}>
                          {t('number-of-guests')}
                        </Typography>
                        <div className={classes.scrollableOptions}>
                          {[...Array(maxGuestsPerReservation)].map((_, index) => {
                            const count = index + 1;
                            const selected = people === count;
                            return (
                              <ButtonBase
                                disableRipple
                                disableTouchRipple
                                className={classes.circularCardOption}
                                style={selected ? { background: palette.primary['500'] } : null}
                                key={`guest-${count}`}
                                onClick={() => updatePeople(count)}
                                disabled={submitting}
                              >
                                <Typography style={{
                                  ...typography.body.medium,
                                  width: 20,
                                  color: selected ? palette.grayscale['100'] : 'inherit'
                                }}
                                >
                                  {count}
                                </Typography>
                              </ButtonBase>
                            );
                          })}
                        </div>
                      </div>
                      <div style={{
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'space-between',
                        position: 'relative',
                        borderBottom: `1px dashed ${palette.grayscale.divider}`,
                      }}
                      >
                        <div className={classes.date}>
                          <div className={classes.field} onClick={openCalendar}>
                            <Field
                              label={t('select-date')}
                              onChange={noop}
                              name="date"
                              value={date ? moment(date).format('DD MMMM YYYY') : t('select-date')}
                            />
                          </div>
                        </div>
                        <div className={classes.time}>
                          <div className={classes.field}>
                            <NativeSelect
                              name="time"
                              value={time}
                              onChange={updateTime}
                              disabled={isEmpty(reservableTimes) || !date || submitting}
                              inputProps={{
                                name: 'preferredTime',
                                id: 'time-selector',
                              }}
                              input={<Field label={t('select-time')} />}
                              IconComponent={() => null}
                            >
                              {isEmpty(reservableTimes) && (
                                <option key="no-values" value="">{t('no-time-slots-available')}</option>
                              )}
                              {!isEmpty(reservableTimes) && (
                                <option key="select" value="">{t('select-time')}</option>
                              )}
                              {!isEmpty(reservableTimes) && reservableTimes.map(({ time, tableId }) => (
                                <option key={time} value={time} data-tableId={tableId}>{time.slice(0, -3)}</option>
                              ))}
                            </NativeSelect>
                          </div>
                        </div>
                      </div>
                      {isSelectedDateBlocked && (
                        <div style={{ marginTop: 8 }}>
                          <Typography style={{ ...typography.body.regular, color: palette.primary['500'] }}>{t('selected-date-is-blocked')}</Typography>
                        </div>
                      )}

                      <div className={classes.customer}>
                        <div className={classes.fields}>
                          <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                            <div style={{ flex: 1 }}>
                              <Field
                                label={t('your-name')}
                                name="firstName"
                                onChange={updateCustomer}
                                value={customer.firstName}
                                placeholder={t('enter-full-name')}
                                required
                                disabled={submitting}
                              />
                            </div>
                          </div>
                          <div style={{ borderTop: `1px dashed ${palette.grayscale['400']}`, marginTop: 12 }}>
                            <div style={{ marginTop: 12 }}>
                              <Field
                                label={`${t('your-phone-number')}`}
                                name="phone"
                                type="tel"
                                // pattern="^\+?\d*$"
                                onChange={updateCustomer}
                                value={customer.phone}
                                placeholder="+49 12345"
                                disabled={submitting}
                                required
                              />
                            </div>
                            <div style={{ marginTop: 12 }}>
                              <Field
                                label={`${t('your-email')}`}
                                name="email"
                                type="text"
                                onChange={updateCustomer}
                                value={customer.email}
                                placeholder={t('email-address')}
                                disabled={submitting}
                                required
                              />
                            </div>
                          </div>
                        </div>
                      </div>
                      <div className={classes.notes}>
                        <div className={classes.field}>
                          <Field
                            label={t('add-notes')}
                            placeholder={t('write-your-note')}
                            name="notes"
                            onChange={updateNotes}
                            value={notes}
                          />
                        </div>
                      </div>
                      <div style={{ marginTop: 12 }}>
                        <div style={{ marginBottom: 12 }}>
                          <Typography style={{ ...typography.body.medium }}>
                            {t('terms-of-service')}
                          </Typography>
                        </div>
                        <ButtonBase
                          disableRipple
                          disableTouchRipple
                          onClick={() => setAcceptedPlatformTerms(!acceptedPlatformTerms)}
                          style={{
                            display: 'flex',
                            alignItems: 'flex-start',
                            justifyContent: 'flex-start',
                            marginTop: 12
                          }}
                        >
                          <Checkbox checked={acceptedPlatformTerms} />
                          <div style={{ display: 'flex' }}>
                            <Typography style={{
                              ...typography.body.medium,
                              color: palette.grayscale['600'],
                              whiteSpace: 'break-spaces',
                              textAlign: 'left',
                              marginLeft: 8
                            }}
                            >
                              {t('by-proceeding-you-agree')}
                              {' '}
                              <a
                                href="https://www.allo.restaurant/privacy-policy"
                                target="_blank"
                                style={{
                                  textDecoration: 'none',
                                  fontSize: 'inherit',
                                  whiteSpace: 'break-spaces',
                                  display: 'inline-block'
                                }}
                              >
                                <span
                                  style={{
                                    ...typography.body.medium,
                                    textDecorationLine: 'underline',
                                    whiteSpace: 'break-spaces'
                                  }}
                                >
                                  {t('privacy-policy')}
                                </span>
                              </a>
                              {' '}
                              {t('and')}
                              {' '}
                              <a
                                href="https://allo.restaurant/terms"
                                target="_blank"
                                style={{
                                  textDecoration: 'none',
                                  fontSize: 'inherit',
                                  whiteSpace: 'break-spaces',
                                  display: 'inline-block'
                                }}
                              >
                                <span style={{
                                  ...typography.body.medium,
                                  textDecorationLine: 'underline',
                                  whiteSpace: 'break-spaces'
                                }}
                                >
                                  {t('terms-and-conditions-to-agree-to')}
                                </span>
                              </a>
                            </Typography>
                          </div>
                        </ButtonBase>
                        {!isEmpty(reservationTerms) && reservationTerms.map((term, index) => (
                          <ButtonBase
                            disableRipple
                            disableTouchRipple
                            key={`term_${index}`}
                            onClick={() => toggleTerm(index)}
                            style={{
                              display: 'flex',
                              alignItems: 'center',
                              justifyContent: 'flex-start',
                              marginTop: 12
                            }}
                          >
                            <Checkbox checked={selectedTerms && selectedTerms.includes(index)} />
                            <Typography style={{ ...typography.body.medium, marginLeft: 8, color: palette.grayscale['600'] }}>
                              {term.text}
                            </Typography>
                          </ButtonBase>
                        ))}
                        <div style={{ marginTop: 12 }}>
                          <Typography style={{ ...typography.small.regular }}>
                            {t('reservation-terms-of-service-disclaimer', { restaurant: name })}
                          </Typography>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <Modal
          open={isCalendarOpen}
          onClose={closeCalendar}
          maxWidth={false}
          PaperProps={{ style: { background: 'transparent', ...shadows.large, width: 400, maxWidth: '95%', margin: 0 } }}
        >
          <div className={classes.calendar} style={{ borderRadius: 20 }}>
            <LocalizationProvider dateLibInstance={moment} dateAdapter={MomentAdapter} locale={i18n.language}>
              <StaticDateRangePicker
                disablePast
                displayStaticWrapperAs="desktop"
                reduceAnimations
                showDaysOutsideCurrentMonth
                calendars={1}
                autoOk
                value={[date, null]}
                onChange={(range) => {
                  if (range[1]) {
                    setDate(range[1]);
                    closeCalendar();
                  } else {
                    setDate(range[0]);
                    closeCalendar();
                  }
                }}
              />
            </LocalizationProvider>
          </div>
        </Modal>
        <div style={{
          paddingTop: 16,
          paddingBottom: 16,
          paddingLeft: 20,
          paddingRight: 20,
          borderBottomLeftRadius: 20,
          borderBottomRightRadius: 20,
          display: 'flex',
          flexDirection: 'row',
          justifyContent: 'space-between',
          alignItems: 'center',
          borderTop: `1px solid ${palette.grayscale.divider}`,
          background: palette.grayscale['100']
        }}
        >
          <Typography style={{ ...typography.body.medium }}>
            {getReservationSummary()}
          </Typography>
          <ButtonBase
            onClick={onSubmit}
            disableRipple
            disableTouchRipple
            disabled={!canReserve || submitting || isSelectedDateBlocked}
            style={{
              paddingTop: 12,
              paddingBottom: 12,
              paddingLeft: 24,
              paddingRight: 24,
              background: !canReserve || submitting ? palette.grayscale['400'] : palette.primary['500'],
              borderRadius: 12
            }}
          >
            <Typography style={{ ...typography.body.medium, color: palette.grayscale['100'] }}>
              {t('reserve-table')}
            </Typography>
          </ButtonBase>
        </div>
      </Modal>
    </>
  );
};

export default withTranslation('common')(ReservationModal);
