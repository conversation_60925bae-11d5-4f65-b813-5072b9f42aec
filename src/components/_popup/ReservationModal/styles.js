import { makeStyles } from '@material-ui/core/styles';
import { drawerModalStyle } from '../../../../styles/theme';
import palette from "../../../../styles/palette";
import shadows from "../../../../styles/shadows";

const useStyles = makeStyles((theme) => ({
  drawerModal: {
    background: palette.grayscale["200"],
    position: 'absolute',
    top: 20,
    width: '100%',
    maxWidth: '700px',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    minHeight: '100%',
    bottom: 0
  },
  drawerModalShorter: {
    // background: "transparent",
    position: 'absolute',
    top: 300,
    width: '100%',
    maxWidth: '700px',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    minHeight: '100%',
    bottom: 0,
    background: palette.grayscale["200"],
  },
  drawerContainer: {
    '&&': {
      ...drawerModalStyle
    }
  },
  modalWrapper: {
    background: palette.grayscale["200"],
    // height: "100%"
    paddingBottom: 50
  },
  appBarRoot: {
    background: palette.grayscale["100"],
    ...shadows.base
  },
  listItemWrapper: {
    '& > div:not(:first-child)': {
      marginTop: 8
    }
  },
  item: {
    paddingBottom: 8,
    '&:not(:first-child)': {
      paddingTop: 8
    },
    display: 'flex',
    '& > p': {
      marginRight: 8,
      minWidth: 90
    }
  },
  description: {
    whiteSpace: 'pre-line'
  },
  background: {
    background: palette.grayscale["200"],
    // paddingTop: 12,
    paddingBottom: 24,
    // paddingLeft: 12,
    // paddingRight: 16,
    height: "100%",
    overflow: "auto"
  },
  layout: {
    display: "flex",
    flexDirection: "row",
    height: "100%",
    maxWidth: 464,
    margin: "0 auto"
  },
  paymentPanel: {
    minWidth: 336,
    background: palette.grayscale["300"],
    borderRadius: 16
  },
  suggestions: {
    display: "flex",
    flexDirection: "row",
    // flexWrap: "nowrap",
    overflowX: "auto",
    paddingBottom: 12
  },
  suggestion: {
    paddingTop: 6,
    paddingBottom: 6,
    paddingLeft: 12,
    paddingRight: 12,
    background: palette.grayscale["100"],
    ...shadows.base,
    marginRight: 8,
    borderRadius: 12
  },
  changeCalculator:{
    marginTop: 12,
    padding: 12,
    background: palette.grayscale["100"],
    ...shadows.base,
    borderRadius: 12
  },
  checkout: {
    display: "flex",
    flexDirection: "column",
    height: "100%",
    width: "100%"
  },
  reservationForm: {
    flex: 1
  },
  reservationOverview: {
    marginTop: 32,
    flex: 1
  },
  reservationCard: {
    borderRadius: 12,
    background: palette.grayscale["300"],
    padding: 16
  },
  breadcrumbs: {
    display: "flex",
    alignItems: "center",
    marginTop: 16,
    paddingBottom: 16,
    borderBottom: `1px solid ${palette.grayscale.divider}`,
    flex: 1
  },
  people: {
    // marginTop: 16,
    paddingBottom: 16,
    borderBottom: `1px dashed ${palette.grayscale.divider}`,
  },
  partner: {
    marginTop: 16,
    paddingBottom: 16,
    borderBottom: `1px dashed ${palette.grayscale.divider}`,
  },
  date: {
    marginTop: 16,
    paddingBottom: 16,
    flex: 1, marginRight: 8
    // borderBottom: `1px dashed ${palette.grayscale.divider}`,
  },
  calendar: {
    // marginTop: 16,
    // padding: 20,
    borderRadius: 12,
    // ...shadows.base,
    overflow: "hidden"
  },
  time: {
    marginTop: 16,
    paddingBottom: 16,
    flex: 1,
    marginLeft: 8
  },
  tables: {
    marginTop: 16,
    paddingBottom: 16,
    borderBottom: `1px dashed ${palette.grayscale.divider}`,
  },
  notes: {
    marginTop: 16,
    paddingBottom: 16,
    // borderBottom: `1px dashed ${palette.grayscale.divider}`,
  },
  customer: {
    marginTop: 16,
    marginBottom: 16,
    borderRadius: 12,
    border: `1px solid ${palette.grayscale["350"]}`,
    background: palette.grayscale["250"],
    padding: 16
  },
  field: {
    "&+&": {
      marginTop: 12
    }
  },
  options :{
    display: "flex",
    flexDirection: "row",
    flexWrap: "wrap"
  },
  scrollableOptions: {
    overflow: "auto",
    display: "flex",
    marginBottom: "-30px",
    paddingBottom: "30px"
  },
  cardOption: {
    marginRight: 12,
    marginTop: 16,
    display: "flex",
    alignItems: "center",
    justifyContent: "space-between",
    background: palette.grayscale["100"],
    padding: 12,
    ...shadows.base,
    borderRadius: 12
  },
  circularCardOption: {
    marginTop: 16,
    marginRight: 12,
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    background: palette.grayscale["100"],
    padding: 12,
    ...shadows.base,
    borderRadius: "100%"
  },
  dateCardOption: {
    marginTop: 12,
    width: 56,
    height: 62,
    display: "flex",
    flexDirection: "column",
    alignItems: "center",
    justifyContent: "center",
    background: palette.grayscale["100"],
    ...shadows.base,
    borderRadius: 12,
    "&+&": {
      marginLeft: 12,
    }
  },
  selectedDateCardOption: {
    background: palette.primary["500"],
  },
  giftCard: {
    marginTop: 16,
    paddingBottom: 16,
    borderBottom: `1px solid ${palette.grayscale.divider}`,
  },
  tip: {
    marginTop: 16,
    paddingBottom: 16,
    borderBottom: `1px solid ${palette.grayscale.divider}`,
  },
  promotion: {
    marginTop: 16,
    paddingBottom: 16,
    borderBottom: `1px solid ${palette.grayscale.divider}`,
  },
  printReceipt: {
    marginTop: 16
  },
  expendableSummary: {
    marginBottom: 16
  },
  summaryLine: {
    display: "flex",
    alignItems: "center",
    justifyContent: "space-between",
    "&+&": {
      marginTop: 4
    }
  }
}));

export default useStyles;
