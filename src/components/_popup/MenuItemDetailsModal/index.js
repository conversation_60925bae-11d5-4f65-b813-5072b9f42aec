import React from "react";
import { withTranslation } from '../../../../i18n';
import Modal from "../Modal";
import ModalBar from "../../_navigation/ModalBar";
import shadows from "../../../../styles/shadows";
import isEmpty from "../../../utilities/isEmpty";
import MenuItemDetails from "../../MenuItemDetails";

const MenuItemDetailsModal = ({ t, onClose, addOrderItem, activeMenuItem, readOnly, showNotes }) => {
  const open = !isEmpty(activeMenuItem)
  
  return (
    <Modal
      open={open}
      onClose={onClose}
      maxWidth={false}
      PaperProps={{ style: { background: "transparent",  ...shadows.large, width: 500, maxWidth: "95%", margin: 0 } }}
    >
      <ModalBar title={activeMenuItem.name} onClose={onClose} />
      <MenuItemDetails
        {...activeMenuItem}
        addOrderItem={addOrderItem}
        readOnly={readOnly}
        showNotes={showNotes}
      />
    </Modal>
  )
}

export default withTranslation("common")(MenuItemDetailsModal);
