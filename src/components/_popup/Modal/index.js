import React from "react";
import Dialog from '@material-ui/core/Dialog';
import { Slide } from "@material-ui/core";

const Transition = React.forwardRef(function Transition(props, ref) {
  return <Slide direction="up" ref={ref} {...props} />;
});

const Modal = ({ open, onClose, fullScreen, children, ...otherProps }) => {
  delete otherProps.tReady;
  
  return (
    <Dialog
      open={open}
      onClose={onClose}
      fullScreen={fullScreen}
      TransitionComponent={fullScreen && Transition}
      {...otherProps}
    >
      {children}
    </Dialog>
  );
};

export default Modal;
