import React, { useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Typography } from '@material-ui/core';
import ButtonBase from '@material-ui/core/ButtonBase';
import Badge from '@material-ui/core/Badge';
import initials from 'initials';
import { withTranslation } from '../../../../i18n';
import Modal from '../Modal';
import ModalBar from '../../_navigation/ModalBar';
import shadows from '../../../../styles/shadows';
import palette from '../../../../styles/palette';
import { orderSelectors, restaurantsSelectors, tableSelectors } from '../../../../redux/selectors';
import typography from '../../../../styles/typography';
import { googleTags } from '../../../../gtm';
import isEmpty from '../../../utilities/isEmpty';
import useStyles from './styles';
import { tableActions } from '../../../../redux/actions';
import { CheckIconRound20, CloseIconDark16, LeaveIcon, MinusIconDark16, PendingIconRound20 } from '../../Icons';
import Avatar from '../../Avatar';
import { Paragraph } from '../../Text';
import { Button, IconButton } from '../../Buttons';
import OutlinedIcon from '../../Buttons/OutlinedIconButton';
import { TitleContainer } from '../../Containers';

const TableManagementModal = ({ t, open, onClose }) => {
  const classes = useStyles();
  const dispatch = useDispatch();

  const { order = {} } = useSelector(orderSelectors.getOrder);
  const { table = {} } = useSelector(tableSelectors.getTable);
  const { tableCode, tableLabel } = order;
  const { label } = table;
  const { participants } = useSelector(orderSelectors.getParticipants);
  const { participant: currentParticipant = {} } = useSelector(orderSelectors.getParticipant);
  const { id: currentParticipantId, canLeave } = currentParticipant;
  const [leaveConfirmation, setLeaveConfirmation] = useState(false);
  const [kickOutConfirmationParticipant, setKickOutConfirmationParticipant] = useState(null);

  const close = () => {
    setLeaveConfirmation(false);
    setKickOutConfirmationParticipant(null);
    onClose();
  };

  const approve = (participantId) => {
    dispatch(tableActions.approveParticipant(order.id, participantId));
  };

  const remove = (participantId) => {
    dispatch(tableActions.deleteParticipant(order.id, participantId));
    setKickOutConfirmationParticipant(null);
  };

  return (
    <Modal
      open={open}
      onClose={onClose}
      maxWidth={false}
      PaperProps={{ style: { background: 'transparent', ...shadows.large, width: 500, maxWidth: '95%', margin: 0 } }}
    >
      <ModalBar title={`${t('activity-title-table')} ${label ?? tableCode}`} onClose={onClose} />
      <div style={{ height: '100%', overflow: 'auto', background: palette.grayscale['200'] }}>
        <div style={{ paddingLeft: 20, paddingRight: 20, paddingTop: 20, paddingBottom: 20 }}>
          <div style={{
            display: 'flex',
            flexDirection: 'column',
            background: palette.grayscale['100'],
            width: '100%',
            padding: 12,
            ...shadows.base,
            borderRadius: 12,
          }}
          >
            <div className={classes.users}>
              <div>
                <div className={classes.avatarGroup}>
                  {(participants ?? []).map(({ id, customer = {}, approvedByCustomer, canApprove, canReject, canKickout, approvalId }) => {
                    const customerFullName = `${customer.firstName} ${customer.lastName || ''}`;

                    return (
                      <div className={classes.userContainer} key={id}>
                        <div className={classes.user} key={id}>
                          <Badge
                            overlap="circle"
                            anchorOrigin={{
                              vertical: 'top',
                              horizontal: 'right',
                            }}
                            badgeContent={approvalId ? <CheckIconRound20 /> : <PendingIconRound20 />}
                          >
                            <Avatar alt={customerFullName} className={classes.avatar}>
                              {initials(customerFullName)}
                            </Avatar>
                          </Badge>
                          <div className={classes.label}>
                            <Paragraph>
                              {customerFullName}
                            </Paragraph>
                            <div className={classes.secondary}>
                              {(kickOutConfirmationParticipant === id) && (
                                <Paragraph>
                                  {t('table-management-kick-out-table-confirmation-label', { name: customer.firstName })}
                                </Paragraph>
                              )}
                              {!(kickOutConfirmationParticipant === id) && (
                                <>
                                  {approvalId && (
                                    <Paragraph>
                                      {!isEmpty(approvedByCustomer) ? t('common-approved-by', { name: approvedByCustomer.firstName }) : t('common-approved')}
                                    </Paragraph>
                                  )}
                                  {!approvalId && (
                                    <Paragraph>
                                      {t('common-pending')}
                                    </Paragraph>
                                  )}
                                </>
                              )}
                            </div>
                          </div>
                          {!(kickOutConfirmationParticipant === id) && (
                            <div className={classes.actions}>
                              {canApprove && (
                                <div className={classes.action}>
                                  <Button color="primary" onClick={() => approve(id)}>{t('common-approve')}</Button>
                                </div>
                              )}
                              {canReject && (
                                <div className={classes.action}>
                                  <OutlinedIcon icon={<CloseIconDark16 />} onClick={() => remove(id)} />
                                </div>
                              )}
                              {canKickout && (
                                <div className={classes.action}>
                                  <OutlinedIcon icon={<MinusIconDark16 />} onClick={() => setKickOutConfirmationParticipant(id)} />
                                </div>
                              )}
                            </div>
                          )}
                        </div>
                        {(kickOutConfirmationParticipant === id) && (
                          <div className={classes.deepShiftedContent}>
                            <div className={classes.actions}>
                              <div className={classes.action}>
                                <Button color="secondary" onClick={() => remove(id)}>{t('table-management-kick-out-btn-label')}</Button>
                              </div>
                              <div className={classes.action}>
                                <Button variant="text" onClick={() => setKickOutConfirmationParticipant(null)}>{t('common-cancel')}</Button>
                              </div>
                            </div>
                          </div>
                        )}
                      </div>
                    );
                  })}
                </div>
              </div>
            </div>
            <TitleContainer divider>
              <div className={classes.iconBtn}>
                <IconButton
                  edge="start"
                  disabled={leaveConfirmation}
                  onClick={() => setLeaveConfirmation(true)}
                  label={t(leaveConfirmation ? (canLeave ? 'table-management-leave-table-confirmation-label' : 'table-management-leave-table-non-confirmation-label') : 'table-management-leave-table-btn-label')}
                >
                  <LeaveIcon />
                </IconButton>
                <div className={classes.shiftedContent}>
                  {leaveConfirmation && !canLeave && (
                    <div className={classes.light}>
                      <Paragraph>
                        {t('table-management-leave-table-non-confirmation-description')}
                      </Paragraph>
                    </div>
                  )}
                  {leaveConfirmation && (
                    <div className={classes.leaveConfirmation}>
                      <div className={classes.actions}>
                        {canLeave && (
                          <>
                            <div className={classes.action}>
                              <Button color="secondary" onClick={() => remove(currentParticipantId)}>{t('table-management-leave-btn-label')}</Button>
                            </div>
                            <div className={classes.action}>
                              <Button variant="text" onClick={() => setLeaveConfirmation(false)}>{t('common-cancel')}</Button>
                            </div>
                          </>
                        )}
                        {!canLeave && (
                          <div className={classes.action}>
                            <Button variant="text" onClick={() => setLeaveConfirmation(false)}>{t('common-okay')}</Button>
                          </div>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </TitleContainer>
          </div>
        </div>
      </div>
      <div style={{
        paddingTop: 16,
        paddingBottom: 16,
        paddingLeft: 20,
        paddingRight: 20,
        borderBottomLeftRadius: 20,
        borderBottomRightRadius: 20,
        display: 'flex',
        flexDirection: 'row',
        justifyContent: 'flex-end',
        alignItems: 'center',
        borderTop: `1px solid ${palette.grayscale.divider}`,
        background: palette.grayscale['100']
      }}
      >
        <ButtonBase
          onClick={onClose}
          disableRipple
          disableTouchRipple
          id={googleTags.dineOut.orderBtn.id}
          style={{
            paddingTop: 12,
            paddingBottom: 12,
            paddingLeft: 24,
            paddingRight: 24,
            background: palette.primary['500'],
            borderRadius: 12
          }}
        >
          <Typography style={{ ...typography.body.medium, color: palette.grayscale['100'] }}>
            {t('close')}
          </Typography>
        </ButtonBase>
      </div>
    </Modal>
  );
};

export default withTranslation('common')(TableManagementModal);
