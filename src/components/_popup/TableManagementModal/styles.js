import { makeStyles } from '@material-ui/core/styles';
import { colors, drawerModalStyle, fontStyles } from '../../../../styles/theme';

const useStyles = makeStyles((theme) => ({
  drawerModal: {
    position: 'absolute',
    bottom: 580,
    width: '100%',
    maxWidth: '700px',
    top: 0
  },
  drawerContainer: {
    '&&': {
      ...drawerModalStyle
    }
  },
  modalWrapper: {
    background: theme.palette.common.white,
    borderBottomLeftRadius: 8,
    borderBottomRightRadius: 8,
  },
  content: {
    paddingTop: theme.spacing(4),
    paddingBottom: theme.spacing(2)
  },
  userContainer: {
    '&+&': {
      marginTop: theme.spacing(3)
    }
  },
  user: {
    display: 'flex',
    marginTop: theme.spacing(1),
    marginBottom: theme.spacing(1),
  },
  avatar: {
    height: 48,
    width: 48,
    ...fontStyles.secondaryTitleRegular
  },
  label: {
    flex: 1,
    marginLeft: theme.spacing(2),
    minHeight: 48,
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center'
  },
  secondary: {
    marginTop: 2,
    color: colors.leviee.greyscale.darkGray
  },
  light: {
    marginTop: theme.spacing(2),
    color: colors.leviee.greyscale.midGray
  },
  actions: {
    display: 'flex',
    alignItems: 'center'
  },
  action: {
    '&+&': {
      marginLeft: theme.spacing(1) + 2
    }
  },
  leaveConfirmation: {
    marginTop: theme.spacing(2)
  },
  shiftedContent: {
    marginLeft: 28,
  },
  deepShiftedContent: {
    marginLeft: 48 + 12
  }
}));

export default useStyles;
