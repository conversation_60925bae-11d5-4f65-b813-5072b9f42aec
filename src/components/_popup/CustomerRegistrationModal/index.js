import React, {useState} from "react";
import { withTranslation } from '../../../../i18n';
import Modal from "../Modal";
import ModalBar from "../../_navigation/ModalBar";
import shadows from "../../../../styles/shadows";
import palette from "../../../../styles/palette";
import {Slide, Typography, useMediaQuery} from "@material-ui/core";
import typography from "../../../../styles/typography";
import ButtonBase from "@material-ui/core/ButtonBase";
import useStyles from "./styles";
import {noop} from "../../Menu/utils";
import Field from "../../_input/Field";
import {restaurantsSelectors} from "../../../../redux/selectors";
import {useSelector} from "react-redux";
import isValidEmail from "../../../utilities/isValidEmail";
import TermsDisclaimer from "../../TermsDisclaimer";

const Transition = React.forwardRef(function Transition(props, ref) {
  return <Slide direction="up" ref={ref} {...props} />;
});

const data = {
  firstName: '',
  email: ''
}

const CustomerRegistrationModal = ({ t, open, onClose, onSubmit = noop, titleI18n }) => {
  const classes = useStyles();
  const isMobile = useMediaQuery('(max-width:600px)');
  
  const { restaurant = {} } = useSelector(restaurantsSelectors.getRestaurant)
  const { name = '' } = restaurant;
  
  const [form, setForm] = useState(data);
  const onChange = (e) => {
    let val = e.target.value || "";
    if (val) {
      val = val.trim();
    }
    setForm({ ...form, [e.target.name]: val })
  }
  
  const canSubmit = !!form.firstName && form.firstName.length >= 3 && !!form.email && isValidEmail(form.email)
  
  const submit = () => {
    onSubmit(form);
  }
  
  return (
    <Modal
      open={open}
      onClose={onClose}
      maxWidth={false}
      PaperProps={{
        style: { background: "transparent",  ...shadows.large, width: isMobile ? "100%" : 500, maxWidth: isMobile ? "100%" : "95%", margin: 0 } }}
      classes={{
        scrollPaper: isMobile ? classes.scrollPaperMobile : null
      }}
      TransitionComponent={Transition}
    >
      <ModalBar title={t(titleI18n)} onClose={onClose} />
      <div style={{ height: "100%", overflow: "auto", background: palette.grayscale["200"] }}>
        <div style={{ paddingLeft: isMobile ? 20 : 32, paddingRight: isMobile ? 20 : 32, paddingTop: 32, paddingBottom: 20 }}>
          <div style={{
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between",
            background: 'inherit',
            width: "100%",
            borderRadius: 12,
          }}>
            <div style={{ display: "flex", justifyContent: "flex-start", flexDirection: "column", width: "100%" }}>
              <Typography style={{ ...typography.large.semiBold, marginLeft: 0, textAlign: "left", marginBottom: 24 }}>
                {t('welcome-info-title', { name })}
              </Typography>
              {/*<Typography style={{ ...typography.body.regular, marginTop: 4 , textAlign: "left" }} />*/}
              <div style={{ marginBottom: 16 }}>
                <Field
                  label={t('your-name')}
                  placeholder={'Max'}
                  name="firstName"
                  value={form.firstName}
                  onChange={onChange}
                  autoComplete="off"
                  required
                />
              </div>
              <div>
                <Field
                  label={t('your-email')}
                  placeholder={'<EMAIL>'}
                  name="email"
                  value={form.email}
                  onChange={onChange}
                  autoComplete="off"
                  required
                />
              </div>
            </div>
          </div>
        </div>
        <div style={{ marginTop: 8, paddingLeft: isMobile ? 20 : 32, paddingRight: isMobile ? 20 : 32 }}>
          <TermsDisclaimer />
        </div>
      </div>
      <div style={{
        paddingTop: isMobile ? 0 : 16, paddingBottom: isMobile ? 32 : 16, paddingLeft: 20, paddingRight: 20,
        borderBottomLeftRadius: isMobile ? 0 : 20,
        borderBottomRightRadius: isMobile ? 0 : 20,
        display: "flex",
        flexDirection: "row",
        justifyContent: "flex-end",
        alignItems: "center",
        borderTop: isMobile ? "none" : `1px solid ${palette.grayscale.divider}`,
        background: palette.grayscale[isMobile ? "200" : "100"]
      }}>
        <ButtonBase
          onClick={submit}
          disableRipple
          disableTouchRipple
          disabled={!canSubmit}
          style={{
            width: isMobile ? "100%" : "auto",
            paddingTop: 12,
            paddingBottom: 12,
            paddingLeft: 24,
            paddingRight: 24,
            background: !canSubmit ? palette.grayscale["400"] : palette.primary["500"],
            borderRadius: 12
          }}
        >
          <Typography style={{ ...typography.body.medium, color: palette.grayscale["100"] }}>
            {t('welcome-info-start-ordering-btn-label')}
          </Typography>
        </ButtonBase>
      </div>
    </Modal>
  )
}

export default withTranslation("common")(CustomerRegistrationModal);
