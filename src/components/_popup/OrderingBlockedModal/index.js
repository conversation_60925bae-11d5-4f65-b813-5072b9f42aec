import React from "react";
import { withTranslation } from '../../../../i18n';
import Modal from "../Modal";
import ModalBar from "../../_navigation/ModalBar";
import shadows from "../../../../styles/shadows";
import palette from "../../../../styles/palette";
import {Typography} from "@material-ui/core";
import typography from "../../../../styles/typography";
import ButtonBase from "@material-ui/core/ButtonBase";

const OrderingBlockedModal = ({ t, open, onClose }) => {
  
  return (
    <Modal
      open={open}
      onClose={onClose}
      maxWidth={false}
      PaperProps={{ style: { background: "transparent",  ...shadows.large, width: 500, maxWidth: "95%", margin: 0 } }}
    >
      <ModalBar title={t('your-order')} onClose={onClose} />
      <div style={{ height: "100%", overflow: "auto", background: palette.grayscale["200"] }}>
        <div style={{ paddingLeft: 20, paddingRight: 20, paddingTop: 20, paddingBottom: 20 }}>
          <div style={{
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between",
            background: palette.grayscale["100"],
            width: "100%",
            padding: 12,
            ...shadows.base,
            borderRadius: 12,
          }}>
            <div style={{ display: "flex", justifyContent: "flex-start", flexDirection: "column" }}>
              <Typography style={{ ...typography.body.medium, marginLeft: 0, textAlign: "left" }}>
                {t('items-not-ordered')}
              </Typography>
              <Typography style={{ ...typography.body.regular, marginTop: 4 , textAlign: "left" }}>
                {t('your-items-are-still-in-the-basket')}
              </Typography>
            </div>
          </div>
        </div>
      </div>
      <div style={{
        paddingTop: 16, paddingBottom: 16, paddingLeft: 20, paddingRight: 20,
        borderBottomLeftRadius: 20,
        borderBottomRightRadius: 20,
        display: "flex",
        flexDirection: "row",
        justifyContent: "flex-end",
        alignItems: "center",
        borderTop: `1px solid ${palette.grayscale.divider}`,
        background: palette.grayscale["100"]
      }}>
        <ButtonBase
          onClick={onClose}
          disableRipple
          disableTouchRipple
          style={{
            paddingTop: 12,
            paddingBottom: 12,
            paddingLeft: 24,
            paddingRight: 24,
            background: palette.primary["500"],
            borderRadius: 12
          }}
        >
          <Typography style={{ ...typography.body.medium, color: palette.grayscale["100"] }}>
            {t('close')}
          </Typography>
        </ButtonBase>
      </div>
    </Modal>
  )
}

export default withTranslation("common")(OrderingBlockedModal);
