import React, {useEffect, useState} from "react";
import { withTranslation } from '../../../../i18n';
import Modal from "../Modal";
import ModalBar from "../../_navigation/ModalBar";
import shadows from "../../../../styles/shadows";
import palette from "../../../../styles/palette";
import {useDispatch, useSelector} from "react-redux";
import {meSelectors, orderSelectors, restaurantsSelectors, tableSelectors} from "../../../../redux/selectors";
import {Typography} from "@material-ui/core";
import typography from "../../../../styles/typography";
import ButtonBase from "@material-ui/core/ButtonBase";
import {googleTags} from "../../../../gtm";
import isEmpty from "../../../utilities/isEmpty";
import useStyles from "./styles";
import {tableActions} from "../../../../redux/actions";
import Container from "@material-ui/core/Container";
import {TextContainer, TitleContainer} from "../../Containers";
import {Paragraph, Title} from "../../Text";
import QRCode from "react-qr-code";
import {colors} from "../../../../styles/theme";
import {IconButton, MainButton} from "../../Buttons";
import {QRCodeIcon} from "../../Icons";
import {getRestaurantConfiguration, resolveOrderByOrderCode} from "../../../../redux/api";
import OtpInput from "react-otp-input";

const UserVerificationModal = ({ t, open, onClose }) => {
  const classes = useStyles();
  const dispatch = useDispatch();
  
  const restaurantId = useSelector(restaurantsSelectors.getRestaurantId)
  const restaurantCode = useSelector(restaurantsSelectors.getRestaurantCode)
  const tableCode = useSelector(tableSelectors.getTableCode)
  const { customer: currentCustomer } = useSelector(meSelectors.getMe)
  const { id: currentCustomerId } = (currentCustomer || {})
  
  const { order = {} } = useSelector(orderSelectors.getOrder);
  const { customer, participants = [], verificationRequestSent } = order;
  
  const [configuration, setConfiguration] = useState(null)
  const { scanToOrderConfig = {} } = (configuration || {})
  const { guestPinEnabled } = (scanToOrderConfig || {})
  
  useEffect(() => {
    getRestaurantConfiguration(restaurantId).then(({ data }) => setConfiguration(data)).catch(() => {})
  }, [])
  
  const verifiedParticipants = (isEmpty(participants) || participants.length === 1) ? [] : participants.filter((p) => !!p.canApprove);
  const verifiedParticipant = isEmpty(verifiedParticipants) ? null : verifiedParticipants[0];
  const { participant } = useSelector(orderSelectors.getParticipant);
  const { id: currentParticipantId } = participant;
  
  const currentParticipant = (participants || []).find(t => t.customerId === currentCustomerId)
  const currentParticipantIsApproved = currentParticipant && currentParticipant.canApprove
  const currentParticipantNeedsApprovalPin = !currentParticipantIsApproved && guestPinEnabled
  
  const [isVerificationCodeVisible, setVerificationCodeVisible] = useState(isEmpty(customer) || !verifiedParticipant);
  const showVerificationCode = () => setVerificationCodeVisible(true);
  const hideVerificationCode = () => setVerificationCodeVisible(false);
  const toggleVerificationCodeVisible = () => (isVerificationCodeVisible ? hideVerificationCode() : showVerificationCode());
  
  const notify = () => {
    dispatch(tableActions.notifyWaiterForParticipantVerification(order.id));
  };
  
  const [otp, setOtp] = useState('');
  const [otpLoading, setOtpLoading] = useState(false)
  const [otpError, setOtpError] = useState(false)
  
  const canContinue = !!otp && otp.length === 4
  
  const handleChange = (otp) => {
    setOtp(otp)
    setOtpError(false)
  };
  
  const approveWithPin = () => {
    if (otpLoading || !canContinue) {
      return
    }
    
    setOtpLoading(true)
    setOtpError(false)
    resolveOrderByOrderCode(`${restaurantCode}#${tableCode}`, { ...currentCustomer, scanToOrderPin: otp }).then(() => {
      setTimeout(() => {
        setOtpLoading(false)
        onClose();
      }, 3000)
    }).catch(() => {
      setOtpError(true)
      setOtpLoading(false)
    })
  }
  
  return (
    <Modal
      open={open}
      onClose={otpLoading ? null : onClose}
      maxWidth={false}
      PaperProps={{ style: { background: "transparent",  ...shadows.large, width: 360, maxWidth: "95%", margin: 0 } }}
    >
      <ModalBar title={t('activity-title-table-verification')} onClose={otpLoading ? null : onClose} />
      {!currentParticipantNeedsApprovalPin && (
        <div style={{height: "100%", overflow: "auto", background: palette.grayscale["200"]}}>
          <div style={{paddingLeft: 20, paddingRight: 20, paddingTop: 20, paddingBottom: 20}}>
            <div style={{
              background: palette.grayscale["100"],
              width: "100%",
              padding: 12,
              ...shadows.base,
              borderRadius: 12,
            }}>
              <div className={classes.modalWrapper}>
                <Container>
                  {customer && !isVerificationCodeVisible && !isEmpty(verifiedParticipants) && (
                    <>
                      <TextContainer>
                        <Title weight="medium">
                          {t('order-table-verification-drawer-friend-verification-title')}
                        </Title>
                        <div className={classes.description}>
                          <Paragraph>
                            {t('order-table-verification-drawer-friend-verification-description', {name: customer.firstName})}
                          </Paragraph>
                        </div>
                      </TextContainer>
                      <TitleContainer divider/>
                    </>
                  )}
                  <TextContainer>
                    <Title weight="medium">
                      {t('order-table-verification-drawer-title')}
                    </Title>
                    <div className={classes.description}>
                      <Paragraph>
                        {t('order-table-verification-drawer-description')}
                      </Paragraph>
                    </div>
                  </TextContainer>
                  {isVerificationCodeVisible && (
                    <>
                      <div className={classes.qrCodeWrapper}>
                        <QRCode
                          value={`lv://2fa/order/${order.id}#${participant.id}`}
                          size={200}
                          renderAs="svg"
                          fgColor={colors.leviee.greyscale.darkGray}
                        />
                      </div>
                      <MainButton onClick={notify} disabled={verificationRequestSent}>
                        {t(verificationRequestSent
                          ? 'order-table-verification-drawer-waiter-notified-label'
                          : 'order-table-verification-drawer-notify-waiter-btn')}
                      </MainButton>
                    </>
                  )}
                  {customer && !isEmpty(verifiedParticipants) && (
                    <div className={classes.showVerificationCodeBtn}>
                      <IconButton
                        edge="start"
                        onClick={toggleVerificationCodeVisible}
                        label={isVerificationCodeVisible ? 'Hide verification QR code' : 'Show verification QR code'}
                      >
                        <QRCodeIcon/>
                      </IconButton>
                    </div>
                  )}
                </Container>
              </div>
            </div>
          </div>
        </div>
      )}
      {currentParticipantNeedsApprovalPin && (
        <div style={{height: "100%", overflow: "auto", background: palette.grayscale["200"], borderBottomLeftRadius: currentParticipantNeedsApprovalPin ? 16 : 0, borderBottomRightRadius: currentParticipantNeedsApprovalPin ? 16 : 0}}>
          <div style={{paddingLeft: 20, paddingRight: 20, paddingTop: 20, paddingBottom: 20}}>
            <form>
              <Typography style={{ marginBottom: 8, ...typography.body.medium }}>{t("enter-code")}</Typography>
              <OtpInput
                containerStyle={classes.otpFields}
                inputStyle={classes.otpField}
                numInputs={4}
                isDisabled={otpLoading}
                // hasErrored={hasErrored}
                errorStyle="error"
                onChange={handleChange}
                isInputNum
                shouldAutoFocus
                value={otp}
              />
              {otpError&& (
                <Typography
                  style={{
                    ...typography.body.regular,
                    color: palette.negative["600"]
                  }}
                >
                  {t('approval-code-not-correct')}
                </Typography>
              )}
              <MainButton color="primary" variant="contained" className={classes.ctaButton} onClick={approveWithPin}
                          loading={otpLoading} disabled={!canContinue}>{t('common-continue')}</MainButton>
            </form>
          </div>
        </div>
      )}
      {!currentParticipantNeedsApprovalPin && (
        <div style={{
          paddingTop: 16, paddingBottom: 16, paddingLeft: 20, paddingRight: 20,
          borderBottomLeftRadius: 20,
          borderBottomRightRadius: 20,
          display: "flex",
          flexDirection: "row",
          justifyContent: "flex-end",
          alignItems: "center",
          borderTop: `1px solid ${palette.grayscale.divider}`,
          background: palette.grayscale["100"]
        }}>
          <ButtonBase
            onClick={onClose}
            disableRipple
            disableTouchRipple
            id={googleTags.dineOut.orderBtn.id}
            style={{
              paddingTop: 12,
              paddingBottom: 12,
              paddingLeft: 24,
              paddingRight: 24,
              background: palette.primary["500"],
              borderRadius: 12
            }}
          >
            <Typography style={{...typography.body.medium, color: palette.grayscale["100"]}}>
              {t('close')}
            </Typography>
          </ButtonBase>
        </div>
      )}
    </Modal>
  )
}

export default withTranslation("common")(UserVerificationModal);
