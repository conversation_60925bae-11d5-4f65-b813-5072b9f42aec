import { fade, makeStyles } from '@material-ui/core/styles';
import { colors, drawerModalStyle } from '../../../../styles/theme';
import palette from "../../../../styles/palette";
import typography from "../../../../styles/typography";

const useStyles = makeStyles((theme) => ({
  drawerModal: {
    background: theme.palette.common.white,
    position: 'absolute',
    top: 230,
    width: '100%',
    maxWidth: '700px',
    borderTopLeftRadius: 8,
    borderTopRightRadius: 8,
    minHeight: '100%',
    bottom: 0
  },
  drawerContainer: {
    '&&': {
      ...drawerModalStyle
    }
  },
  modalWrapper: {
    // marginTop: theme.spacing(4),
    // background: theme.palette.common.white
  },
  description: {
    color: fade(colors.leviee.main.dark, 0.48),
    paddingTop: 2
  },
  qrCodeWrapper: {
    paddingTop: 30,
    paddingBottom: 40,
    textAlign: 'center'
  },
  light: {
    marginTop: theme.spacing(2),
    color: colors.leviee.greyscale.midGray
  },
  actions: {
    display: 'flex',
    alignItems: 'center'
  },
  action: {
    '&+&': {
      marginLeft: theme.spacing(1) + 2
    }
  },
  leaveConfirmation: {
    marginTop: theme.spacing(2)
  },
  shiftedContent: {
    marginLeft: 28,
  },
  deepShiftedContent: {
    marginLeft: 48 + 12
  },
  showVerificationCodeBtn: {
    paddingTop: 15,
    paddingBottom: 15
  },
  form: {
    width: 320,
    maxWidth: "calc(100% - 24px)",
    margin: "48px auto"
  },
  otpFields: {
    display: 'flex',
    marginBottom: 8,
    '@media (max-width: 600px)': {
      marginBottom: 16
    },
  },
  otpField: {
    width: '44px !important',
    height: '44px !important',
    ...typography.body.regular,
    borderRadius: 12,
    marginRight: 8,
    border: `1px solid ${palette.grayscale["350"]}`,
    backgroundColor: palette.grayscale["100"]
  },
  ctaButton: {
    width: '100%',
    marginTop: 24
  },
}));

export default useStyles;
