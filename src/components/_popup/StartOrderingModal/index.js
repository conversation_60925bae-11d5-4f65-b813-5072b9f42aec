import React, { useEffect, useState } from "react";
import { withTranslation } from '../../../../i18n';
import Modal from "../Modal";
import ModalBar from "../../_navigation/ModalBar";
import shadows from "../../../../styles/shadows";
import palette from "../../../../styles/palette";
import {useSelector} from "react-redux";
import {restaurantsSelectors} from "../../../../redux/selectors";
import {Typography} from "@material-ui/core";
import typography from "../../../../styles/typography";
import ButtonBase from "@material-ui/core/ButtonBase";
import Radio from "../../_toggles/Radio";
import {googleTags} from "../../../../gtm";
import {useRouter} from "next/router";
import formatNumber from "../../../utilities/formatNumber";
import {getDiscountConfig} from "../../../../redux/api";

const StartOrderingModal = ({ t, open, onClose = "", titleI18n }) => {
  const router = useRouter();
  const { restaurant = {} } = useSelector(restaurantsSelectors.getRestaurant);
  const { id: restaurantId, name, slug, hasPickup, hasDelivery, address = {} } = restaurant;
  const { street, number, zipCode, city } = address;
  const resolvedAddress = `${street} ${number}, ${zipCode} ${city}`;
  
  const [orderType, setOrderType] = useState((!hasPickup && !hasDelivery) ? null : (hasPickup ? 'pickup' : 'delivery'));
  
  const [discountConfiguration, setDiscountConfiguration] = useState(null)
  const { pickupDiscountPercentage } = (discountConfiguration || {});
  
  useEffect(() => {
    if (restaurantId) {
      getDiscountConfig(restaurantId).then(({ data = {} }) => {
        const fetched = data.data || {};
        const { getRestaurantConfig = {} } = fetched || {};
        const { webshopConfig = {} } = getRestaurantConfig || {};
        setDiscountConfiguration(webshopConfig)
      }).catch(() => {})
    }
  }, [restaurantId])
  
  const setPickup = () => setOrderType('pickup');
  const setDelivery = () => setOrderType('delivery');
  const [loading, setLoading] = useState(false);
  
  const onClick = () => {
    // we check for ?delivery= or ?pickup=
    // setting here ?=xref to have a value
    if (!orderType) {
      return;
    }
    router
      .push(
        `/restaurant/${slug}/takeaway?${orderType}=xref`,
        `/restaurant/${slug}/takeaway?${orderType}=xref`,
        { shallow: true }
      )
      .then(() => {
        setLoading(true)
      })
      .catch(() => {})
      .finally(() => setLoading(false));
  };
  
  return (
    <Modal
      open={open}
      onClose={onClose}
      maxWidth={false}
      PaperProps={{ style: { background: "transparent",  ...shadows.large, width: 500, maxWidth: "95%", margin: 0 } }}
    >
      <ModalBar title={t(titleI18n)} onClose={onClose} />
      <div style={{ height: "100%", overflow: "auto", background: palette.grayscale["200"] }}>
        <div style={{ paddingLeft: 20, paddingRight: 20, paddingTop: 20, paddingBottom: 20 }}>
          <ButtonBase disableRipple disableTouchRipple disabled={!hasPickup} style={{
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between",
            background: palette.grayscale["100"],
            width: "100%",
            padding: 12,
            ...shadows.base,
            borderRadius: 12,
            opacity: !hasPickup ? 0.6 : 1
          }} onClick={setPickup}>
            <div style={{ display: "flex", justifyContent: "flex-start", flexDirection: "column" }}>
              <div style={{ display: "flex", alignItems: "center" }}>
                <Typography style={{ ...typography.body.medium, marginLeft: 0, textAlign: "left" }}>
                  {t('pickup-order-type-field-pickup-option-label')}
                </Typography>
                {pickupDiscountPercentage && (
                  <Typography style={{ ...typography.small.medium, padding: '2px 8px', marginLeft: 4, color: palette.grayscale["100"], background: palette.primary["500"], borderRadius: 12 }}>
                    {t('percent-discount', { percent: pickupDiscountPercentage })}
                  </Typography>
                )}
              </div>
              <Typography style={{ ...typography.body.regular, marginTop: 4 , textAlign: "left" }}>
                {resolvedAddress}
              </Typography>
            </div>
            <Radio checked={orderType === 'pickup'} />
          </ButtonBase>
          <ButtonBase disableRipple disableTouchRipple disabled={!hasDelivery} style={{
            marginTop: 12,
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between",
            background: palette.grayscale["100"],
            width: "100%",
            padding: 12,
            ...shadows.base,
            borderRadius: 12,
            opacity: !hasDelivery ? 0.6 : 1
          }} onClick={setDelivery}>
            <div style={{ display: "flex", justifyContent: "flex-start", flexDirection: "column" }}>
              <Typography style={{ ...typography.body.medium, marginLeft: 0, textAlign: "left" }}>
                {t('pickup-order-type-field-delivery-option-label')}
              </Typography>
              {hasDelivery && (
                <Typography style={{ ...typography.body.regular, marginTop: 4 , textAlign: "left" }}>
                  {t('delivery-fees-may-apply')}
                </Typography>
              )}
              {!hasDelivery && (
                <Typography style={{ ...typography.body.regular, marginTop: 4 , textAlign: "left" }}>
                  {t('restaurant-does-not-deliver', { restaurant: name })}
                </Typography>
              )}
            </div>
            <Radio checked={orderType === 'delivery'} />
          </ButtonBase>
        </div>
      </div>
      <div style={{
        paddingTop: 16, paddingBottom: 16, paddingLeft: 20, paddingRight: 20,
        borderBottomLeftRadius: 20,
        borderBottomRightRadius: 20,
        display: "flex",
        flexDirection: "row",
        justifyContent: "flex-end",
        alignItems: "center",
        borderTop: `1px solid ${palette.grayscale.divider}`,
        background: palette.grayscale["100"]
      }}>
        <ButtonBase
          onClick={onClick}
          disabled={!orderType}
          disableRipple
          disableTouchRipple
          id={googleTags.dineOut.orderBtn.id}
          style={{
            paddingTop: 12,
            paddingBottom: 12,
            paddingLeft: 24,
            paddingRight: 24,
            background: orderType || !loading ? palette.primary["500"] : palette.grayscale["400"],
            borderRadius: 12
          }}
        >
          <Typography style={{ ...typography.body.medium, color: palette.grayscale["100"] }}>
            {t('restaurant-order-btn-label')}
          </Typography>
        </ButtonBase>
      </div>
    </Modal>
  )
}

export default withTranslation("common")(StartOrderingModal);
