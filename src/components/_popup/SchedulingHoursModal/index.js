import React, { useEffect, useState } from 'react';
import Grid from '@material-ui/core/Grid';
import moment from 'moment/moment';
import ButtonBase from '@material-ui/core/ButtonBase';
import clsx from 'clsx';
import Typography from '@material-ui/core/Typography';
import { withTranslation } from '../../../../i18n';
import Modal from '../Modal';
import ModalBar from '../../_navigation/ModalBar';
import shadows from '../../../../styles/shadows';
import palette from '../../../../styles/palette';
import isEmpty from '../../../utilities/isEmpty';
import typography from '../../../../styles/typography';
import Checkbox from '../../_toggles/Checkbox';
import useStyles from '../../../features/takeaway/checkout/styles';

const SchedulingHoursModal = ({ t, open, onClose, schedulingHoursOptions, schedulingDaysOptions, form, onTimeCheckboxChange,
  onTakeawayDateChange, isSubmitting, setSchedulingHoursModalOpen, setForm }) => {
  const classes = useStyles();

  const isValid = form.takeawayDate && form.pickupTime;

  const onSubmitTimeAndDate = () => {
    setSchedulingHoursModalOpen(false);
  };

  useEffect(() => {
    if (!isEmpty(schedulingHoursOptions)) {
      setForm({ ...form, takeawayDate: schedulingDaysOptions[0].id });
    }
  }, []);

  return (
    <Modal
      open={open}
      onClose={onClose}
      maxWidth={false}
      PaperProps={{ style: { background: 'transparent', ...shadows.large, width: 500, maxWidth: '95%', margin: 0, borderRadius: '20px' } }}
    >
      <ModalBar title={t('choose-scheduling-time')} onClose={onClose} onDone={isValid ? onSubmitTimeAndDate : null} />
      <div style={{ height: '100%', overflow: 'auto', background: palette.grayscale['200'] }}>
        <div style={{ paddingLeft: 20, paddingRight: 20, paddingTop: 20, paddingBottom: 20 }}>
          <Grid item xs={12}>
            <div style={{ display: 'flex', gap: 8, alignItems: 'center', justifyContent: 'flex-start', marginBottom: 12 }}>
              {!isEmpty(schedulingDaysOptions) && schedulingDaysOptions.map(
                ({
                  id,
                  value: optionValue,
                  label: optionLabel,
                  disabled
                }) => {
                  const selected = moment(optionValue, 'DD-MM-YYYY').isSame(moment(form.takeawayDate, 'DD-MM-YYYY'), 'day');
                  return (
                    <ButtonBase
                      className={clsx(classes.dateCardOption, { [classes.selectedDateCardOption]: !!selected })}
                      onClick={() => onTakeawayDateChange(optionValue)}
                      disabled={isEmpty(schedulingDaysOptions) || isSubmitting}
                    >
                      <Typography style={{ ...typography.extraSmall.medium, color: selected ? 'rgba(249, 249, 249, 0.8)' : palette.grayscale['600'] }}>
                        {moment(optionValue, 'DD-MM-YYYY').format('ddd')}
                      </Typography>
                      <Typography style={{ ...typography.body.medium, marginTop: 2, color: selected ? palette.grayscale['100'] : 'inherit' }}>
                        {moment(optionValue, 'DD-MM-YYYY').format('DD')}
                      </Typography>
                    </ButtonBase>
                  );
                }
              )}
            </div>
          </Grid>
          <Grid item xs={12}>
            <div style={{ maxHeight: 600, overflow: 'auto' }}>
              {!isEmpty(schedulingHoursOptions) && schedulingHoursOptions
                .map(
                  ({ id, value: optionValue, label: optionLabel, disabled }) => (
                    <ButtonBase
                      style={{ borderRadius: 10, backgroundColor: palette.grayscale.white, padding: 12, width: '100%', display: 'flex', justifyContent: 'space-between', marginBottom: 8 }}
                      onClick={() => onTimeCheckboxChange(optionValue)}
                      disabled={isEmpty(schedulingHoursOptions) || isSubmitting}
                    >
                      <Typography style={{ ...typography.body.regular }}>{isEmpty(optionValue) ? optionLabel : moment(optionValue, 'HH:mm:ss').format('HH:mm')}</Typography>
                      <Checkbox checked={form.pickupTime === optionValue} />
                    </ButtonBase>
                  )
                )}
            </div>
          </Grid>
        </div>
      </div>
    </Modal>
  );
};

export default withTranslation('common')(SchedulingHoursModal);
