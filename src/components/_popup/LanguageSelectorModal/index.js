import React from "react";
import {withTranslation} from '../../../../i18n';
import {Slide, Typography, useMediaQuery} from "@material-ui/core";
import Modal from "../Modal";
import ModalBar from "../../_navigation/ModalBar";
import typography from "../../../../styles/typography";
import palette from "../../../../styles/palette";
import shadows from "../../../../styles/shadows";
import useStyles from "./styles";
import FlexDiv from "../../_div/FlexDiv";
import ButtonBase from "@material-ui/core/ButtonBase";
import {languageOptions} from "../../../const";
import {CaretRightIcon} from "../../../utilities/icons";

const Transition = React.forwardRef(function Transition(props, ref) {
  return <Slide direction="up" ref={ref} {...props} />;
});

const LanguageSelectorModal = ({ t, open, onClose, setLanguage }) => {
  const classes = useStyles();
  const isMobile = useMediaQuery('(max-width:600px)');
  
  return (
    <Modal
      open={open}
      onClose={onClose}
      maxWidth={false}
      style={{ zIndex: 9999 }}
      PaperProps={{
        style: { background: "transparent", ...shadows.large, width: isMobile ? "100%" : 300, maxWidth: isMobile ? "100%" : "95%", margin: 0, borderRadius: 0, zIndex: 9999 } }}
      classes={{
        scrollPaper: isMobile ? classes.scrollPaperMobile : null
      }}
      TransitionComponent={Transition}
    >
      <ModalBar title={t("language")} onClose={onClose} />
      <div style={{ height: "100%", overflow: "auto", background: palette.grayscale["200"], borderBottomLeftRadius: isMobile ? 0 : 20, borderBottomRightRadius: isMobile ? 0 : 20 }}>
        <div style={{ overflow: "hidden" }}>
          <div style={{ height: "100%", ...shadows.base }}>
            <div
              style={{
                backgroundColor: palette.grayscale["100"],
              }}
            >
              <div>
                {languageOptions
                  .map((lang) => {
                    const { i18nKey, iconSrc, value } = lang;
                    return (
                      <div style={{width: "100%"}}>
                        <ButtonBase onClick={() => setLanguage(value)} style={{padding: 12, width: "100%"}}>
                          <FlexDiv alignItems="center" justifyContent="space-between" style={{width: "100%"}}>
                            <FlexDiv alignItems="center">
                              {iconSrc && (
                                <div
                                  style={{height: 24, display: "flex", alignItems: "center", justifyContent: "center"}}>
                                  <div
                                    style={{
                                      height: 16,
                                      width: 22,
                                      borderRadius: 3,
                                      backgroundImage: `url('${iconSrc}')`,
                                      backgroundRepeat: "no-repeat",
                                      backgroundSize: "cover",
                                      backgroundPosition: "center",
                                      marginRight: 8
                                    }}
                                  />
                                </div>
                              )}
                              <Typography
                                style={{
                                  ...typography.body.regular,
                                }}
                              >
                                {t(i18nKey)}
                              </Typography>
                            </FlexDiv>
                            <CaretRightIcon />
                          </FlexDiv>
                        </ButtonBase>
                      </div>
                    );
                  })}
              </div>
            </div>
          </div>
        </div>
      </div>
    </Modal>
  )
}

export default withTranslation("common")(LanguageSelectorModal);
