import React, {useEffect, useState} from "react";
import { withTranslation } from '../../../../i18n';
import Modal from "../Modal";
import ModalBar from "../../_navigation/ModalBar";
import shadows from "../../../../styles/shadows";
import palette from "../../../../styles/palette";
import {useSelector} from "react-redux";
import {restaurantsSelectors} from "../../../../redux/selectors";
import {Typography} from "@material-ui/core";
import typography from "../../../../styles/typography";
import ButtonBase from "@material-ui/core/ButtonBase";
import {googleTags} from "../../../../gtm";
import {useRouter} from "next/router";
import calculateOpeningHours from "../../../utilities/calculateOpeningHours";
import isEmpty from "../../../utilities/isEmpty";
import {getRestaurantConfiguration} from "../../../../redux/api";

const RestaurantInfoModal = ({ t, open, onClose = "", titleI18n }) => {
  const router = useRouter();
  const { restaurant = {} } = useSelector(restaurantsSelectors.getRestaurant);
  const { id, name, slug, hasPickup, hasDelivery, address = {}, deliveryFee, phone, openingHours = {}, description } = restaurant;
  const { street, number, zipCode, city } = address;
  const resolvedAddress = `${street} ${number}, ${zipCode} ${city}`;
  const { MONDAY = {}, TUESDAY = {}, WEDNESDAY = {}, THURSDAY = {}, FRIDAY = {}, SATURDAY = {}, SUNDAY = {}} = (isEmpty(openingHours) ? {} : openingHours);
  
  const weekday = ["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"];

  const [webshopOpeningHours, setWebshopOpeningHours] = useState()
  
  const d = new Date();
  let today = weekday[d.getDay()];

  useEffect(() => {
    getRestaurantConfiguration(id)
      .then(({ data }) => {
        const { webshopConfig = {} } = (data || {});
        const { webshopHours = {} } = (webshopConfig || {});
        if(webshopHours && !isEmpty(webshopHours))
          setWebshopOpeningHours(webshopHours);
        else {
          setWebshopOpeningHours(openingHours);
        }
      })
      .catch((e) => {
        console.log(e);
      });
  }, [id]);
  
  return (
    <Modal
      open={open}
      onClose={onClose}
      maxWidth={false}
      PaperProps={{ style: { background: "transparent",  ...shadows.large, width: 500, maxWidth: "95%", margin: 0 } }}
    >
      <ModalBar title={t(titleI18n)} onClose={onClose} />
      <div style={{ height: "100%", overflow: "auto", background: palette.grayscale["200"] }}>
        <div style={{ paddingLeft: 20, paddingRight: 20, paddingTop: 20, paddingBottom: 20 }}>
          <div style={{
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between",
            background: palette.grayscale["100"],
            width: "100%",
            padding: 12,
            ...shadows.base,
            borderRadius: 12,
          }}>
            <div style={{ display: "flex", justifyContent: "flex-start", flexDirection: "column" }}>
              <Typography style={{ ...typography.body.medium, marginLeft: 0, textAlign: "left" }}>
                {t('activity-title-restaurant-details')}
              </Typography>
              <Typography style={{ ...typography.body.regular, marginTop: 4 , textAlign: "left" }}>
                {name}
              </Typography>
              <Typography style={{ ...typography.body.regular, marginTop: 4 , textAlign: "left" }}>
                {resolvedAddress}
              </Typography>
              <Typography style={{ ...typography.body.regular, marginTop: 4 , textAlign: "left" }}>
                {phone}
              </Typography>
            </div>
          </div>
          {!isEmpty(webshopOpeningHours) && (
            <div style={{
              display: "flex",
              alignItems: "center",
              justifyContent: "space-between",
              background: palette.grayscale["100"],
              width: "100%",
              padding: 12,
              ...shadows.base,
              borderRadius: 12,
              marginTop: 12
            }}>
              <div style={{ display: "flex", justifyContent: "flex-start", flexDirection: "column" }}>
                <Typography style={{ ...typography.body.medium, marginLeft: 0, textAlign: "left" }}>
                  {t('webshop-opening-hours')}
                </Typography>
                {weekday.map(wd => (
                  <div style={{ display: "flex", alignItems: "center" }} key={wd}>
                    <Typography style={{ ...typography.body.regular, marginTop: 4 , textAlign: "left", minWidth: 80 }}>
                      {t(`common-${wd.toLowerCase()}`)}
                    </Typography>
                    <Typography style={{ ...typography.body.regular, marginTop: 4 , textAlign: "left" }}>
                      <span style={{ display: "inline-block" }}>{calculateOpeningHours(webshopOpeningHours[wd.toUpperCase()])}</span>
                    </Typography>
                  </div>
                ))}
              </div>
            </div>
          )}
          {!isEmpty(description) && (
            <div style={{
              display: "flex",
              alignItems: "center",
              justifyContent: "space-between",
              background: palette.grayscale["100"],
              width: "100%",
              padding: 12,
              ...shadows.base,
              borderRadius: 12,
              marginTop: 12
            }}>
              <div style={{ display: "flex", justifyContent: "flex-start", flexDirection: "column" }}>
                <Typography style={{ ...typography.body.regular, marginLeft: 0, textAlign: "left" }}>
                  {description}
                </Typography>
              </div>
            </div>
          )}
        </div>
      </div>
      <div style={{
        paddingTop: 16, paddingBottom: 16, paddingLeft: 20, paddingRight: 20,
        borderBottomLeftRadius: 20,
        borderBottomRightRadius: 20,
        display: "flex",
        flexDirection: "row",
        justifyContent: "flex-end",
        alignItems: "center",
        borderTop: `1px solid ${palette.grayscale.divider}`,
        background: palette.grayscale["100"]
      }}>
        <ButtonBase
          onClick={onClose}
          disableRipple
          disableTouchRipple
          id={googleTags.dineOut.orderBtn.id}
          style={{
            paddingTop: 12,
            paddingBottom: 12,
            paddingLeft: 24,
            paddingRight: 24,
            background: palette.primary["500"],
            borderRadius: 12
          }}
        >
          <Typography style={{ ...typography.body.medium, color: palette.grayscale["100"] }}>
            {t('close')}
          </Typography>
        </ButtonBase>
      </div>
    </Modal>
  )
}

export default withTranslation("common")(RestaurantInfoModal);
