import React, {useEffect, useState} from "react";
import { withTranslation } from '../../../../i18n';
import Modal from "../Modal";
import ModalBar from "../../_navigation/ModalBar";
import shadows from "../../../../styles/shadows";
import palette from "../../../../styles/palette";
import {Slide, Typography, useMediaQuery} from "@material-ui/core";
import typography from "../../../../styles/typography";
import useStyles from "./styles";
import {restaurantsSelectors} from "../../../../redux/selectors";
import {useSelector} from "react-redux";
import {PaymentIllustration160} from "../../../utilities/icons";

const Transition = React.forwardRef(function Transition(props, ref) {
  return <Slide direction="up" ref={ref} {...props} />;
});

const CartPaymentStatusModal = ({ t, open, onClose, titleI18n, values = {} }) => {
  const classes = useStyles();
  const isMobile = useMediaQuery('(max-width:600px)');
  
  const { restaurant = {} } = useSelector(restaurantsSelectors.getRestaurant)
  const { name = '' } = restaurant;
  
  const { status, order = {} } = values ?? {}
  const { number: orderNumber, status: orderStatus } = order;
  const isCompleted = status === 'COMPLETED';
  const isPaymentInProgress = status === 'PAYMENT_REQUESTED'
  
  useEffect(() => {
    if (orderStatus === 'CLOSED') {
    
    }
  }, [orderStatus])
  
  return (
    <Modal
      open={open}
      onClose={onClose}
      maxWidth={false}
      PaperProps={{
        style: { background: "transparent",  ...shadows.large, width: isMobile ? "100%" : 500, maxWidth: isMobile ? "100%" : "95%", margin: 0 } }}
      classes={{
        scrollPaper: isMobile ? classes.scrollPaperMobile : null
      }}
      TransitionComponent={Transition}
    >
      <ModalBar title={t(titleI18n)} onClose={onClose} />
      <div style={{ height: "100%", overflow: "auto", background: palette.grayscale["200"] }}>
        <div style={{ paddingLeft: isMobile ? 20 : 32, paddingRight: isMobile ? 20 : 32, paddingTop: 32, paddingBottom: 20 }}>
          <div style={{
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between",
            background: 'inherit',
            width: "100%",
            borderRadius: 12,
          }}>
            <div style={{ display: "flex", justifyContent: "flex-start", flexDirection: "column", width: "100%" }}>
              {isPaymentInProgress && (
                <div>
                  <div style={{ marginBottom: 32 }}>
                    <div style={{ display: "flex", justifyContent: "center" }}>
                      <PaymentIllustration160 />
                    </div>
                  </div>
                  <Typography style={{ ...typography.large.semiBold, marginLeft: 0, textAlign: "left", marginBottom: 10 }}>
                    {t('order-payment-btn-label-disabled')}
                  </Typography>
                  <Typography style={{ ...typography.body.regular, marginLeft: 0, textAlign: "left", marginBottom: 10 }}>
                    {t('wait-while-we-verify-payment-and-prepare-order', { name })}
                  </Typography>
                </div>
                
              )}
              {isCompleted && (
                <div>
                  <div style={{ marginBottom: 32 }}>
                    <div style={{ display: "flex", justifyContent: "center" }}>
                      <Typography style={{ ...typography.x.paymentAmount }}>{orderNumber}</Typography>
                    </div>
                  </div>
                  <Typography style={{ ...typography.large.semiBold, marginLeft: 0, textAlign: "left", marginBottom: 10 }}>
                    {t('notification-order-items-processing')}
                  </Typography>
                  <Typography style={{ ...typography.body.regular, marginLeft: 0, textAlign: "left", marginBottom: 10 }}>
                    {t('thank-you-for-ordering-here-is-your-order-number', { name })}
                  </Typography>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
      <div style={{
        paddingTop: isMobile ? 0 : 16, paddingBottom: isMobile ? 32 : 16, paddingLeft: 20, paddingRight: 20,
        borderBottomLeftRadius: isMobile ? 0 : 20,
        borderBottomRightRadius: isMobile ? 0 : 20,
        display: "flex",
        flexDirection: "row",
        justifyContent: "flex-end",
        alignItems: "center",
        borderTop: isMobile ? "none" : `1px solid ${palette.grayscale.divider}`,
        background: palette.grayscale[isMobile ? "200" : "100"]
      }} />
    </Modal>
  )
}

export default withTranslation("common")(CartPaymentStatusModal);
