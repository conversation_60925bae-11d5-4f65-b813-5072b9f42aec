import React, {useState} from "react";
import { withTranslation } from '../../../../i18n';
import Modal from "../Modal";
import ModalBar from "../../_navigation/ModalBar";
import shadows from "../../../../styles/shadows";
import palette from "../../../../styles/palette";
import {useSelector} from "react-redux";
import {orderSelectors} from "../../../../redux/selectors";
import {Typography} from "@material-ui/core";
import typography from "../../../../styles/typography";
import ButtonBase from "@material-ui/core/ButtonBase";
import {googleTags} from "../../../../gtm";
import isEmpty from "../../../utilities/isEmpty";
import useStyles from "./styles";
import ListItem from "../../ListItem";
import clsx from "clsx";
import {Paragraph, SmallParagraph, Title} from "../../Text";
import {
  CardIllustration56,
  CardIllustrationGrayScale56,
  CashIllustration56,
  CashIllustrationGrayScale56, InAppIllustration56, InAppIllustrationGrayScale56,
  ReceiptIcon
} from "../../Icons";
import formatNumber from "../../../utilities/formatNumber";
import {DisplayTitleContainer, TextContainer, TitleContainer} from "../../../components/Containers";
import AvatarItem from "../../AvatarItem";
import OrderItem from "../../OrderItem";

export const OrderItemsCategory = ({ icon, text, amount = 0, status }) => {
  const classes = useStyles();
  
  return (
    <div className={classes.orderItemsCategoryWrapper}>
      <div className={classes.orderItemsCategoryLayout}>
        <ListItem icon={icon} label={text} labelStyles={clsx(classes.orderItemsCategoryLabel, classes[status])} />
        <div className={clsx(classes.orderItemsCategoryAmount, classes[status])}>
          <Paragraph>{`${amount.toFixed(2)}€`}</Paragraph>
        </div>
      </div>
    </div>
  );
};

const ReceiptModal = ({ t, open, onClose }) => {
  const classes = useStyles();
  const { order = {} } = useSelector(orderSelectors.getOrder);
  const { summary = {} } = useSelector(orderSelectors.getOrderSummary);
  const { receipts = [] } = summary;
  
  const customerReceipts = receipts.filter((r) => !!r.customer);
  const waiterReceipt = receipts.find((i) => isEmpty(i.customer));
  
  const { participant } = useSelector(orderSelectors.getParticipant);
  const { customerId: currentCustomerId } = participant;
  
  // const [checkout, setCheckout] = useState({});
  
  const [selectedOption, setSelectedOption] = useState('ALL');
  const [selectedTip, setSelectedTip] = useState('TIP5');
  const [selectedMethod, setSelectedMethod] = useState('CASH');
  
  // const {
  //   allAmount,
  //   allAmountTipValue = {},
  //   ownAmount,
  //   ownAmountTipValue = {},
  //   splitAmount,
  //   splitAmountTipValue = {}
  // } = (checkout || {});
  
  const [customTipDrawer, setCustomTipDrawer] = useState(false);
  const [paymentMethodDrawer, setPaymentMethodDrawer] = useState(false);
  
  const [completingPayment, setCompletingPayment] = useState(false);
  
  // useEffect(() => {
  //   getCheckout(order.id).then(({ data }) => setCheckout(data)).catch(() => {});
  // }, [order.id]);
  
  const completePayment = () => {
    setCompletingPayment(true);
  };
  
  // const paymentOptions = [
  //   {
  //     id: 'ALL',
  //     icon: <PayAllIllustrationGrayScale56 />,
  //     selectedIcon: <PayAllIllustration56 />,
  //     title: t('common-payment-option-all'),
  //     description: `${allAmount}€`,
  //     disabled: false
  //   }, {
  //     id: 'OWN',
  //     icon: <MyShareIllustrationGrayScale56 />,
  //     selectedIcon: <MyShareIllustration56 />,
  //     title: t('common-payment-option-own'),
  //     description: `${formatNumber(ownAmount)}€`,
  //     disabled: true
  //   }, {
  //     id: 'SPLIT',
  //     icon: <SplitIllustrationGrayScale56 />,
  //     selectedIcon: <SplitIllustration56 />,
  //     title: t('common-payment-option-split'),
  //     description: `${formatNumber(splitAmount || allAmount)}€`,
  //     disabled: true
  //   }
  // ];
  
  // const { TIP5, TIP10, TIP15, TIP20 } = allAmountTipValue;
  
  // const tipOptions = [
  //   {
  //     id: 'TIP5',
  //     title: '5%',
  //     description: `${formatNumber(TIP5)}€`
  //   }, {
  //     id: 'TIP10',
  //     title: '10%',
  //     description: `${formatNumber(TIP10)}€`
  //   }, {
  //     id: 'TIP15',
  //     title: '15%',
  //     description: `${formatNumber(TIP15)}€`
  //   }, {
  //     id: 'TIP20',
  //     title: '20%',
  //     description: `${formatNumber(TIP20)}€`
  //   }
  // ];
  
  const paymentMethods = [
    {
      id: 'CASH',
      icon: <CashIllustrationGrayScale56 />,
      selectedIcon: <CashIllustration56 />,
      title: t('common-payment-method-cash'),
    }, {
      id: 'CARD',
      icon: <CardIllustrationGrayScale56 />,
      selectedIcon: <CardIllustration56 />,
      title: t('common-payment-method-card'),
    }, {
      id: 'APP',
      icon: <InAppIllustrationGrayScale56 />,
      selectedIcon: <InAppIllustration56 />,
      title: t('common-payment-method-app'),
      disabled: true
    }
  ];
  
  const subTotal = order.totalDue;
  // const resolvedSubtotal = formatNumber(subTotal);
  // const total = subTotal + (selectedTip ? allAmountTipValue[selectedTip] : 0);
  const resolvedTotal = formatNumber(subTotal);
  
  return (
    <Modal
      open={open}
      onClose={onClose}
      maxWidth={false}
      PaperProps={{ style: { background: "transparent",  ...shadows.large, width: 500, maxWidth: "95%", margin: 0 } }}
    >
      <ModalBar title={t('activity-title-payment')} onClose={onClose} />
      <div style={{ height: "100%", overflow: "auto", background: palette.grayscale["200"] }}>
        <div style={{ paddingLeft: 20, paddingRight: 20, paddingTop: 20, paddingBottom: 20 }}>
          <div style={{
            background: palette.grayscale["100"],
            width: "100%",
            padding: 12,
            ...shadows.base,
            borderRadius: 12,
          }}>
            <div className={classes.receipt}>
              <TitleContainer withText divider={false}>
                <div>
                  <TextContainer>
                    <Title weight="medium">
                      {t('order-payment-drawer-order-summary-title')}
                    </Title>
                    <div className={classes.description}>
                      <Paragraph>
                        {t('order-payment-drawer-order-summary-description')}
                      </Paragraph>
                    </div>
                  </TextContainer>
                </div>
              </TitleContainer>
              {customerReceipts.map(({ confirmed, customer = {} }, index) => (
                <div key={customer.id}>
                  <div>
                    {!isEmpty(customer) && (
                      <AvatarItem
                        firstName={customer.firstName ?? ''}
                        lastName={customer.lastName ?? ''}
                      />
                    )}
                    {!isEmpty(confirmed) && (
                      <>
                        <div className={classes.orderItemWrapper}>
                          {confirmed.items
                            .map((i) => (
                              <OrderItem
                                unitPrice={i.unitPrice}
                                name={i.name}
                                qtd={i.qtd}
                                {...i}
                              />
                            ))}
                        </div>
                        <div className={classes.userTotal}>
                          <OrderItemsCategory
                            icon={<ReceiptIcon />}
                            text={t('order-current-order-drawer-users-total-label', { name: customer.firstName })}
                            amount={confirmed ? confirmed.total : 0}
                          />
                        </div>
                      </>
                    )}
                  </div>
                  {receipts.length - 1 > index && <TitleContainer withText />}
                </div>
              ))}
              {!isEmpty(waiterReceipt) && (
                <div>
                  <div>
                    {!isEmpty(waiterReceipt.confirmed) && (
                      <>
                        <div className={classes.orderItemWrapper}>
                          {waiterReceipt.confirmed.items
                            .map((i) => (
                              <OrderItem
                                unitPrice={i.unitPrice}
                                name={i.name}
                                qtd={i.qtd}
                                {...i}
                              />
                            ))}
                        </div>
                      </>
                    )}
                  </div>
                </div>
              )}
            </div>
            <TitleContainer withText divider>
              <div>
                {/*<div className={classes.subtotalRow}>*/}
                {/*  <Paragraph>*/}
                {/*    {t('order-payment-drawer-total-breakdown-subtotal-label')}*/}
                {/*  </Paragraph>*/}
                {/*  <Paragraph>*/}
                {/*    {`${resolvedSubtotal}€`}*/}
                {/*  </Paragraph>*/}
                {/*</div>*/}
                {/*<div className={classes.tipRow}>*/}
                {/*  <Paragraph>*/}
                {/*    {t('order-payment-drawer-total-breakdown-tip-label')}*/}
                {/*  </Paragraph>*/}
                {/*  <Paragraph>*/}
                {/*    {`${selectedTip ? formatNumber(allAmountTipValue[selectedTip]) : '0.00'}€`}*/}
                {/*  </Paragraph>*/}
                {/*</div>*/}
                <div className={classes.totalRow}>
                  <Title weight="medium">
                    {t('pickup-success-order-summary-total-label')}
                  </Title>
                  <Title weight="medium">
                    {`${resolvedTotal}€`}
                  </Title>
                </div>
              </div>
            </TitleContainer>
            <DisplayTitleContainer>
              <div>
                <div className={classes.vatDisclaimer}>
                  <SmallParagraph>
                    {t('order-summary-vat-disclaimer-label')}
                  </SmallParagraph>
                </div>
              </div>
            </DisplayTitleContainer>
          </div>
        </div>
      </div>
      <div style={{
        paddingTop: 16, paddingBottom: 16, paddingLeft: 20, paddingRight: 20,
        borderBottomLeftRadius: 20,
        borderBottomRightRadius: 20,
        display: "flex",
        flexDirection: "row",
        justifyContent: "flex-end",
        alignItems: "center",
        borderTop: `1px solid ${palette.grayscale.divider}`,
        background: palette.grayscale["100"]
      }}>
        <ButtonBase
          onClick={onClose}
          disableRipple
          disableTouchRipple
          id={googleTags.dineOut.orderBtn.id}
          style={{
            paddingTop: 12,
            paddingBottom: 12,
            paddingLeft: 24,
            paddingRight: 24,
            background: palette.primary["500"],
            borderRadius: 12
          }}
        >
          <Typography style={{ ...typography.body.medium, color: palette.grayscale["100"] }}>
            {t('close')}
          </Typography>
        </ButtonBase>
      </div>
    </Modal>
  )
}

export default withTranslation("common")(ReceiptModal);
