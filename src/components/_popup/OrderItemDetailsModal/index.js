import React from "react";
import { withTranslation } from '../../../../i18n';
import Modal from "../Modal";
import ModalBar from "../../_navigation/ModalBar";
import shadows from "../../../../styles/shadows";
import {useDispatch, useSelector} from "react-redux";
import {orderSelectors} from "../../../../redux/selectors";
import isEmpty from "../../../utilities/isEmpty";
import {orderActions} from "../../../../redux/actions";
import MenuItemDetails from "../../MenuItemDetails";

const OrderItemDetailsModal = ({ t, addItem, addNestedItem }) => {
  const dispatch = useDispatch();
  const { order = {} } = useSelector(orderSelectors.getOrder);
  const { item: orderItem, menuItem } = useSelector(orderSelectors.getItemDetails);
  
  const { participant = {} } = useSelector(orderSelectors.getParticipant);
  const { customerId: currentCustomerId } = (participant ?? {});
  
  if (isEmpty(orderItem) || isEmpty(menuItem)) {
    return null;
  }
  
  const { ongoing, id: orderItemId } = orderItem;
  
  const onAddOrderItem = (data) => (ongoing ? addNestedItem(orderItemId, data) : addItem(data));
  
  const onClose = () => dispatch(orderActions.resetItem());
  
  const readOnly = ongoing && (currentCustomerId !== order.customerId);
  
  const open = !!menuItem
  
  return (
    <Modal
      open={open}
      onClose={onClose}
      maxWidth={false}
      PaperProps={{ style: { background: "transparent",  ...shadows.large, width: 500, maxWidth: "95%", margin: 0 } }}
    >
      <ModalBar title={menuItem.name} onClose={onClose} />
      <MenuItemDetails
        {...menuItem}
        addOrderItem={onAddOrderItem}
        orderItem={orderItem}
        readOnly={readOnly}
      />
    </Modal>
  )
}

export default withTranslation("common")(OrderItemDetailsModal);
