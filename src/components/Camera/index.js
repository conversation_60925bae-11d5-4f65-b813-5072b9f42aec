import React from 'react';
import { BrowserQRCodeReader } from '@zxing/library';
import palette from "../../../styles/palette";

let codeReader;
class Camera extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      videoInputs: []
    };
  }

  componentDidMount() {
    codeReader = new BrowserQRCodeReader();
    const { onScan, onError } = this.props;
    let deviceId;

    codeReader
      .listVideoInputDevices()
      .then((videoInputDevices = []) => {
        this.setState({
          videoInputs: videoInputDevices || []
        });
        const videoDevicesLength = videoInputDevices.length;
        if (videoDevicesLength > 0) {
          deviceId = videoInputDevices[videoDevicesLength - 1].deviceId;
        }

        if (deviceId) {
          codeReader.decodeOnceFromVideoDevice(deviceId, 'video')
            .then((result) => onScan(result.text))
            .catch((err) => onError(err));
        } else {
          const constraints = {
            video: {
              aspectRatio: { exact: 1.7777777778 },
              facingMode: 'environment',
              width: { min: 1280, ideal: 1600, max: 1920 },
              height: { min: 720, ideal: 1200, max: 1080 }
            }
          };

          codeReader
            .decodeOnceFromConstraints(constraints, 'video')
            .then((result) => onScan(result.text))
            .catch((err) => onError(err));
        }
      })
      .catch((err) => onError(err));

    // https://github.com/zxing-js/library/issues/254#issuecomment-614529555
  }

  componentWillUnmount() {
    codeReader.reset();
  }

  render() {
    return (
      // eslint-disable-next-line jsx-a11y/media-has-caption
      <video
        id="video"
        style={{
          // width: '100vh',
          maxWidth: '730px',
          width: "90%",
          // height: '100vh',
          background: palette.grayscale["800"],
          marginLeft: '50%',
          transform: 'translateX(-50%)',
          borderRadius: 32
        }}
      />
    );
  }
}

export default Camera;
