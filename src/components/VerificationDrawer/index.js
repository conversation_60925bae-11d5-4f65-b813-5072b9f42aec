/* eslint-disable no-unused-vars,max-len */
import React, { useState } from 'react';
import Container from '@material-ui/core/Container';
import Drawer from 'react-drag-drawer';
import QRCode from 'react-qr-code';
import { useDispatch, useSelector } from 'react-redux';
import ActivityBar from '../ActivityBar';
import { Button, IconButton, MainButton } from '../Buttons';
import { CloseIcon, LeaveIcon, LoginIcon, QRCodeIcon } from '../Icons';
import { TextContainer, TitleContainer } from '../Containers';
import { Paragraph, Title } from '../Text';
import { orderSelectors } from '../../../redux/selectors';
// eslint-disable-next-line import/named
import { withTranslation } from '../../../i18n';
import { tableActions } from '../../../redux/actions';
import isEmpty from '../../utilities/isEmpty';
import { colors } from '../../../styles/theme';
import useStyles from './styles';

const VerificationDrawer = ({ t, open, onClose }) => {
  const classes = useStyles();
  const dispatch = useDispatch();

  const { order = {} } = useSelector(orderSelectors.getOrder);
  const { customer, participants = [], verificationRequestSent } = order;
  const verifiedParticipants = (isEmpty(participants) || participants.length === 1) ? []
    : participants.filter((p) => !!p.canApprove);
  const verifiedParticipant = isEmpty(verifiedParticipants) ? null : verifiedParticipants[0];
  const { participant } = useSelector(orderSelectors.getParticipant);
  const { id: currentParticipantId } = participant;

  const [isVerificationCodeVisible, setVerificationCodeVisible] = useState(isEmpty(customer) || !verifiedParticipant);
  const showVerificationCode = () => setVerificationCodeVisible(true);
  const hideVerificationCode = () => setVerificationCodeVisible(false);
  const toggleVerificationCodeVisible = () => (isVerificationCodeVisible ? hideVerificationCode() : showVerificationCode());

  const [leaveConfirmation, setLeaveConfirmation] = useState(false);
  const remove = (participantId) => {
    dispatch(tableActions.deleteParticipant(order.id, participantId));
  };

  const notify = () => {
    dispatch(tableActions.notifyWaiterForParticipantVerification(order.id));
  };

  return (
    <Drawer
      open={open}
      onRequestClose={onClose}
      modalElementClass={classes.drawerModal}
      containerElementClass={classes.drawerContainer}
    >
      <ActivityBar
        title={t('activity-title-table-verification')}
        color="transparent"
        position="sticky"
        close={(
          <IconButton component="a" edge="start" color="inherit" aria-label="menu" onClick={onClose}>
            <CloseIcon />
          </IconButton>
        )}
      />
      <div className={classes.modalWrapper}>
        <Container>
          {customer && !isVerificationCodeVisible && !isEmpty(verifiedParticipants) && (
            <>
              <TextContainer>
                <Title weight="medium">
                  {t('order-table-verification-drawer-friend-verification-title')}
                </Title>
                <div className={classes.description}>
                  <Paragraph>
                    {t('order-table-verification-drawer-friend-verification-description', { name: customer.firstName })}
                  </Paragraph>
                </div>
              </TextContainer>
              <TitleContainer divider />
            </>
          )}
          <TextContainer>
            <Title weight="medium">
              {t('order-table-verification-drawer-title')}
            </Title>
            <div className={classes.description}>
              <Paragraph>
                {t('order-table-verification-drawer-description')}
              </Paragraph>
            </div>
          </TextContainer>
          {isVerificationCodeVisible && (
            <>
              <div className={classes.qrCodeWrapper}>
                <QRCode
                  value={`lv://2fa/order/${order.id}#${participant.id}`}
                  size={200}
                  renderAs="svg"
                  fgColor={colors.leviee.greyscale.darkGray}
                />
              </div>
              <MainButton onClick={notify} disabled={verificationRequestSent}>
                {t(verificationRequestSent
                  ? 'order-table-verification-drawer-waiter-notified-label'
                  : 'order-table-verification-drawer-notify-waiter-btn')}
              </MainButton>
            </>
          )}
          {customer && !isEmpty(verifiedParticipants) && (
            <div className={classes.showVerificationCodeBtn}>
              <IconButton
                edge="start"
                onClick={toggleVerificationCodeVisible}
                label={isVerificationCodeVisible ? 'Hide verification QR code' : 'Show verification QR code'}
              >
                <QRCodeIcon />
              </IconButton>
            </div>
          )}
          {/*
          {isVerificationCodeVisible && isEmpty(customer) && (
            <TitleContainer divider>
              <div className={classes.iconBtn}>
                <IconButton
                  edge="start"
                  disabled
                  label="Login"
                >
                  <LoginIcon />
                </IconButton>
              </div>
            </TitleContainer>
          )}
          */}
        </Container>
        {/* <TitleContainer divider> */}
        {/*  <Container> */}
        {/*    <div className={classes.iconBtn}> */}
        {/*      <IconButton */}
        {/*        edge="start" */}
        {/*        disabled={leaveConfirmation} */}
        {/*        onClick={() => setLeaveConfirmation(true)} */}
        {/*        label={t(leaveConfirmation ? 'table-management-leave-table-confirmation-label' : 'table-management-leave-table-btn-label')} */}
        {/*      > */}
        {/*        <LeaveIcon /> */}
        {/*      </IconButton> */}
        {/*      <div className={classes.shiftedContent}> */}
        {/*        {leaveConfirmation && ( */}
        {/*          <div className={classes.leaveConfirmation}> */}
        {/*            <div className={classes.actions}> */}
        {/*              <div className={classes.action}> */}
        {/*                <Button color="secondary" onClick={() => remove(currentParticipantId)}>{t('table-management-leave-btn-label')}</Button> */}
        {/*              </div> */}
        {/*              <div className={classes.action}> */}
        {/*                <Button variant="text" onClick={() => setLeaveConfirmation(false)}>{t('common-cancel')}</Button> */}
        {/*              </div> */}
        {/*            </div> */}
        {/*          </div> */}
        {/*        )} */}
        {/*      </div> */}
        {/*    </div> */}
        {/*  </Container> */}
        {/* </TitleContainer> */}
      </div>
    </Drawer>
  );
};

export default withTranslation('common')(VerificationDrawer);
