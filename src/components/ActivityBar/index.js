import React from 'react';
import MuiAppBar from '@material-ui/core/AppBar';
import Container from '@material-ui/core/Container';
import Toolbar from '@material-ui/core/Toolbar';
import clsx from 'clsx';
import { Paragraph } from '../Text';
import useStyles from './styles';

const ActivityBar = ({ title, titleProps = {}, close, back, ...otherProps }) => {
  const classes = useStyles();
  const { style } = titleProps;

  return (
    <MuiAppBar
      position="static"
      elevation={0}
      color="inherit"
      className={classes.appBar}
      {...otherProps}
    >
      <Toolbar disableGutters>
        <Container maxWidth="lg">
          <div className={classes.layout}>
            <div>{back}</div>
            <div className={clsx(classes.titleWrapper, style)}>
              <Paragraph>
                {title}
              </Paragraph>
            </div>
            <div>{close}</div>
          </div>
        </Container>
      </Toolbar>
    </MuiAppBar>
  );
};

export default ActivityBar;
