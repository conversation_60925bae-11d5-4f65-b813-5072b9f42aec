import { makeStyles } from '@material-ui/core/styles';
import palette from "../../../styles/palette";

const useStyles = makeStyles(() => ({
  appBar: {
    background: palette.grayscale["200"]
  },
  layout: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    position: 'relative',
  },
  titleWrapper: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: 'translate(-50%,-50%)',
    whiteSpace: 'nowrap'
  }
}));

export default useStyles;
