import React, { useState } from 'react';
import {i18n} from "../../../i18n";
import ButtonBase from "@material-ui/core/ButtonBase";
import LanguageSelectorModal from "../_popup/LanguageSelectorModal";
import {languages} from "../../const";
import palette from "../../../styles/palette";
import shadows from "../../../styles/shadows";
import {CaretDownIcon} from "../../utilities/icons";

const LanguageSelector = () => {
	const { language } = (i18n || {})
	const { iconSrc } = languages[language] || {}
	const [isOpen, setIsOpen] = useState(false);

	const open = () => setIsOpen(true)
	const close = () => setIsOpen(false)

	const updateLanguage = (lang) => {
		i18n.changeLanguage(lang, () => {
			return false;
		}).then(() => {
			close()
		}).catch(() => {});
	}

	if (language) {
		return (
			<div>
				<ButtonBase
					onClick={open}
					disableRipple
					disableTouchRipple
					style={{
						border: `1px solid ${palette.grayscale["350"]}`,
						borderRadius: 12,
						padding: "5px 5px 5px 7px"
					}}
				>
					<div
						style={{
							marginRight: 6,
							backgroundImage: `url('${iconSrc}')`,
							backgroundRepeat: "no-repeat",
							backgroundSize: "cover",
							backgroundPosition: "center",
							borderRadius: 3,
							...shadows.base,
							height: 16,
							width: 22,
						}}
					/>
					<CaretDownIcon />
				</ButtonBase>
				<LanguageSelectorModal
					titleI18n={'activity-title-welcome'}
					open={isOpen}
					onClose={close}
					setLanguage={updateLanguage}
				/>
			</div>
		);
	}

	return null;
};

export default LanguageSelector;
