import React from 'react';
import Container from '@material-ui/core/Container';
import Typography from '@material-ui/core/Typography';
import { withTranslation } from '../../../i18n';
import MainButton from '../Buttons/MainButton';
import { DisplayTitleContainer } from '../Containers';
import useStyles from './styles';

const Hero = ({ t }) => {
  const classes = useStyles();

  return (
    <div className={classes.wrapper}>
      <Container maxWidth="lg">
        <div className={classes.jumbotron}>
          <div className={classes.textContent}>
            <DisplayTitleContainer>
              <Typography variant="h1" className={classes.white} gutterBottom>
                {t('landing-hero-title')}
              </Typography>
            </DisplayTitleContainer>
            <div className={classes.callToAction}>
              <a href="https://company.leviee.de">
                <MainButton>
                  {t('landing-hero-action-btn-label')}
                </MainButton>
              </a>
            </div>
          </div>
        </div>
      </Container>
    </div>
  );
};

export default withTranslation('common')(Hero);
