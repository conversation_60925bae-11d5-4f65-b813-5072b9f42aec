import { makeStyles } from '@material-ui/core/styles';

const useStyles = makeStyles((theme) => ({
  wrapper: {
    backgroundImage: 'linear-gradient(180deg,rgba(0,0,0,.38) 0,rgba(0,0,0,.38) 3.5%,rgba(0,0,0,.379) 7%,rgba(0,0,0,.377) 10.35%,rgba(0,0,0,.375) 13.85%,rgba(0,0,0,.372) 17.35%,rgba(0,0,0,.369) 20.85%,rgba(0,0,0,.366) 24.35%,rgba(0,0,0,.364) 27.85%,rgba(0,0,0,.361) 31.35%,rgba(0,0,0,.358) 34.85%,rgba(0,0,0,.355) 38.35%,rgba(0,0,0,.353) 41.85%,rgba(0,0,0,.351) 45.35%,rgba(0,0,0,.35) 48.85%,rgba(0,0,0,.353) 52.35%,rgba(0,0,0,.36) 55.85%,rgba(0,0,0,.371) 59.35%,rgba(0,0,0,.385) 62.85%,rgba(0,0,0,.402) 66.35%,rgba(0,0,0,.42) 69.85%,rgba(0,0,0,.44) 73.35%,rgba(0,0,0,.46) 76.85%,rgba(0,0,0,.48) 80.35%,rgba(0,0,0,.498) 83.85%,rgba(0,0,0,.515) 87.35%,rgba(0,0,0,.529) 90.85%,rgba(0,0,0,.54) 94.35%,rgba(0,0,0,.547) 97.85%,rgba(0,0,0,.55)), url("/assets/hero/restaurant-view.jpg")',
    backgroundSize: 'cover',
    backgroundPosition: 'center'
  },
  jumbotron: {
    height: '100vh',
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between'
  },
  textContent: {
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
    maxWidth: 600,
  },
  shotContent: {
    backgroundImage: 'url("/assets/hero/indian-food-on-restaurant-table.jpg")',
    minHeight: '50rem',
    width: '35vw',
    backgroundSize: 'cover',
    position: 'absolute',
    right: 0,
    borderTopLeftRadius: '5vw',
    borderBottomLeftRadius: '25vw'
  },
  white: {
    color: theme.palette.common.white,
    margin: theme.spacing(2, 0, 2, 0)
  },
  callToAction: {
    marginTop: theme.spacing(3),
    display: 'flex',
    '& > *': {
      flexBasis: 140
    }
  },
  callToActionBtn: {
    color: theme.palette.primary.main,
    background: 'rgba(255, 255, 255, 0.84)'
  },
  excerptElementA: {
    position: 'relative',
    right: 250,
    zIndex: 1,
    bottom: -430,
    width: 400
  },
  excerptElementB: {
    position: 'relative',
    right: 130,
    zIndex: 1,
    bottom: -450,
    width: 400
  }
}));

export default useStyles;
