import React from 'react';
// eslint-disable-next-line import/named
import { withTranslation } from '../../../i18n';
import { googleTags } from '../../../gtm';
import palette from "../../../styles/palette";
import {Typography} from "@material-ui/core";
import typography from "../../../styles/typography";
import ButtonBase from "@material-ui/core/ButtonBase";

const StartNewOrderBtn = ({ t, onClick, loading, labelKey, restaurant = {}, onMoreInfo, isOpen, ...otherProps }) => {
  const { disabled } = otherProps;
  delete otherProps.tReady;

  if (loading) {
    return null;
  }
  
  const { name, openingHours } = restaurant;

  
  return (
    <div style={{
      position: "absolute",
      width: "100%",
      bottom: 24,
      paddingLeft: 16,
      paddingRight: 16
    }}>
      <div style={{
        maxWidth: 400,
        margin: "0 auto",
        paddingTop: 12,
        paddingLeft: 16,
        paddingRight: 16,
        paddingBottom: 12,
        backgroundColor: palette.grayscale["800"],
        borderRadius: 12,
      }}>
        {/*<div style={{ paddingBottom: 16 }}>*/}
        {/*  <Typography style={{ ...typography.body.medium, color: palette.grayscale.white }}>*/}
        {/*    {t('your-order')}*/}
        {/*  </Typography>*/}
        {/*  <Typography style={{ ...typography.body.regular, color: palette.transparency.light["80"] }}>*/}
        {/*    {t('you-have-an-order-in-progress-at-restaurant', { name })}*/}
        {/*  </Typography>*/}
        {/*</div>*/}
        <div style={{
          // borderTop: `1px dashed ${palette.transparency.light["20"]}`,
          // paddingTop: 12,
          display: "flex",
          justifyContent: "space-between",
          width: "100%",
          alignItems: "center"
        }}>
          {isOpen && (
            <Typography style={{ ...typography.body.regular, color: palette.grayscale.white }}>
              {t('restaurant-is-open')}
            </Typography>
          )}
          {!isOpen && (
            <div style={{ display: "flex", alignItems: "center" }}>
              <Typography style={{ ...typography.body.regular, color: palette.grayscale.white }}>
                {t('closed')}
              </Typography>
              <span style={{ marginLeft: 4, marginRight: 4, ...typography.extraSmall.regular, color: palette.grayscale.white }}>{` • `}</span>
              <ButtonBase style={{ padding: 0 }} disableRipple disableTouchRipple onClick={onMoreInfo}>
                <Typography style={{ ...typography.body.medium, color: palette.grayscale.white }}>
                  {t('more-info')}
                </Typography>
              </ButtonBase>
            </div>
          )}
          <ButtonBase
            onClick={onClick}
            disableRipple
            disableTouchRipple
            loading={loading ? loading : undefined}
            disabled={loading || disabled}
            id={googleTags.dineOut.orderBtn.id}
            style={{
              paddingTop: 12,
              paddingBottom: 12,
              paddingLeft: 24,
              paddingRight: 24,
              background: palette.primary["500"],
              borderRadius: 12
            }}
          >
            <Typography style={{ ...typography.body.medium, color: palette.grayscale["100"] }}>
              {!loading && t(labelKey)}
            </Typography>
          </ButtonBase>
        </div>
      </div>
    </div>
  );
};

export default withTranslation('common')(StartNewOrderBtn);
