import React from 'react';
import { useDispatch, useSelector } from 'react-redux';
import IconButton from '@material-ui/core/IconButton';
import Container from '@material-ui/core/Container';
import Overlay from '../Overlay';
import ActivityBar from '../ActivityBar';
import { CloseIcon } from '../Icons';
import MenuItemDetails from '../MenuItemDetails';
import isEmpty from '../../utilities/isEmpty';
import { orderSelectors } from '../../../redux/selectors';
import useStyles from './styles';
import { orderActions } from '../../../redux/actions';

const MenuItemDetailsOverlay = ({ addItem, addNestedItem }) => {
  const classes = useStyles();

  const dispatch = useDispatch();
  const { order = {} } = useSelector(orderSelectors.getOrder);
  const { item: orderItem, menuItem } = useSelector(orderSelectors.getItemDetails);

  const { participant = {} } = useSelector(orderSelectors.getParticipant);
  const { customerId: currentCustomerId } = (participant ?? {});

  if (isEmpty(orderItem) || isEmpty(menuItem)) {
    return null;
  }

  const { ongoing, id: orderItemId } = orderItem;

  const onAddOrderItem = (data) => (ongoing ? addNestedItem(orderItemId, data) : addItem(data));

  const onClose = () => dispatch(orderActions.resetItem());

  const readOnly = ongoing && (currentCustomerId !== order.customerId);

  return (
    <Overlay open={!!menuItem}>
      <ActivityBar
        title={menuItem.name}
        close={(
          <IconButton component="a" edge="end" className={classes.menuButton} color="inherit" aria-label="menu" onClick={onClose}>
            <CloseIcon />
          </IconButton>
        )}
      />
      <Container>
        <MenuItemDetails
          {...menuItem}
          addOrderItem={onAddOrderItem}
          orderItem={orderItem}
          readOnly={readOnly}
        />
      </Container>
    </Overlay>
  );
};

export default MenuItemDetailsOverlay;
