import React from 'react';
import SvgIcon from '@material-ui/core/SvgIcon';
import { colors } from '../../../styles/theme';

export const MenuIcon = (props) => (
  <SvgIcon {...props}>
    <path d="M4 7.5H20" stroke="#04172F" strokeWidth="1.2" strokeLinecap="round" />
    <path d="M4 12.5H18" stroke="#04172F" strokeWidth="1.2" strokeLinecap="round" />
    <path d="M4 17.5H19" stroke="#04172F" strokeWidth="1.2" strokeLinecap="round" />
  </SvgIcon>
);

export const MenuIconLight = (props) => (
  <SvgIcon {...props}>
    <path d="M4 7.5H20" stroke="#FFF" strokeWidth="1.2" strokeLinecap="round" />
    <path d="M4 12.5H18" stroke="#FFF" strokeWidth="1.2" strokeLinecap="round" />
    <path d="M4 17.5H19" stroke="#FFF" strokeWidth="1.2" strokeLinecap="round" />
  </SvgIcon>
);

export const SearchIcon = (props) => (
  <SvgIcon {...props}>
    <g opacity="0.9">
      <circle cx="10" cy="10" r="6.4" stroke="#04172F" strokeWidth="1.2" fill="none" />
      <path d="M15 15L19.5 19.5" stroke="#04172F" strokeWidth="1.2" strokeLinecap="round" />
    </g>
  </SvgIcon>
);

export const SearchIconLight = (props) => (
  <SvgIcon {...props}>
    <g opacity="0.9">
      <circle cx="10" cy="10" r="6.4" stroke="#FFF" strokeWidth="1.2" fill="none" />
      <path d="M15 15L19.5 19.5" stroke="#FFF" strokeWidth="1.2" strokeLinecap="round" />
    </g>
  </SvgIcon>
);

export const ReceiptInProgressIcon = (props) => (
  <SvgIcon {...props}>
    <path d="M8 22H18C18.5523 22 19 21.5523 19 21V18V10V8C19 6.89543 18.1046 6 17 6L7 6V13V21C7 21.5523 7.44772 22 8 22Z" stroke="#04172F" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" fill="none" />
    <line x1="10.6" y1="10.4" x2="13.4" y2="10.4" stroke="#04172F" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" />
    <line x1="10.6" y1="13.4" x2="15.4" y2="13.4" stroke="#04172F" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" />
    <line x1="10.6" y1="16.4" x2="14.4" y2="16.4" stroke="#04172F" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M4 8C4 6.89543 4.89543 6 6 6H7V12H5C4.44772 12 4 11.5523 4 11V8Z" fill="#E0E3E7" stroke="#04172F" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" />
    <circle cx="17" cy="6" r="5" fill="#FFDFB0" stroke="white" strokeWidth="2" />
    <circle cx="17" cy="6" r="4" fill="#FFDFB0" stroke="#FFC475" strokeWidth="1.2" />
  </SvgIcon>
);

export const ReceiptCompletedIcon = (props) => (
  <SvgIcon {...props}>
    <path d="M8 22H18C18.5523 22 19 21.5523 19 21V18V10V8C19 6.89543 18.1046 6 17 6L7 6V13V21C7 21.5523 7.44772 22 8 22Z" stroke="#04172F" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" fill="none" />
    <line x1="10.6" y1="10.4" x2="13.4" y2="10.4" stroke="#04172F" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" />
    <line x1="10.6" y1="13.4" x2="15.4" y2="13.4" stroke="#04172F" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" />
    <line x1="10.6" y1="16.4" x2="14.4" y2="16.4" stroke="#04172F" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M4 8C4 6.89543 4.89543 6 6 6H7V12H5C4.44772 12 4 11.5523 4 11V8Z" fill="#E0E3E7" stroke="#04172F" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" />
    <circle cx="17" cy="6" r="5" fill="#C9EFE3" stroke="white" strokeWidth="2" />
    <circle cx="17" cy="6" r="4" fill="#C9EFE3" stroke="#5CBAA7" strokeWidth="1.2" />
  </SvgIcon>
);

export const ReceiptIcon = (props) => (
  <SvgIcon {...props}>
    <path d="M8 22H18C18.5523 22 19 21.5523 19 21V18V10V8C19 6.89543 18.1046 6 17 6L7 6V13V21C7 21.5523 7.44772 22 8 22Z" stroke="#04172F" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" fill="none" />
    <line x1="10.6" y1="10.4" x2="13.4" y2="10.4" stroke="#04172F" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" />
    <line x1="10.6" y1="13.4" x2="15.4" y2="13.4" stroke="#04172F" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" />
    <line x1="10.6" y1="16.4" x2="14.4" y2="16.4" stroke="#04172F" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M4 8C4 6.89543 4.89543 6 6 6H7V12H5C4.44772 12 4 11.5523 4 11V8Z" fill="#E0E3E7" stroke="#04172F" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" />
  </SvgIcon>
);

export const QRCodeIcon = (props) => (
  <SvgIcon {...props}>
    <rect x="3.5" y="3.5" width="5" height="5" stroke="#04172F" fill="none" />
    <rect x="3.5" y="15.5" width="5" height="5" stroke="#04172F" fill="none" />
    <rect x="15.5" y="3.5" width="5" height="5" stroke="#04172F" fill="none" />
    <rect x="17" y="11" width="2" height="2" fill="#04172F" />
    <rect x="13" y="11" width="2" height="2" fill="#04172F" />
    <rect x="11" y="13" width="2" height="2" fill="#04172F" />
    <rect x="9" y="11" width="2" height="2" fill="#04172F" />
    <rect x="11" y="9" width="2" height="2" fill="#04172F" />
    <rect x="11" y="7" width="2" height="2" fill="#04172F" />
    <rect x="13" y="5" width="2" height="2" fill="#04172F" />
    <rect x="9" y="5" width="2" height="2" fill="#04172F" />
    <rect x="11" y="3" width="2" height="2" fill="#04172F" />
    <rect x="7" y="11" width="2" height="2" fill="#04172F" />
    <rect x="5" y="13" width="2" height="2" fill="#04172F" />
    <rect x="3" y="11" width="2" height="2" fill="#04172F" />
    <rect x="15" y="9" width="2" height="2" fill="#04172F" />
    <rect x="15" y="13" width="2" height="2" fill="#04172F" />
    <rect x="17" y="13" width="2" height="2" fill="#04172F" />
    <rect x="19" y="15" width="2" height="2" fill="#04172F" />
    <rect x="17" y="17" width="2" height="2" fill="#04172F" />
    <rect x="19" y="19" width="2" height="2" fill="#04172F" />
    <rect x="15" y="17" width="2" height="2" fill="#04172F" />
    <rect x="13" y="19" width="2" height="2" fill="#04172F" />
    <rect x="11" y="17" width="2" height="2" fill="#04172F" />
  </SvgIcon>
);

export const QRCodeIconLight = (props) => (
  <SvgIcon {...props}>
    <rect x="3.5" y="3.5" width="5" height="5" stroke="#FFF" fill="none" />
    <rect x="3.5" y="15.5" width="5" height="5" stroke="#FFF" fill="none" />
    <rect x="15.5" y="3.5" width="5" height="5" stroke="#FFF" fill="none" />
    <rect x="17" y="11" width="2" height="2" fill="#FFF" />
    <rect x="13" y="11" width="2" height="2" fill="#FFF" />
    <rect x="11" y="13" width="2" height="2" fill="#FFF" />
    <rect x="9" y="11" width="2" height="2" fill="#FFF" />
    <rect x="11" y="9" width="2" height="2" fill="#FFF" />
    <rect x="11" y="7" width="2" height="2" fill="#FFF" />
    <rect x="13" y="5" width="2" height="2" fill="#FFF" />
    <rect x="9" y="5" width="2" height="2" fill="#FFF" />
    <rect x="11" y="3" width="2" height="2" fill="#FFF" />
    <rect x="7" y="11" width="2" height="2" fill="#FFF" />
    <rect x="5" y="13" width="2" height="2" fill="#FFF" />
    <rect x="3" y="11" width="2" height="2" fill="#FFF" />
    <rect x="15" y="9" width="2" height="2" fill="#FFF" />
    <rect x="15" y="13" width="2" height="2" fill="#FFF" />
    <rect x="17" y="13" width="2" height="2" fill="#FFF" />
    <rect x="19" y="15" width="2" height="2" fill="#FFF" />
    <rect x="17" y="17" width="2" height="2" fill="#FFF" />
    <rect x="19" y="19" width="2" height="2" fill="#FFF" />
    <rect x="15" y="17" width="2" height="2" fill="#FFF" />
    <rect x="13" y="19" width="2" height="2" fill="#FFF" />
    <rect x="11" y="17" width="2" height="2" fill="#FFF" />
  </SvgIcon>
);

export const ScanQRCodeIconLight = (props) => (
  <SvgIcon {...props} viewBox="0 0 64 64" style={{ width: 24, height: 24 }}>
    <g
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth="3"
      transform="translate(0.5 0.5)"
      fill="#ffffff"
      stroke="#ffffff"
    >
      <g transform="translate(8 8)">
        <polygon fill="none" stroke="#ffffff" strokeMiterlimit="10" points="18,18 2,18 2,2 18,2 18,2 " />
        <polyline fill="none" stroke="#ffffff" strokeMiterlimit="10" points="46,38 46,30 36,30 36,27 " />
        <line fill="none" stroke="#ffffff" strokeMiterlimit="10" x1="46" y1="46" x2="30" y2="46" />
        <polygon data-color="color-2" fill="none" strokeMiterlimit="10" points=" 12,12 8,12 8,8 12,8 12,12 " />
        <polygon fill="none" stroke="#ffffff" strokeMiterlimit="10" points="46,18 30,18 30,2 46,2 46,2 " />
        <polygon data-color="color-2" fill="none" strokeMiterlimit="10" points=" 40,12 36,12 36,8 40,8 40,12 " />
        <polygon fill="none" stroke="#ffffff" strokeMiterlimit="10" points="18,46 2,46 2,30 18,30 18,30 " />
        <polygon data-color="color-2" fill="none" strokeMiterlimit="10" points=" 12,40 8,40 8,36 12,36 12,40 " />
        <polygon data-color="color-2" fill="none" strokeMiterlimit="10" points=" 40,40 36,40 36,36 40,36 40,40 " />
        <polyline fill="none" stroke="#ffffff" strokeMiterlimit="10" points="25,41 25,37 29,37 29,27 " />
        <polyline fill="none" stroke="#ffffff" strokeMiterlimit="10" points="17,25 17,23 24,23 24,27 " />
        <polyline fill="none" stroke="#ffffff" strokeMiterlimit="10" points="22,11 25,11 25,6 " />
        <line fill="none" stroke="#ffffff" strokeMiterlimit="10" x1="43" y1="24" x2="43" y2="30" />
        <polyline fill="none" stroke="#ffffff" strokeMiterlimit="10" points="2,22 2,24 8,24 " />
      </g>
    </g>
  </SvgIcon>
);

export const OrderIconLight = (props) => (
  <SvgIcon {...props}>
    <path d="M8 20H18C18.5523 20 19 19.5523 19 19V16V8V6C19 4.89543 18.1046 4 17 4L7 4V11V19C7 19.5523 7.44772 20 8 20Z" stroke="#FFF" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" fill="none" />
    <line x1="10.6" y1="8.4" x2="13.4" y2="8.4" stroke="#FFF" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" />
    <line x1="10.6" y1="11.4" x2="15.4" y2="11.4" stroke="#FFF" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" />
    <line x1="10.6" y1="14.4" x2="14.4" y2="14.4" stroke="#FFF" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" />
  </SvgIcon>
);

export const TipIcon = (props) => (
  <SvgIcon {...props}>
    <path d="M14.5935 6H9.40651C8.00042 6 6.78298 6.9766 6.47795 8.34921L5 15V18C5 19.1046 5.89543 20 7 20H17C18.1046 20 19 19.1046 19 18V15L17.522 8.34921C17.217 6.9766 15.9996 6 14.5935 6Z" fill="#F0F1F3" stroke="#04172F" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" />
    <circle cx="14" cy="7" r="5" fill="#FFDFB0" stroke="white" strokeWidth="2" />
    <circle cx="14" cy="7" r="4" fill="#CCD1D7" stroke="#919CA9" strokeWidth="1.2" />
    <path d="M5 15H8.38197C8.76074 15 9.107 15.214 9.27639 15.5528L9.44721 15.8944C9.786 16.572 10.4785 17 11.2361 17H12.7639C13.5215 17 14.214 16.572 14.5528 15.8944L14.7236 15.5528C14.893 15.214 15.2393 15 15.618 15H19V18C19 19.1046 18.1046 20 17 20H7C5.89543 20 5 19.1046 5 18V15Z" fill="white" stroke="#04172F" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" />
  </SvgIcon>
);

export const TipActiveIcon = (props) => (
  <SvgIcon {...props}>
    <path d="M14.5935 6H9.40651C8.00042 6 6.78298 6.9766 6.47795 8.34921L5 15V18C5 19.1046 5.89543 20 7 20H17C18.1046 20 19 19.1046 19 18V15L17.522 8.34921C17.217 6.9766 15.9996 6 14.5935 6Z" fill="#F0F1F3" stroke="#04172F" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" />
    <circle cx="14" cy="7" r="5" fill="#FFDFB0" stroke="white" strokeWidth="2" />
    <circle cx="14" cy="7" r="4" fill="#FFDFB0" stroke="#FFAE33" strokeWidth="1.2" />
    <path d="M5 15H8.38197C8.76074 15 9.107 15.214 9.27639 15.5528L9.44721 15.8944C9.786 16.572 10.4785 17 11.2361 17H12.7639C13.5215 17 14.214 16.572 14.5528 15.8944L14.7236 15.5528C14.893 15.214 15.2393 15 15.618 15H19V18C19 19.1046 18.1046 20 17 20H7C5.89543 20 5 19.1046 5 18V15Z" fill="white" stroke="#04172F" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" />
  </SvgIcon>
);

export const ArrowBackIcon = (props) => (
  <SvgIcon {...props}>
    <path d="M7.125 12L17.25 12" stroke={colors.leviee.main.dark} strokeLinecap="round" strokeLinejoin="round" fill="none" />
    <path d="M11.625 16.875L6.75 12L11.625 7.125" stroke={colors.leviee.main.dark} strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" fill="none" />
  </SvgIcon>
);

export const ArrowBackIconWhite = (props) => (
  <SvgIcon {...props}>
    <path d="M7.125 12L17.25 12" stroke={colors.leviee.main.white} strokeLinecap="round" strokeLinejoin="round" fill="none" />
    <path d="M11.625 16.875L6.75 12L11.625 7.125" stroke={colors.leviee.main.white} strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" fill="none" />
  </SvgIcon>
);

export const ArrowForwardIcon = (props) => (
  <SvgIcon {...props}>
    <path d="M16.875 12L6.75 12" stroke={colors.leviee.main.dark} strokeLinecap="round" strokeLinejoin="round" fill="none" />
    <path d="M12.375 16.875L17.25 12L12.375 7.125" stroke={colors.leviee.main.dark} strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" fill="none" />
  </SvgIcon>
);

export const ArrowForwardIconWhite = (props) => (
  <SvgIcon {...props}>
    <path d="M16.875 12L6.75 12" stroke={colors.leviee.main.white} strokeLinecap="round" strokeLinejoin="round" fill="none" />
    <path d="M12.375 16.875L17.25 12L12.375 7.125" stroke={colors.leviee.main.white} strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" fill="none" />
  </SvgIcon>
);

export const CloseIcon = (props) => (
  <SvgIcon {...props}>
    <path d="M6.5 6.5L17.5 17.5" stroke="#04172F" strokeWidth="1.2" strokeLinecap="round" />
    <path d="M17.5 6.5L6.5 17.5" stroke="#04172F" strokeWidth="1.4" strokeLinecap="round" />
  </SvgIcon>
);

export const CheckIconRound = (props) => (
  <SvgIcon {...props} viewBox="0 0 28 28" style={{ width: 24, height: 24 }}>
    <circle cx="14" cy="14" r="13" fill="#04172F" stroke="white" strokeWidth="2" />
    <path d="M9 14L12.5 17.5L19.5 10.5" stroke="#FEFEFE" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
  </SvgIcon>
);

export const CloseIconLight = (props) => (
  <SvgIcon {...props}>
    <path d="M6.5 6.5L17.5 17.5" stroke="#FFF" strokeWidth="1.2" strokeLinecap="round" />
    <path d="M17.5 6.5L6.5 17.5" stroke="#FFF" strokeWidth="1.4" strokeLinecap="round" />
  </SvgIcon>
);

export const CloseIcon20 = (props) => (
  <SvgIcon {...props}>
    <path d="M6.46436 6.46447L13.5354 13.5355" stroke="white" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M13.5354 6.46447L6.46433 13.5355" stroke="white" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" />
  </SvgIcon>
);

export const TableVerificationIcon20 = (props) => (
  <SvgIcon {...props} viewBox="0 0 20 20" style={{ width: 20, height: 20 }}>
    <path d="M18 11H2L5 5H9.5" stroke="#04172F" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" fill="none" />
    <line x1="4.6" y1="11.6" x2="4.6" y2="16.4" stroke="#04172F" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" />
    <line x1="15.6" y1="11.6" x2="15.6" y2="16.4" stroke="#04172F" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M12.5996 11.6001L12.5996 13.4001" stroke="#04172F" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M7.59961 11.6001L7.59961 13.4001" stroke="#04172F" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M10.183 7.42332C9.96759 7.72852 9.94024 8.12836 10.1121 8.46006C10.284 8.79176 10.6264 9 11 9H19C19.3638 9 19.699 8.80238 19.8751 8.484C20.0512 8.16561 20.0404 7.77671 19.8471 7.46851L16.0824 1.46851C15.9046 1.18514 15.5968 1.00945 15.2624 1.00037C14.928 0.991287 14.6112 1.15002 14.4183 1.42332L10.183 7.42332Z" fill="#FFDFB0" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M11.5565 7.21166L14.8023 2.61338C15.0082 2.32172 15.4446 2.33357 15.6343 2.63597L18.5195 7.23425C18.7285 7.56725 18.4891 8 18.096 8H11.965C11.5596 8 11.3227 7.54286 11.5565 7.21166Z" fill="#FFDFB0" stroke="#FFC475" strokeWidth="1.2" />
  </SvgIcon>
);

export const TableVerificationIconLight20 = (props) => (
  <SvgIcon {...props} viewBox="0 0 20 20" style={{ width: 20, height: 20 }}>
    <path d="M18 11H2L5 5H9.5" stroke="#FFF" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" fill="none" />
    <line x1="4.6" y1="11.6" x2="4.6" y2="16.4" stroke="#FFF" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" />
    <line x1="15.6" y1="11.6" x2="15.6" y2="16.4" stroke="#FFF" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M12.5996 11.6001L12.5996 13.4001" stroke="#FFF" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M7.59961 11.6001L7.59961 13.4001" stroke="#FFF" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M10.183 7.42332C9.96759 7.72852 9.94024 8.12836 10.1121 8.46006C10.284 8.79176 10.6264 9 11 9H19C19.3638 9 19.699 8.80238 19.8751 8.484C20.0512 8.16561 20.0404 7.77671 19.8471 7.46851L16.0824 1.46851C15.9046 1.18514 15.5968 1.00945 15.2624 1.00037C14.928 0.991287 14.6112 1.15002 14.4183 1.42332L10.183 7.42332Z" fill="#FFDFB0" stroke="#000" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M11.5565 7.21166L14.8023 2.61338C15.0082 2.32172 15.4446 2.33357 15.6343 2.63597L18.5195 7.23425C18.7285 7.56725 18.4891 8 18.096 8H11.965C11.5596 8 11.3227 7.54286 11.5565 7.21166Z" fill="#FFDFB0" stroke="#FFC475" strokeWidth="1.2" />
  </SvgIcon>
);

export const CheckIconRound20 = (props) => (
  <SvgIcon {...props} viewBox="0 0 24 24" style={{ width: 20, height: 20 }}>
    <circle cx="12" cy="12" r="11" fill="#04172F" stroke="white" strokeWidth="2" />
    <path d="M7.5 12L10.5 15L16.5 9" stroke="#FEFEFE" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
  </SvgIcon>
);

export const PendingIconRound20 = (props) => (
  <SvgIcon {...props} viewBox="0 0 24 24" style={{ width: 20, height: 20 }}>
    <circle cx="12" cy="12" r="11" fill="#FFAE33" stroke="white" strokeWidth="2" />
    <path d="M12.116 6.93301L16.4461 9.43301L15.917 10.3494C15.4614 11.1386 14.6716 11.678 13.7707 11.8152L12.031 12.0801L11.3907 10.4411C11.059 9.59222 11.1312 8.63863 11.5869 7.84938L12.116 6.93301Z" stroke="white" strokeWidth="1.2" fill="#FFAE33" />
    <path d="M11.25 6.43301C11.3881 6.19387 11.6939 6.11193 11.933 6.25L17.1292 9.25C17.3683 9.38807 17.4502 9.69387 17.3122 9.93301C17.1741 10.1722 16.8683 10.2541 16.6292 10.116L11.433 7.11603C11.1939 6.97795 11.1119 6.67216 11.25 6.43301Z" fill="white" />
    <path d="M11.9462 17.2272L7.61604 14.7272L8.1451 13.8109C8.60078 13.0216 9.39052 12.4823 10.2915 12.3451L12.0311 12.0801L12.6715 13.7192C13.0031 14.568 12.9309 15.5216 12.4752 16.3109L11.9462 17.2272Z" stroke="white" strokeWidth="1.2" fill="#FFAE33" />
    <path d="M12.8121 17.7272C12.6741 17.9664 12.3683 18.0483 12.1291 17.9103L6.93297 14.9103C6.69382 14.7722 6.61189 14.4664 6.74996 14.2272C6.88803 13.9881 7.19382 13.9062 7.43297 14.0442L12.6291 17.0442C12.8683 17.1823 12.9502 17.4881 12.8121 17.7272Z" fill="white" />
  </SvgIcon>
);

export const ArrowRightIcon20 = (props) => (
  <SvgIcon {...props} viewBox="0 0 24 24" style={{ width: 20, height: 20 }}>
    <path d="M16.875 12L6.75 12" stroke="#04172F" strokeLinecap="round" strokeLinejoin="round" fill="none" />
    <path d="M12.375 16.875L17.25 12L12.375 7.125" stroke="#04172F" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" fill="none" />
  </SvgIcon>
);

export const InfoIcon = (props) => (
  <SvgIcon {...props}>
    <circle cx="12" cy="12" r="9" stroke="#04172F" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" fill="none" />
    <path d="M12 11V17" stroke="#04172F" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" />
    <circle cx="12" cy="7.75" r="0.75" fill="#04172F" />
  </SvgIcon>
);

export const LeaveIcon = (props) => (
  <SvgIcon {...props}>
    <path d="M11 4H6C4.89543 4 4 4.89543 4 6V18C4 19.1046 4.89543 20 6 20H11C12.1046 20 13 19.1046 13 18V16V8V6C13 4.89543 12.1046 4 11 4Z" fill="#E0E3E7" stroke="#CCD1D7" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M16.7678 16.9173L21.7175 11.9675M21.7175 11.9675L16.7678 7.01777M21.7175 11.9675L8.28249 11.9675" stroke="#04172F" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" />
  </SvgIcon>
);

export const LoginIcon = (props) => (
  <SvgIcon {...props}>
    <path d="M10 20L18 20C19.1046 20 20 19.1046 20 18L20 6C20 4.89543 19.1046 4 18 4L10 4C8.89543 4 8 4.89543 8 6L8 18C8 19.1046 8.89543 20 10 20Z" fill="#E0E3E7" stroke="#CCD1D7" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M12.7678 15.9173L16.7175 11.9675M16.7175 11.9675L12.7678 8.01777M16.7175 11.9675L3.28249 11.9675" stroke="#04172F" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" />
  </SvgIcon>
);

export const FavouriteIcon = (props) => (
  <SvgIcon {...props}>
    <path d="M19.9298 5.41452C19.4815 4.96607 18.9493 4.61034 18.3636 4.36763C17.7778 4.12492 17.15 4 16.516 4C15.8819 4 15.2541 4.12492 14.6683 4.36763C14.0826 4.61034 13.5504 4.96607 13.1022 5.41452L12.1719 6.34476L11.2417 5.41452C10.3363 4.50912 9.10828 4.00047 7.82786 4.00047C6.54743 4.00047 5.31945 4.50912 4.41405 5.41452C3.50865 6.31992 3 7.5479 3 8.82833C3 10.1088 3.50865 11.3367 4.41405 12.2421L5.34429 13.1724L12.1719 20L18.9995 13.1724L19.9298 12.2421C20.3782 11.7939 20.7339 11.2617 20.9767 10.676C21.2194 10.0902 21.3443 9.46237 21.3443 8.82833C21.3443 8.19428 21.2194 7.56645 20.9767 6.9807C20.7339 6.39494 20.3782 5.86275 19.9298 5.41452V5.41452Z" stroke="#04172F" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" fill="none" />
  </SvgIcon>
);

export const ShareIcon = (props) => (
  <SvgIcon {...props}>
    <path d="M20 15.7333V18C20 19.1046 19.1046 20 18 20H6C4.89543 20 4 19.1046 4 18V10.4V6C4 4.89543 4.89543 4 6 4H7.5" stroke="#04172F" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" fill="none" />
    <path d="M20 11V4M20 4H13M20 4L10.5 13.5" stroke="#04172F" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" />
  </SvgIcon>
);

export const PhoneIcon = (props) => (
  <SvgIcon {...props}>
    <path d="M18 5.6H6" stroke="#CCD1D7" strokeWidth="1.2" />
    <path d="M6 5C6 3.34315 7.34315 2 9 2H15C16.6569 2 18 3.34315 18 5H6Z" fill="#E0E3E7" />
    <rect x="18.4" y="2.6" width="18.8" height="12.8" rx="2.4" transform="rotate(90 18.4 2.6)" stroke="#04172F" strokeWidth="1.2" fill="none" />
    <rect x="11" y="18" width="2" height="1.2" rx="0.6" fill="#04172F" />
  </SvgIcon>
);

export const RestaurantTypeIcon = (props) => (
  <SvgIcon {...props}>
    <path d="M12 6C6.5 6 4 10.5817 4 15C4 15 7.5 16 12 16C16.5 16 20 15 20 15C20 10.5817 17.5 6 12 6Z" fill="white" stroke="#04172F" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M12 4V6" stroke="#04172F" strokeWidth="1.2" strokeLinecap="round" />
    <path d="M3 18.5C3 18.5 7 19.5 12 19.5C17 19.5 21 18.5 21 18.5" stroke="#04172F" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" />
  </SvgIcon>
);

export const EuroIcon = (props) => (
  <SvgIcon {...props}>
    <path d="M17 6.60317C16.1726 6.21679 15.2432 6 14.2609 6C10.8031 6 8 8.68629 8 12C8 15.3137 10.8031 18 14.2609 18C15.2432 18 16.1726 17.7832 17 17.3968" stroke="#04172F" strokeWidth="1.2" strokeLinecap="round" fill="none" />
    <line x1="6.6" y1="10.9" x2="14.4" y2="10.9" stroke="#04172F" strokeWidth="1.2" strokeLinecap="round" />
    <line x1="6.6" y1="13.9" x2="14.4" y2="13.9" stroke="#04172F" strokeWidth="1.2" strokeLinecap="round" />
  </SvgIcon>
);

export const PinIcon = (props) => (
  <SvgIcon {...props}>
    <circle cx="12" cy="6" r="3" stroke="#04172F" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M18.5 17.5C18.5 18.1751 17.9397 18.9209 16.7261 19.5277C15.5424 20.1195 13.8722 20.5 12 20.5C10.1278 20.5 8.45755 20.1195 7.27386 19.5277C6.06026 18.9209 5.5 18.1751 5.5 17.5C5.5 16.8249 6.06026 16.0791 7.27386 15.4723C8.45755 14.8805 10.1278 14.5 12 14.5C13.8722 14.5 15.5424 14.8805 16.7261 15.4723C17.9397 16.0791 18.5 16.8249 18.5 17.5Z" fill="#E0E3E7" stroke="#CCD1D7" />
    <path d="M12.1001 9.6L12.1001 17.4" stroke="#04172F" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" />
  </SvgIcon>
);

export const CalendarIcon = (props) => (
  <SvgIcon {...props}>
    <path d="M4 7C4 5.89543 4.89543 5 6 5H18C19.1046 5 20 5.89543 20 7V10H4V7Z" fill="#E0E3E7" stroke="#CCD1D7" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" />
    <rect x="4" y="5" width="16" height="14" rx="2" stroke="#04172F" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" fill="none" />
    <path d="M7.5 3V5" stroke="#04172F" strokeWidth="1.2" strokeLinecap="round" />
    <path d="M16.5 3V5" stroke="#04172F" strokeWidth="1.2" strokeLinecap="round" />
  </SvgIcon>
);

export const ClockIcon = (props) => (
  <SvgIcon {...props}>
    <circle cx="12" cy="12" r="8" stroke="#04172F" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" fill="none" />
    <path d="M17 12C17 9.23858 14.7614 7 12 7C11.8313 7 11.6645 7.00836 11.5 7.02469V13L15 16.0004C16.2144 15.0882 17 13.6358 17 12Z" fill="#E0E3E7" stroke="#CCD1D7" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M11.5 7V13L15 16" stroke="#04172F" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" />
  </SvgIcon>
);

export const CloseIconDark16 = (props) => (
  <SvgIcon {...props} viewBox="0 0 16 16" style={{ width: 16, height: 16 }}>
    <path d="M11.5355 4.46447L4.46445 11.5355" stroke={colors.leviee.greyscale.darkGray} strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M11.5355 11.5355L4.46446 4.46447" stroke={colors.leviee.greyscale.darkGray} strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" />
  </SvgIcon>
);

export const PlusIconLight16 = (props) => (
  <SvgIcon {...props} viewBox="0 0 16 16" style={{ width: 16, height: 16 }}>
    <path d="M8 3V13" stroke={colors.leviee.greyscale.midGray} strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M13 8L3 8" stroke={colors.leviee.greyscale.midGray} strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" />
  </SvgIcon>
);

export const CheckIconWhite16 = (props) => (
  <SvgIcon {...props} viewBox="0 0 16 16" style={{ width: 16, height: 16 }}>
    <path d="M3 8L6.5 11.5L13.5 4.5" stroke={colors.leviee.main.white} strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" fill="none" />
  </SvgIcon>
);

export const MinusIconLight16 = (props) => (
  <SvgIcon {...props} viewBox="0 0 16 16" style={{ width: 16, height: 16 }}>
    <path d="M13 8L3 8" stroke={colors.leviee.greyscale.midGray} strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" />
  </SvgIcon>
);

export const MinusIconDark16 = (props) => (
  <SvgIcon {...props} viewBox="0 0 16 16" style={{ width: 16, height: 16 }}>
    <path d="M13 8L3 8" stroke={colors.leviee.greyscale.darkGray} strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" />
  </SvgIcon>
);

export const EuroIconDark16 = (props) => (
  <SvgIcon {...props} viewBox="0 0 16 16" style={{ width: 16, height: 16 }}>
    <path d="M11 3.50265C10.3565 3.18066 9.63357 3 8.86957 3C6.18018 3 4 5.23858 4 8C4 10.7614 6.18018 13 8.86957 13C9.63357 13 10.3565 12.8193 11 12.4974" stroke="#04172F" strokeWidth="1.2" strokeLinecap="round" fill="none" />
    <line x1="2.6" y1="6.4" x2="8.4" y2="6.4" stroke="#04172F" strokeWidth="1.2" strokeLinecap="round" />
    <line x1="2.6" y1="9.4" x2="8.4" y2="9.4" stroke="#04172F" strokeWidth="1.2" strokeLinecap="round" />
  </SvgIcon>
);

export const ClockIconDark16 = (props) => (
  <SvgIcon {...props} viewBox="0 0 16 16" style={{ width: 16, height: 16 }}>
    <circle cx="8" cy="8" r="6" stroke="#04172F" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" fill="none" />
    <path d="M12 7.88873C12 5.74104 10.3719 4 8.36364 4C8.24091 4 8.1196 4.0065 8 4.0192V7.88873L10.5455 11C11.4287 10.2905 12 9.16098 12 7.88873Z" fill="#E0E3E7" stroke="#CCD1D7" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M8 4V8L10 10.5" stroke="#04172F" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" />
  </SvgIcon>
);

export const PinIconDark16 = (props) => (
  <SvgIcon {...props} viewBox="0 0 16 16" style={{ width: 16, height: 16 }}>
    <circle cx="8" cy="4" r="2" stroke="#04172F" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" fill="none" />
    <path d="M12.5 11.5C12.5 11.8989 12.1636 12.3947 11.3119 12.8206C10.4902 13.2314 9.31992 13.5 8 13.5C6.68008 13.5 5.50984 13.2314 4.68807 12.8206C3.8364 12.3947 3.5 11.8989 3.5 11.5C3.5 11.1011 3.8364 10.6053 4.68807 10.1794C5.50984 9.76857 6.68008 9.5 8 9.5C9.31992 9.5 10.4902 9.76857 11.3119 10.1794C12.1636 10.6053 12.5 11.1011 12.5 11.5Z" fill="#E0E3E7" stroke="#CCD1D7" />
    <path d="M8 6L8 11" stroke="#04172F" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" />
  </SvgIcon>
);

export const MyShareIllustration56 = (props) => (
  <SvgIcon {...props} viewBox="0 0 56 56" style={{ width: 56, height: 56 }}>
    <circle cx="27" cy="20" r="12" fill="#FFC475" />
    <path d="M8 46C8 39.9249 12.9249 35 19 35H37C43.0751 35 48 39.9249 48 46V48H8V46Z" fill="#FE754B" />
  </SvgIcon>
);

export const MyShareIllustrationGrayScale56 = (props) => (
  <SvgIcon {...props} viewBox="0 0 56 56" style={{ width: 56, height: 56 }}>
    <circle cx="27" cy="20" r="12" fill="#E0E3E7" />
    <path d="M8 46C8 39.9249 12.9249 35 19 35H37C43.0751 35 48 39.9249 48 46V48H8V46Z" fill="#CCD1D7" />
  </SvgIcon>
);

export const SplitIllustration56 = (props) => (
  <SvgIcon {...props} viewBox="0 0 56 56" style={{ width: 56, height: 56 }}>
    <path d="M16 20C16 26.6274 21.3726 32 28 32V8C21.3726 8 16 13.3726 16 20Z" fill="#FFC475" />
    <path d="M40 20C40 26.6274 34.6274 32 28 32V8C34.6274 8 40 13.3726 40 20Z" fill="#04172F" />
    <path d="M40 20C40 26.6274 34.6274 32 28 32V8C34.6274 8 40 13.3726 40 20Z" fill="#FE754B" />
    <path d="M8 46V48H28V35H19C12.9249 35 8 39.9249 8 46Z" fill="#FE754B" />
    <path d="M48 46V48H28V35H37C43.0751 35 48 39.9249 48 46Z" fill="#04172F" />
  </SvgIcon>
);

export const SplitIllustrationGrayScale56 = (props) => (
  <SvgIcon {...props} viewBox="0 0 56 56" style={{ width: 56, height: 56 }}>
    <path d="M16 20C16 26.6274 21.3726 32 28 32V8C21.3726 8 16 13.3726 16 20Z" fill="#E0E3E7" />
    <path d="M40 20C40 26.6274 34.6274 32 28 32V8C34.6274 8 40 13.3726 40 20Z" fill="#04172F" />
    <path d="M40 20C40 26.6274 34.6274 32 28 32V8C34.6274 8 40 13.3726 40 20Z" fill="#CCD1D7" />
    <path d="M8 46V48H28V35H19C12.9249 35 8 39.9249 8 46Z" fill="#CCD1D7" />
    <path d="M48 46V48H28V35H37C43.0751 35 48 39.9249 48 46Z" fill="#919CA9" />
  </SvgIcon>
);

export const PayAllIllustration56 = (props) => (
  <SvgIcon {...props} viewBox="0 0 56 56" style={{ width: 56, height: 56 }}>
    <rect x="24" y="8" width="9" height="24" fill="#04172F" />
    <circle cx="32" cy="20" r="12" fill="#04172F" />
    <circle cx="24" cy="20" r="12" fill="#FFC475" />
    <path d="M8 46C8 39.9249 12.9249 35 19 35H37C43.0751 35 48 39.9249 48 46V48H8V46Z" fill="#FE754B" />
    <path fillRule="evenodd" clipRule="evenodd" d="M40 48H48V46C48 39.9249 43.0751 35 37 35H29C35.0751 35 40 39.9249 40 46V48Z" fill="#04172F" />
  </SvgIcon>
);

export const PayAllIllustrationGrayScale56 = (props) => (
  <SvgIcon {...props} viewBox="0 0 56 56" style={{ width: 56, height: 56 }}>
    <rect x="24" y="8" width="9" height="24" fill="#919CA9" />
    <circle cx="32" cy="20" r="12" fill="#919CA9" />
    <circle cx="24" cy="20" r="12" fill="#E0E3E7" />
    <path d="M8 46C8 39.9249 12.9249 35 19 35H37C43.0751 35 48 39.9249 48 46V48H8V46Z" fill="#CCD1D7" />
    <path fillRule="evenodd" clipRule="evenodd" d="M40 48H48V46C48 39.9249 43.0751 35 37 35H29C35.0751 35 40 39.9249 40 46V48Z" fill="#919CA9" />
  </SvgIcon>
);

export const InAppIllustration56 = (props) => (
  <SvgIcon {...props} viewBox="0 0 56 56" style={{ width: 56, height: 56 }}>
    <rect x="8" y="8" width="22" height="40" rx="2" fill="#4696F7" />
    <path d="M26 8C26 9.65686 24.6568 11 23 11L15 11C13.3431 11 12 9.65685 12 8L26 8Z" fill="#435163" />
    <rect x="18" y="18" width="30" height="20" rx="2" fill="#FFC475" />
    <rect x="18" y="22" width="30" height="4" fill="#435163" />
  </SvgIcon>
);

export const InAppIllustrationGrayScale56 = (props) => (
  <SvgIcon {...props} viewBox="0 0 56 56" style={{ width: 56, height: 56 }}>
    <rect x="8" y="8" width="22" height="40" rx="2" fill="#CCD1D7" />
    <path d="M26 8C26 9.65686 24.6568 11 23 11L15 11C13.3431 11 12 9.65685 12 8L26 8Z" fill="#919CA9" />
    <rect x="18" y="18" width="30" height="20" rx="2" fill="#E0E3E7" />
    <rect x="18" y="22" width="30" height="4" fill="#919CA9" />
  </SvgIcon>
);

export const CashIllustration56 = (props) => (
  <SvgIcon {...props} viewBox="0 0 56 56" style={{ width: 56, height: 56 }}>
    <rect x="8" y="16" width="40" height="22" fill="#1FBB8B" />
    <rect x="43" y="16" width="5" height="22" fill="#209571" />
    <circle cx="28" cy="27" r="7" fill="#209571" />
    <circle cx="18" cy="38" r="10" fill="#FFC475" />
    <circle cx="18" cy="38" r="7" stroke="#FE754B" strokeWidth="2" />
  </SvgIcon>
);

export const CashIllustrationGrayScale56 = (props) => (
  <SvgIcon {...props} viewBox="0 0 56 56" style={{ width: 56, height: 56 }}>
    <rect x="8" y="16" width="40" height="22" fill="#E0E3E7" />
    <path d="M43 16H48V38H43V16Z" fill="#CCD1D7" />
    <circle cx="28" cy="27" r="7" fill="#919CA9" />
    <circle cx="18" cy="38" r="10" fill="#F0F1F3" />
    <circle cx="18" cy="38" r="7" stroke="#CCD1D7" strokeWidth="2" />
  </SvgIcon>
);

export const CardIllustration56 = (props) => (
  <SvgIcon {...props} viewBox="0 0 56 56" style={{ width: 56, height: 56 }}>
    <rect x="8" y="16" width="40" height="24" rx="2" fill="#FFC475" />
    <rect x="8" y="21" width="40" height="5" fill="#B10744" />
    <rect x="35" y="16" width="9" height="24" fill="#FE754B" />
    <rect x="35" y="21" width="9" height="5" fill="#FFC475" />
  </SvgIcon>
);

export const CardIllustrationGrayScale56 = (props) => (
  <SvgIcon {...props} viewBox="0 0 56 56" style={{ width: 56, height: 56 }}>
    <rect x="8" y="16" width="40" height="24" rx="2" fill="#E0E3E7" />
    <rect x="8" y="21" width="40" height="5" fill="#919CA9" />
    <rect x="35" y="16" width="9" height="24" fill="#CCD1D7" />
    <rect x="35" y="21" width="9" height="5" fill="#919CA9" />
  </SvgIcon>
);

export const CloseIcon24 = () => (
  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd" d="M19.8002 12.0002C19.8002 16.308 16.308 19.8002 12.0002 19.8002C7.69237 19.8002 4.2002 16.308 4.2002 12.0002C4.2002 7.69237 7.69237 4.2002 12.0002 4.2002C16.308 4.2002 19.8002 7.69237 19.8002 12.0002ZM14.7532 9.24654C14.9484 9.44181 14.9484 9.75839 14.7532 9.95365L12.7067 12.0001L14.7532 14.0465C14.9484 14.2418 14.9484 14.5584 14.7532 14.7537C14.5579 14.9489 14.2413 14.9489 14.0461 14.7537L11.9996 12.7072L9.95316 14.7537C9.7579 14.9489 9.44132 14.9489 9.24606 14.7537C9.05079 14.5584 9.05079 14.2418 9.24606 14.0465L11.2925 12.0001L9.24606 9.95365C9.05079 9.75839 9.05079 9.44181 9.24606 9.24654C9.44132 9.05128 9.7579 9.05128 9.95316 9.24654L11.9996 11.293L14.0461 9.24654C14.2413 9.05128 14.5579 9.05128 14.7532 9.24654Z" fill="#BAB9B8"/>
  </svg>

)
