/* eslint-disable no-nested-ternary */
import React, { useState } from 'react';
import Container from '@material-ui/core/Container';
import Drawer from 'react-drag-drawer';
import { useDispatch, useSelector } from 'react-redux';
import initials from 'initials';
import Badge from '@material-ui/core/Badge';
import isEmpty from '../../utilities/isEmpty';
import ActivityBar from '../ActivityBar';
import { Button, IconButton } from '../Buttons';
import { CheckIconRound20, CloseIcon, CloseIconDark16, LeaveIcon, MinusIconDark16, PendingIconRound20 } from '../Icons';
import { TitleContainer } from '../Containers';
import { Paragraph } from '../Text';
import {orderSelectors, tableSelectors} from '../../../redux/selectors';
// eslint-disable-next-line import/named
import { withTranslation } from '../../../i18n';
import Avatar from '../Avatar';
import OutlinedIcon from '../Buttons/OutlinedIconButton';
import { tableActions } from '../../../redux/actions';
import useStyles from './styles';

const TableManagementDrawer = ({ t, open, onClose }) => {
  const classes = useStyles();
  const dispatch = useDispatch();

  const { order = {} } = useSelector(orderSelectors.getOrder);
  const { table = {} } = useSelector(tableSelectors.getTable);
  const { tableCode, tableLabel } = order;
  const { label } = table;
  const { participants } = useSelector(orderSelectors.getParticipants);
  const { participant: currentParticipant = {} } = useSelector(orderSelectors.getParticipant);
  const { id: currentParticipantId, canLeave } = currentParticipant;
  const [leaveConfirmation, setLeaveConfirmation] = useState(false);
  const [kickOutConfirmationParticipant, setKickOutConfirmationParticipant] = useState(null);

  const close = () => {
    setLeaveConfirmation(false);
    setKickOutConfirmationParticipant(null);
    onClose();
  };

  const approve = (participantId) => {
    dispatch(tableActions.approveParticipant(order.id, participantId));
  };

  const remove = (participantId) => {
    dispatch(tableActions.deleteParticipant(order.id, participantId));
    setKickOutConfirmationParticipant(null);
  };

  return (
    <Drawer
      open={open}
      onRequestClose={close}
      modalElementClass={classes.drawerModal}
      containerElementClass={classes.drawerContainer}
      dontApplyListeners
      direction="top"
    >
      <div className={classes.modalWrapper}>
        <ActivityBar
          title={`${t('activity-title-table')} ${label ?? tableCode}`}
          back={(
            <IconButton component="a" edge="start" color="inherit" aria-label="menu" onClick={close}>
              <CloseIcon />
            </IconButton>
          )}
        />
        <div className={classes.content}>
          <Container>
            <div className={classes.users}>
              <div>
                <div className={classes.avatarGroup}>
                  {(participants ?? []).map(({ id, customer = {}, approvedByCustomer, canApprove, canReject, canKickout, approvalId }) => (
                    <div className={classes.userContainer} key={id}>
                      <div className={classes.user} key={id}>
                        <Badge
                          overlap="circle"
                          anchorOrigin={{
                            vertical: 'top',
                            horizontal: 'right',
                          }}
                          badgeContent={approvalId ? <CheckIconRound20 /> : <PendingIconRound20 />}
                        >
                          <Avatar alt={`${customer.firstName ?? ''} ${customer.lastName ?? ''}`} className={classes.avatar}>
                            {initials(`${customer.firstName ?? ''} ${customer.lastName ?? ''}`)}
                          </Avatar>
                        </Badge>
                        <div className={classes.label}>
                          <Paragraph>
                            {`${customer.firstName ?? ''} ${customer.lastName ?? ''}`}
                          </Paragraph>
                          <div className={classes.secondary}>
                            {(kickOutConfirmationParticipant === id) && (
                              <Paragraph>
                                {t('table-management-kick-out-table-confirmation-label', { name: customer.firstName })}
                              </Paragraph>
                            )}
                            {!(kickOutConfirmationParticipant === id) && (
                              <>
                                {approvalId && (
                                  <Paragraph>
                                    {!isEmpty(approvedByCustomer) ? t('common-approved-by', { name: approvedByCustomer.firstName }) : t('common-approved')}
                                  </Paragraph>
                                )}
                                {!approvalId && (
                                  <Paragraph>
                                    {t('common-pending')}
                                  </Paragraph>
                                )}
                              </>
                            )}
                          </div>
                        </div>
                        {!(kickOutConfirmationParticipant === id) && (
                          <div className={classes.actions}>
                            {canApprove && (
                              <div className={classes.action}>
                                <Button color="primary" onClick={() => approve(id)}>{t('common-approve')}</Button>
                              </div>
                            )}
                            {canReject && (
                              <div className={classes.action}>
                                <OutlinedIcon icon={<CloseIconDark16 />} onClick={() => remove(id)} />
                              </div>
                            )}
                            {canKickout && (
                              <div className={classes.action}>
                                <OutlinedIcon icon={<MinusIconDark16 />} onClick={() => setKickOutConfirmationParticipant(id)} />
                              </div>
                            )}
                          </div>
                        )}
                      </div>
                      {(kickOutConfirmationParticipant === id) && (
                        <div className={classes.deepShiftedContent}>
                          <div className={classes.actions}>
                            <div className={classes.action}>
                              <Button color="secondary" onClick={() => remove(id)}>{t('table-management-kick-out-btn-label')}</Button>
                            </div>
                            <div className={classes.action}>
                              <Button variant="text" onClick={() => setKickOutConfirmationParticipant(null)}>{t('common-cancel')}</Button>
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </Container>
          <TitleContainer divider>
            <Container>
              <div className={classes.iconBtn}>
                <IconButton
                  edge="start"
                  disabled={leaveConfirmation}
                  onClick={() => setLeaveConfirmation(true)}
                  label={t(leaveConfirmation ? (canLeave ? 'table-management-leave-table-confirmation-label' : 'table-management-leave-table-non-confirmation-label') : 'table-management-leave-table-btn-label')}
                >
                  <LeaveIcon />
                </IconButton>
                <div className={classes.shiftedContent}>
                  {leaveConfirmation && !canLeave && (
                    <div className={classes.light}>
                      <Paragraph>
                        {t('table-management-leave-table-non-confirmation-description')}
                      </Paragraph>
                    </div>
                  )}
                  {leaveConfirmation && (
                    <div className={classes.leaveConfirmation}>
                      <div className={classes.actions}>
                        {canLeave && (
                          <>
                            <div className={classes.action}>
                              <Button color="secondary" onClick={() => remove(currentParticipantId)}>{t('table-management-leave-btn-label')}</Button>
                            </div>
                            <div className={classes.action}>
                              <Button variant="text" onClick={() => setLeaveConfirmation(false)}>{t('common-cancel')}</Button>
                            </div>
                          </>
                        )}
                        {!canLeave && (
                          <div className={classes.action}>
                            <Button variant="text" onClick={() => setLeaveConfirmation(false)}>{t('common-okay')}</Button>
                          </div>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </Container>
          </TitleContainer>
        </div>
      </div>
    </Drawer>
  );
};

export default withTranslation('common')(TableManagementDrawer);
