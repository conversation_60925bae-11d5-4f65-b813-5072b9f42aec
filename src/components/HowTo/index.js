import React from 'react';
import Container from '@material-ui/core/Container';
import Typography from '@material-ui/core/Typography';
import Grid from '@material-ui/core/Grid';
import Box from '@material-ui/core/Box';
import Card from '@material-ui/core/Card';
import CardMedia from '@material-ui/core/CardMedia';
import CardContent from '@material-ui/core/CardContent';
import { Breakpoint } from 'react-socks';
import Mobile from './mobile';
import useStyles from './styles';

const HowTo = () => {
  const classes = useStyles();

  return (
    <>
      <Breakpoint customQuery="(max-width: 900px)">
        <Mobile />
      </Breakpoint>
      <Breakpoint customQuery="(min-width: 901px)">
        <Container maxWidth="lg">
          <Box pt={8} pb={8}>
            <Typography variant="h2" gutterBottom className={classes.title}>
              How It Works
            </Typography>
            <Grid container spacing={4}>
              <Grid item xs={12} sm={4} md={4}>
                <Card elevation={0} classes={{ root: classes.cardRoot }}>
                  <CardMedia
                    component="img"
                    alt="Contemplative Reptile"
                    height="180"
                    image="/assets/how-to/scan.png"
                    title="Contemplative Reptile"
                    classes={{
                      root: classes.cardMediaRoot
                    }}
                  />
                  <CardContent classes={{
                    root: classes.cardContentRoot
                  }}
                  >
                    <Typography gutterBottom>Scan to order</Typography>
                    <Typography color="textSecondary">
                      In every Leviee restaurant, you will find our QR code.
                      You can scan to start ordering directly to the kitchen.
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
              <Grid item xs={12} sm={4} md={4}>
                <Card elevation={0} classes={{ root: classes.cardRoot }}>
                  <CardMedia
                    component="img"
                    alt="Contemplative Reptile"
                    height="180"
                    image="/assets/how-to/friends.png"
                    title="Contemplative Reptile"
                    classes={{
                      root: classes.cardMediaRoot
                    }}
                  />
                  <CardContent classes={{
                    root: classes.cardContentRoot
                  }}
                  >
                    <Typography gutterBottom>Invite friends</Typography>
                    <Typography color="textSecondary">
                      Join the table with friends, and see what others are ordering.
                      No need to pay before eating, keep the tap open and reorder at any time
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
              <Grid item xs={12} sm={4} md={4}>
                <Card elevation={0} classes={{ root: classes.cardRoot }}>
                  <CardMedia
                    component="img"
                    alt="Contemplative Reptile"
                    height="180"
                    image="/assets/how-to/split.png"
                    title="Contemplative Reptile"
                    classes={{
                      root: classes.cardMediaRoot
                    }}
                  />
                  <CardContent classes={{
                    root: classes.cardContentRoot
                  }}
                  >
                    <Typography gutterBottom>Split the bill</Typography>
                    <Typography color="textSecondary">
                      Ready to go? Pay through Leviee or ping the waiter through the app.
                      You can even split the bill with your friends.
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          </Box>
        </Container>
      </Breakpoint>
    </>
  );
};

export default HowTo;
