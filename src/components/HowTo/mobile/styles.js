import { makeStyles } from '@material-ui/core/styles';
import { fontStyles } from '../../../../styles/theme';

const useStyles = makeStyles((theme) => ({
  wrapper: {
    background: theme.palette.primary.main,
    paddingTop: theme.spacing(1) + 4,
    paddingBottom: theme.spacing(2),
  },
  img: {
    background: 'rgba(255, 255, 255, 0.64)',
    borderRadius: 12,
    width: '100%'
  },
  imgSlide: {
    width: '90%'
  },
  slide: {
    height: 61
  },
  pagination: {
    textAlign: 'left'
  },
  line: {
    border: '1.5px solid rgba(255, 255, 255, 0.64)'
  },
  activeLine: {
    width: theme.spacing(2),
    border: '1.5px solid #FFFFFF'
  },
  slideText: {
    marginTop: theme.spacing(2) + 4,
    color: theme.palette.common.white,
    ...fontStyles.secondaryTitleRegular
  }
}));

export default useStyles;
