// Import Swiper styles
import 'swiper/swiper-bundle.min.css';

import React, { useState } from 'react';
import Container from '@material-ui/core/Container';
import Typography from '@material-ui/core/Typography';
import SwiperCore, { Mousewheel, Controller, Pagination, A11y } from 'swiper';
import { Swiper, SwiperSlide } from 'swiper/react';
import Img from '../../Img';
import { withTranslation } from '../../../../i18n';
import useStyles from './styles';

SwiperCore.use([Mousewheel, Controller, Pagination, A11y]);

const Mobile = ({ t }) => {
  const classes = useStyles();
  const [controlledSwiper, setControlledSwiper] = useState(null);

  return (
    <div className={classes.wrapper}>
      <Container maxWidth={false}>
        <Swiper
          controller={{ control: controlledSwiper }}
          spaceBetween={12}
          slidesPerView="auto"
          centeredSlides
          centeredSlidesBounds
          mousewheel={{
            enabled: true,
            forceToAxis: true
          }}
        >
          <SwiperSlide className={classes.imgSlide}>
            <div className={classes.sliderWrapper}>
              <Img className={classes.img} src="/assets/how-to/sit.svg" alt="" />
            </div>
          </SwiperSlide>
          <SwiperSlide className={classes.imgSlide}>
            <div className={classes.sliderWrapper}>
              <Img className={classes.img} src="/assets/how-to/scan.svg" alt="" />
            </div>
          </SwiperSlide>
          <SwiperSlide className={classes.imgSlide}>
            <div className={classes.sliderWrapper}>
              <Img className={classes.img} src="/assets/how-to/friends.svg" alt="" />
            </div>
          </SwiperSlide>
          <SwiperSlide className={classes.imgSlide}>
            <div className={classes.sliderWrapper}>
              <Img className={classes.img} src="/assets/how-to/split.svg" alt="" />
            </div>
          </SwiperSlide>
        </Swiper>
        <Swiper
          onSwiper={setControlledSwiper}
          spaceBetween={12}
          slidesPerView={1}
          allowSlidePrev={false}
          allowSlideNext={false}
          pagination
        >
          <SwiperSlide className={classes.slide}>
            <Typography className={classes.slideText}>
              {t('landing-instruction-sit-at-a-leviee-restaurant-label')}
            </Typography>
          </SwiperSlide>
          <SwiperSlide className={classes.slide}>
            <Typography className={classes.slideText}>
              {t('landing-instruction-scan-the-qr-code-label')}
            </Typography>
          </SwiperSlide>
          <SwiperSlide className={classes.slide}>
            <Typography className={classes.slideText}>
              {t('landing-instruction-no-payment-needed-label')}
            </Typography>
          </SwiperSlide>
          <SwiperSlide className={classes.slide}>
            <Typography className={classes.slideText}>
              {t('landing-instruction-split-the-bill-label')}
            </Typography>
          </SwiperSlide>
        </Swiper>
      </Container>
    </div>
  );
};

export default withTranslation('common')(Mobile);
