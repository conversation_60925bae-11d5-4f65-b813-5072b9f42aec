import { makeStyles } from '@material-ui/core/styles';

const useStyles = makeStyles((theme) => ({
  title: {
    margin: theme.spacing(4, 0, 8, 0)
  },
  cardRoot: {
    // boxShadow: '3px 3px 6px 0 rgba(0, 0, 0, 0.05)',
    // border: '1px solid #ecebea',
    // padding: theme.spacing(2)
  },
  cardContentRoot: {
    paddingLeft: 0,
    paddingRight: 0,
  },
  cardMediaRoot: {
    borderRadius: 4,
    background: theme.palette.primary.main
  }
}));

export default useStyles;
