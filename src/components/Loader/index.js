import React from 'react';
import { Backdrop, fade, withStyles } from '@material-ui/core';
import CircularProgress from '@material-ui/core/CircularProgress';
import { makeStyles } from '@material-ui/core/styles';
import { colors } from '../../../styles/theme';
import { SecondaryTitleContainer } from '../Containers';
import { SecondaryTitle } from '../Text';
import palette from "../../../styles/palette";

const StyledCircularProgress = withStyles({
  root: {
    color: fade(colors.leviee.main.white, 0.72)
  },
})(CircularProgress);

const StyledBackdrop = withStyles({
  root: {
    background: palette.primary["500"],
    display: 'flex',
    flexDirection: 'column',
    zIndex: 9999
  },
})(Backdrop);

const useStyles = makeStyles(() => ({
  message: {
    color: fade(colors.leviee.main.white, 0.72),
    maxWidth: 300,
    margin: '0 auto',
    textAlign: 'center',
    display: 'block'
  }
}));

const Loader = ({ message }) => {
  const classes = useStyles();
  return (
    <StyledBackdrop open>
      <StyledCircularProgress />
      <SecondaryTitleContainer>
        <SecondaryTitle>
          <span className={classes.message}>{message}</span>
        </SecondaryTitle>
      </SecondaryTitleContainer>
    </StyledBackdrop>
  );
};

export default Loader;
