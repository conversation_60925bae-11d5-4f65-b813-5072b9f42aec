import React from 'react';
import {useDispatch, useSelector} from "react-redux";
import {orderSelectors} from "../../../redux/selectors";
import isEmpty from "../../utilities/isEmpty";
import PromotedOrderItem from "../PromotedOrderItem";
import {googleTags} from "../../../gtm";
import useStyles from "./styles";
import {orderActions} from "../../../redux/actions";
import {withTranslation} from "../../../i18n";
import ButtonTimer from "../ButtonTimer";
import {ButtonBase, Typography, useMediaQuery} from "@material-ui/core";
import palette from "../../../styles/palette";
import typography from "../../../styles/typography";
import Badge from "../_tags/Badge";
import DurationBadgeActive from "../_tags/DurationBadgeActive";

const OngoingOrderingAction = ({ t }) => {
	const classes = useStyles();
	const dispatch = useDispatch();
	
	const isMobile = useMediaQuery('(max-width:600px)');
	
	const { order = {} } = useSelector(orderSelectors.getOrder);
	const {items: ongoingItems = []} = useSelector(orderSelectors.getOngoing);
	
	const { participant = {} } = useSelector(orderSelectors.getParticipant);
	const { customerId: currentCustomerId } = (participant ?? {});
	
	const canViewMenuItem = (item) => {
		if (order.type === 'DINE_IN' && !isEmpty(item) && item.ongoing) {
			return () => dispatch(orderActions.setItem(item));
		}
		return null;
	};
	
	const canOrder = (item) => {
		if (order.type === 'DINE_IN' && !isEmpty(item) && item.ongoing) {
			return () => dispatch(orderActions.setItem(item));
		}
		return null;
	};
	
	const getAction = (item, nestedModificationTime) => (currentCustomerId !== order.customerId)
		? (
				<ButtonBase style={{
					paddingTop: 7,
					paddingBottom: 7,
					paddingLeft: 16,
					paddingRight: 16,
					background: palette.primary["500"],
					borderRadius: 12
				}} onClick={canViewMenuItem(item)}>
					<Typography style={{ ...typography.body.medium, color: palette.grayscale["100"] }}>
						{t('order-current-view-ongoing-menu-item-btn-label')}
					</Typography>
				</ButtonBase>
		)
		: (
				<ButtonTimer
					starting={nestedModificationTime}
					limit={15}
				>
					{(label, disabled) => (
						<ButtonBase style={{
							paddingTop: 7,
							paddingBottom: 7,
							paddingLeft: 16,
							paddingRight: 16,
							background: disabled ? palette.grayscale["400"] : palette.primary["500"],
							borderRadius: 12
						}}
							onClick={canOrder(item)} id={googleTags.menu.orderOngoingItemBtn.id} disabled={disabled}>
							<Typography style={{ ...typography.body.medium, color: palette.grayscale["100"] }}>
								{disabled ? label : t('order-current-order-ongoing-order-item-btn-label')}
							</Typography>
						</ButtonBase>
					)}
				</ButtonTimer>
	 )
	
	if (isEmpty(ongoingItems)) {
		return null;
	}
	
	return (
		<div className={classes.ongoingItemsWrapper}>
			<div style={{ marginBottom: 12, paddingBottom: 12, borderBottom: `1px dashed ${palette.grayscale["400"]}` }}>
				<div style={{ display: "flex", width: "100%", justifyContent: "space-between", alignItems: "center" }}>
					<Typography style={{ ...typography.body.medium }}>
						{t('your-buffet')}
					</Typography>
					<Badge label={t('in-progress')} color={"ACTION"}/>
				</div>
			</div>
			<div style={isMobile
				? { display: "grid", gridTemplateColumns: "repeat(1,minmax(160px, 1fr))", gap: 12 }
				: { display: "grid", gridTemplateColumns: "repeat(2,minmax(160px, 1fr))", gap: 12 }}>
				{ongoingItems.map(item => {
					const confirmedNestedItemsByUser = (item.nestedOrderItems ?? []).filter(it => it.status === 'CONFIRMED' && !it.offline);
					const nestedModificationTime = isEmpty(confirmedNestedItemsByUser) ? null : confirmedNestedItemsByUser[0].modificationTime
					
					return (
						<PromotedOrderItem
							key={item.id}
							{...item}
							disabled={item.disabled}
							price={item.unitPrice}
							action={getAction(item, nestedModificationTime)}
							onClick={() => {}}
						/>
					)
				})}
			</div>
		</div>
	)
};

export default withTranslation('common')(OngoingOrderingAction);
