import React from 'react';
import clsx from 'clsx';
import moment from 'moment';
import { Paragraph } from '../Text';
import { Button } from '../Buttons';
// eslint-disable-next-line import/named
import { withTranslation } from '../../../i18n';
import useStyles from './styles';
import isEmpty from '../../utilities/isEmpty';
import formatNumber from '../../utilities/formatNumber';
import { googleTags } from '../../../gtm';
import ButtonTimer from '../ButtonTimer';
import ButtonBase from "@material-ui/core/ButtonBase";
import palette from "../../../styles/palette";
import typography from "../../../styles/typography";
import Typography from "@material-ui/core/Typography";

const OrderItem = ({
  t, index = 0, qtd = 1, modificationTime, nestedModificationTime, name, notes,
  options = [], extras = [], unitPrice, total, onRemove, status, nested, onOrderOngoing, onViewMenuItem
}) => {
  const classes = useStyles();
  const cancelled = status === 'CANCELLED';
  const unconfirmed = status === 'UNCONFIRMED';
  const paid = status === 'PAID';
  unitPrice = ((unitPrice) || 0).toFixed(Number.isInteger(total) ? 0 : 2);
  total = ((total) || 0).toFixed(Number.isInteger(total) ? 0 : 2);

  const optionItems = isEmpty(options) ? [] : options.reduce((acc, next) => {
    const { items } = next;
    if (next.qtd < 2) {
      items.map((i) => {
        delete i.qtd;
        return i;
      });
    }
    acc = acc.concat(items);
    return acc;
  }, []);

  const extraItems = isEmpty(extras) ? [] : extras.reduce((acc, next) => {
    const { items = [] } = next;

    items.forEach((i) => {
      if (i.max < 2) {
        delete i.qtd;
      }
      return i;
    });
    acc = acc.concat(items);
    return acc;
  }, []);

  return (
    <div className={classes.container}>
      <div>
        <div className={classes.layout}>
          <div className={classes.text}>
            <div className={classes.count}>
              {!nested && <Paragraph>{qtd}</Paragraph>}
            </div>
            <div className={classes.body}>
              <div className={classes.bodyGroup}>
                <div className={classes.itemGroup}>
                  <div className={classes.itemTextGroup}>
                    <div className={clsx(classes.name, { [classes.strike]: !!cancelled, [classes.fade]: !!paid })}>
                      <Paragraph>
                        {nested
                          ? `${t('order-current-order-ongoing-order-item-round-label', {
                            round: unconfirmed ? null : index
                          })} (${moment(modificationTime).format('HH:mm')})`
                          : name}
                      </Paragraph>
                    </div>
                    {notes && (
                      <div className={clsx(classes.notes, { [classes.strike]: !!cancelled, [classes.fade]: !!paid })}>
                        <Paragraph>{notes}</Paragraph>
                      </div>
                    )}
                  </div>
                  {!nested && (
                    <div className={clsx(classes.price, { [classes.strike]: !!cancelled, [classes.fade]: !!paid })}>
                      <Paragraph>{`${total}€`}</Paragraph>
                    </div>
                  )}
                </div>
              </div>
              {(!isEmpty(optionItems) || !isEmpty(extraItems)) && !nested && (
                <div key="base" className={clsx(classes.optionItem, { [classes.strike]: !!cancelled, [classes.fade]: !!paid })}>
                  <div className={classes.layout}>
                    <div className={classes.text}>
                      <div className={classes.additionCount}>
                        <Paragraph />
                      </div>
                      <div className={classes.additionalGroup}>
                        <div className={clsx(classes.name, { [classes.strike]: !!cancelled, [classes.fade]: !!paid })}>
                          <Paragraph>{t('order-item-base-price-label')}</Paragraph>
                        </div>
                        <div className={clsx(classes.additionPrice, { [classes.strike]: !!cancelled, [classes.fade]: !!paid })}>
                          <Paragraph>{`${unitPrice}€`}</Paragraph>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}
              {!isEmpty(optionItems) && optionItems.map(({ id, qtd, unitPrice, name }) => (
                <div key={id} className={clsx(classes.optionItem, { [classes.strike]: !!cancelled, [classes.fade]: !!paid })}>
                  <div className={classes.layout}>
                    <div className={classes.text}>
                      <div className={classes.additionCount}>
                        <Paragraph>{qtd}</Paragraph>
                      </div>
                      <div className={classes.additionalGroup}>
                        <div className={clsx(classes.name, { [classes.strike]: !!cancelled, [classes.fade]: !!paid })}>
                          <Paragraph>{name}</Paragraph>
                        </div>
                        <div className={clsx(classes.additionPrice, { [classes.strike]: !!cancelled, [classes.fade]: !!paid })}>
                          {!nested && <Paragraph>{`+ ${formatNumber(unitPrice)}€`}</Paragraph>}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
              {!isEmpty(extraItems) && extraItems.map(({ id, qtd, unitPrice, name }) => (
                <div key={id} className={clsx(classes.extraItem, { [classes.strike]: !!cancelled, [classes.fade]: !!paid })}>
                  <div className={classes.layout}>
                    <div className={classes.text}>
                      <div className={classes.additionCount}>
                        <Paragraph>{qtd}</Paragraph>
                      </div>
                      <div className={classes.additionalGroup}>
                        <div className={clsx(classes.name, { [classes.strike]: !!cancelled, [classes.fade]: !!paid })}>
                          <Paragraph>{name}</Paragraph>
                        </div>
                        <div className={clsx(classes.additionPrice, { [classes.strike]: !!cancelled, [classes.fade]: !!paid })}>
                          {!nested && <Paragraph>{`+ ${formatNumber(unitPrice)}€`}</Paragraph>}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
              {onRemove && (
                <div className={classes.actions}>
                  <ButtonBase
                    onClick={onRemove}
                    id={googleTags.menu.removeItemBtn.id}
                    style={{
                      paddingTop: 6,
                      paddingBottom: 6,
                      paddingLeft: 16,
                      paddingRight: 16,
                      background: palette.negative["500"],
                      borderRadius: 10
                    }}
                  >
                    <Typography style={{ ...typography.body.medium, color: palette.grayscale.white }}>
                      {t('order-current-order-remove-item-btn-label')}
                    </Typography>
                  </ButtonBase>
                </div>
              )}
              {onOrderOngoing && !paid && (
                <div className={classes.actions}>
                  <ButtonTimer
                    starting={nestedModificationTime}
                    limit={15}
                  >
                    {(label, disabled) => (
                      <ButtonBase
                        onClick={onOrderOngoing} id={googleTags.menu.orderOngoingItemBtn.id} disabled={disabled}
                        style={{
                          paddingTop: 6,
                          paddingBottom: 6,
                          paddingLeft: 16,
                          paddingRight: 16,
                          background: palette.primary["500"],
                          borderRadius: 10
                        }}
                      >
                        <Typography style={{ ...typography.body.medium, color: palette.grayscale.white }}>
                          {disabled ? label : t('buffet-ordering')}
                        </Typography>
                      </ButtonBase>
                    )}
                  </ButtonTimer>
                </div>
              )}
              {!onOrderOngoing && onViewMenuItem && (
                <Button variant="outlined" onClick={onViewMenuItem}>
                  {t('order-current-view-ongoing-menu-item-btn-label')}
                </Button>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default withTranslation('common')(OrderItem);
