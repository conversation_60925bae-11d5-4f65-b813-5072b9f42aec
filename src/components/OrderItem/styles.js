import { makeStyles } from '@material-ui/core/styles';
import { colors } from '../../../styles/theme';

const useStyles = makeStyles((theme) => ({
  container: {
    padding: theme.spacing(1, 0, 1, 0)
  },
  layout: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'top',
    justifyContent: 'space-between',
    verticalAlign: 'middle',
    paddingTop: theme.spacing(1) - 4,
    paddingBottom: theme.spacing(1) - 4
  },
  text: {
    display: 'flex',
    flex: 1
  },
  body: {
    flex: 1
  },
  count: {
    minWidth: 20,
    marginRight: 8,
    paddingTop: theme.spacing(1),
  },
  additionCount: {
    minWidth: 20,
    marginRight: 8
  },
  bodyGroup: {
    paddingTop: theme.spacing(1),
    paddingBottom: theme.spacing(1)
  },
  itemGroup: {
    display: 'flex',
    alignItems: 'baseline'
  },
  itemTextGroup: {
    flex: 1
  },
  name: {
    flex: 1
  },
  notes: {
    color: colors.leviee.greyscale.midGray
  },
  actions: {
    marginTop: 8
  },
  price: {
    color: colors.leviee.greyscale.darkGray
  },
  additionPrice: {
    color: colors.leviee.greyscale.midGray
  },
  strike: {
    textDecoration: 'line-through'
  },
  fade: {
    opacity: '.6'
  },
  additionalGroup: {
    display: 'flex',
    flex: 1
  }
}));

export default useStyles;
