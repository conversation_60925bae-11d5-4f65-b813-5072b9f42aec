import React from 'react';
import { ButtonContainer } from '../Containers';
import { MainButton } from '../Buttons';
// eslint-disable-next-line import/named
import { withTranslation } from '../../../i18n';
import useStyles from './styles';
import { googleTags } from '../../../gtm';

const MenuItemDetailsAction = ({ t, amount = 0, ...otherProps }) => {
  const classes = useStyles();
  delete otherProps.tReady;

  return (
    <ButtonContainer>
      <MainButton className={classes.container} {...otherProps} id={googleTags.menu.addItemBtn.id}>
        <div className={classes.layout}>
          <div>
            {t('order-item-details-add-to-order-btn-label')}
          </div>
          {amount && (
            <div className={classes.sidebar}>
              <div className={classes.divider} />
              <div className={classes.priceLabel}>
                {`${amount.toFixed(2)} €`}
              </div>
            </div>
          )}
        </div>
      </MainButton>
    </ButtonContainer>
  );
};

export default withTranslation('common')(MenuItemDetailsAction);
