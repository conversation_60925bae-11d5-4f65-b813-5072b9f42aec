import { makeStyles } from '@material-ui/core/styles';
import { drawerModalStyle } from '../../../styles/theme';

const useStyles = makeStyles((theme) => ({
  drawerModal: {
    background: theme.palette.common.white,
    position: 'absolute',
    width: '100%',
    maxWidth: '700px',
    borderTopLeftRadius: 8,
    borderTopRightRadius: 8,
    bottom: 0
  },
  drawerContainer: {
    '&&': {
      ...drawerModalStyle,
      zIndex: '1701'
    }
  },
  iconCardContainer: {
    marginTop: 40,
    marginBottom: 16,
  },
  selectionSpacing: {
    paddingTop: theme.spacing(8),
    paddingBottom: theme.spacing(6)
  },
  iconPadding: {
    paddingRight: theme.spacing(2)
  },
  item: {
    display: 'flex',
    minHeight: 48
  },
  expand: {
    width: '100%'
  },
  flexExpand: {
    flex: 1
  },
  flex: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between'
  },
  fade: {
    opacity: '.6'
  }
}));

export default useStyles;
