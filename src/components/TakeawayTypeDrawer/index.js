import Container from '@material-ui/core/Container';
import React from 'react';
import Drawer from 'react-drag-drawer';
import { ButtonBase } from '@material-ui/core';
import clsx from 'clsx';
import { withTranslation } from '../../../i18n';
import ActivityBar from '../ActivityBar';
import { IconButton } from '../Buttons';
import { ArrowForwardIcon, CloseIcon } from '../Icons';
import Img from '../Img';
import { Paragraph } from '../Text';
import useStyles from './styles';

const TakeawayTypeDrawer = ({ t, open, onClose, orderTypes = {}, onOrder }) => {
  const classes = useStyles();

  const { hasPickup } = orderTypes;
  const { hasDelivery } = orderTypes;

  return (
    <Drawer
      open={open}
      onRequestClose={onClose}
      modalElementClass={classes.drawerModal}
      containerElementClass={classes.drawerContainer}
    >
      <ActivityBar
        title={t('takeaway-type-drawer-title')}
        color="transparent"
        close={(
          <IconButton
            component="a"
            edge="end"
            color="inherit"
            aria-label="menu"
            onClick={onClose}
          >
            <CloseIcon />
          </IconButton>
        )}
      />
      <Container>
        <div className={classes.selectionSpacing}>
          <div className={classes.item}>
            <ButtonBase onClick={() => onOrder('pickup')} disabled={!hasPickup} className={clsx(classes.expand, { [classes.fade]: !hasPickup })}>
              <div className={clsx(classes.flex, classes.expand)}>
                <div className={classes.flex}>
                  <Img src="/icons/login.svg" alt="" className={classes.iconPadding} />
                  <Paragraph>{t('pickup-order-type-field-pickup-option-label')}</Paragraph>
                </div>
                <ArrowForwardIcon />
              </div>
            </ButtonBase>
          </div>
          <div className={classes.item}>
            <ButtonBase onClick={() => onOrder('delivery')} disabled={!hasDelivery} className={clsx(classes.expand, { [classes.fade]: !hasDelivery })}>
              <div className={clsx(classes.flex, classes.expand)}>
                <div className={classes.flex}>
                  <Img src="/icons/Leave.svg" alt="" className={classes.iconPadding} />
                  <Paragraph>{t('pickup-order-type-field-delivery-option-label')}</Paragraph>
                </div>
                <ArrowForwardIcon />
              </div>
            </ButtonBase>
          </div>
        </div>
      </Container>
    </Drawer>
  );
};

export default withTranslation('common')(TakeawayTypeDrawer);
