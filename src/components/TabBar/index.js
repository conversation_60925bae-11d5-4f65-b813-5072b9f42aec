import React from 'react';
// import { Swiper, SwiperSlide } from 'swiper/react';
// import IconButton from '@material-ui/core/IconButton';
// import clsx from 'clsx';
import Tabs from '@material-ui/core/Tabs';
import Tab from '@material-ui/core/Tab';
import Container from '@material-ui/core/Container';
import { SearchIcon } from '../Icons';
import useStyles from './styles';
import {useMediaQuery} from "@material-ui/core";

function a11yProps(id) {
  return {
    id: `${id}`,
    'aria-controls': `tab-panel-${id}`,
  };
}

const TabBar = ({ tabs, value, onChange }) => {
  const classes = useStyles();

  // tabs.scrollLeft = tab.offsetLeft

  return (
    <div className={classes.wrapper}>
      {/* <Swiper */}
      {/*  spaceBetween={0} */}
      {/*  slidesPerView="auto" */}
      {/*  className={classes.swiper} */}
      {/*  initialSlide={1} */}
      {/* > */}
      {/*  /!*<SwiperSlide key="search-tab" className={classes.actionTab}>*!/ */}
      {/*  /!*  <IconButton edge="start">*!/ */}
      {/*  /!*    <SearchIcon />*!/ */}
      {/*  /!*  </IconButton>*!/ */}
      {/*  /!*</SwiperSlide>*!/ */}
      {/*  {tabs.map(({ id, label }) => ( */}
      {/*    <SwiperSlide key={id} className={classes.tab}> */}
      {/*      {({ isActive }) => ( */}
      {/*        <span className={clsx({ [classes.activeTab]: isActive })}>{label}</span> */}
      {/*      )} */}
      {/*    </SwiperSlide> */}
      {/*  ))} */}
      {/* </Swiper> */}
      <div className={classes.container}>
        <Tabs value={value} onChange={onChange} aria-label="tabs controller" variant="scrollable" scrollButtons="off" orientation={"horizontal"}>
          {/*<Tab*/}
          {/*  key="search-tab"*/}
          {/*  value="search"*/}
          {/*  icon={<SearchIcon />}*/}
          {/*/>*/}
          {tabs.map(({ id, label, emoji, size, ...otherProps }) => (
            <Tab
              disableRipple
              disableFocusRipple
              disableTouchRipple
              key={id}
              label={(
                <div style={{ display: "flex", alignItems: "center" }}>
                  <span>{label}</span>
                  {!!emoji && <span style={{ marginLeft: 2, marginRight: 2 }}>{emoji}</span>}
                  <span style={{
                    background: otherProps.value === value ? '#929191' : '#E8E7E6',
                    borderRadius: 12,
                    color: otherProps.value === value ? '#F9F9F9' : '#333332',
                    fontStyle: 'normal',
                    fontWeight: '500',
                    fontSize: '11px',
                    lineHeight: '16px',
                    paddingLeft: 5,
                    paddingRight: 5,
                    marginLeft: 6,
                    whiteSpace: "nowrap"
                  }}>{size}</span>
                </div>
              )}
              {...otherProps}
              {...a11yProps(id)}
            />
          ))}
        </Tabs>
      </div>
    </div>
  );
};

export default TabBar;
