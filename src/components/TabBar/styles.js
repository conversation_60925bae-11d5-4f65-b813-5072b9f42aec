import { makeStyles } from '@material-ui/core/styles';
import { colors, containerStyles, fontStyles } from '../../../styles/theme';

const useStyles = makeStyles(() => ({
  swiper: {
    paddingLeft: 12,
    paddingRight: 12,
    '& > div': {
      display: 'flex',
      alignItems: 'center'
    }
  },
  tab: {
    flexShrink: 'unset',
    ...fontStyles.paragraphRegular,
    ...containerStyles.textContainer,
    color: colors.leviee.greyscale.midGray,
    whiteSpace: 'nowrap',
    paddingRight: 12,
    display: 'flex',
    alignItems: 'center',
    minHeight: 28
  },
  activeTab: {
    color: colors.leviee.main.dark,
  },
  actionTab: {
    flexShrink: 'unset',
    display: 'flex'
  },
  container: {
    paddingLeft: 0,
    paddingRight: 0
  }
}));

export default useStyles;
