import { makeStyles } from '@material-ui/core/styles';
import { drawerModalStyle } from '../../../styles/theme';

const useStyles = makeStyles((theme) => ({
  drawerModal: {
    background: theme.palette.common.white,
    position: 'absolute',
    top: 300,
    width: '100%',
    maxWidth: '700px',
    borderTopLeftRadius: 8,
    borderTopRightRadius: 8,
    // minHeight: '100%',
    bottom: 0
  },
  drawerContainer: {
    '&&': {
      ...drawerModalStyle,
      zIndex: ({ zIndex }) => zIndex || drawerModalStyle.zIndex
    }
  },
  modalWrapper: {
    marginTop: theme.spacing(1),
    background: theme.palette.common.white
  }
}));

export default useStyles;
