import React from 'react';
import Container from '@material-ui/core/Container';
import Drawer from 'react-drag-drawer';
import ActivityBar from '../ActivityBar';
import { IconButton } from '../Buttons';
import { CloseIcon } from '../Icons';
// eslint-disable-next-line import/named
import { withTranslation } from '../../../i18n';
import useStyles from './styles';

const MessageDrawer = ({ open, onClose, title, children, zIndex }) => {
  const classes = useStyles({ zIndex });

  return (
    <Drawer
      open={open}
      onRequestClose={onClose}
      modalElementClass={classes.drawerModal}
      containerElementClass={classes.drawerContainer}
    >
      <ActivityBar
        title={title}
        color="transparent"
        close={(
          <IconButton component="a" edge="start" color="inherit" aria-label="menu" onClick={onClose}>
            <CloseIcon />
          </IconButton>
        )}
      />
      <div className={classes.modalWrapper}>
        <Container>
          {children}
        </Container>
      </div>
    </Drawer>
  );
};

export default withTranslation('common')(MessageDrawer);
