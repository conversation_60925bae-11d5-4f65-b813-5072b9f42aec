export const mobileOS = {
  WINDOWS: 'windows',
  ANDROID: 'android',
  IOS: 'ios'
};

export function getMobileOS() {
  if (typeof window === 'undefined' || typeof navigator === 'undefined') {
    return false;
  }

  // eslint-disable-next-line no-undef
  const userAgent = navigator.userAgent || navigator.vendor || window.opera;

  // Windows Phone must come first because its UA also contains "Android"
  if (/windows phone/i.test(userAgent)) {
    return mobileOS.WINDOWS;
  }

  if (/android/i.test(userAgent)) {
    return mobileOS.ANDROID;
  }

  // iOS detection from: http://stackoverflow.com/a/9039885/177710
  // eslint-disable-next-line no-undef
  if (/iPad|iPhone|iPod/.test(userAgent) && !window.MSStream) {
    return mobileOS.IOS;
  }

  return false;
}
