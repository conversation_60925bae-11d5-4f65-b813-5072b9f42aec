import React from 'react';
import SvgIcon from '@material-ui/core/SvgIcon';

export const ScanSvg = (
  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
       stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"
       className="feather feather-maximize">
    <path d="M8 3H5a2 2 0 0 0-2 2v3m18 0V5a2 2 0 0 0-2-2h-3m0 18h3a2 2 0 0 0 2-2v-3M3 16v3a2 2 0 0 0 2 2h3" />
    <path d="M3 12H21" />
  </svg>
);

export const UserSvg = (
  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
       stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"
       className="feather feather-user">
    <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2" />
    <circle cx="12" cy="7" r="4" />
  </svg>
);

export const LanguageSvg = (
  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor"
       strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="feather feather-globe">
    <circle cx="12" cy="12" r="10" />
    <line x1="2" y1="12" x2="22" y2="12" />
    <path d="M12 2a15.3 15.3 0 0 1 4 10 15.3 15.3 0 0 1-4 10 15.3 15.3 0 0 1-4-10 15.3 15.3 0 0 1 4-10z" />
  </svg>
);

export const MenuSvg = (
  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
       stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"
       className="feather feather-menu">
    <line x1="3" y1="12" x2="21" y2="12" />
    <line x1="3" y1="6" x2="21" y2="6" />
    <line x1="3" y1="18" x2="21" y2="18" />
  </svg>
);

export const ClipboardSvg = (
  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor"
       strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="feather feather-clipboard">
    <path d="M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2" />
    <rect x="8" y="2" width="8" height="4" rx="1" ry="1" />
  </svg>
);

export const HelpSvg = (
  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor"
       strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="feather feather-help-circle">
    <circle cx="12" cy="12" r="10" />
    <path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3" />
    <line x1="12" y1="17" x2="12.01" y2="17" />
  </svg>
);

export const BackSvg = (
  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor"
       strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="feather feather-arrow-left">
    <line x1="19" y1="12" x2="5" y2="12" />
    <polyline points="12 19 5 12 12 5" />
  </svg>
);

export const AddSvg = (
  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor"
       strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="feather feather-plus">
    <line x1="12" y1="5" x2="12" y2="19"/>
    <line x1="5" y1="12" x2="19" y2="12"/>
  </svg>
);

export const InfoSvg = (
  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor"
       strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="feather feather-info">
    <circle cx="12" cy="12" r="10" />
    <line x1="12" y1="16" x2="12" y2="12" />
    <line x1="12" y1="8" x2="12.01" y2="8" />
  </svg>
);

export const FavouritesSvg = (
  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor"
       strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="feather feather-heart">
    <path
      d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"/>
  </svg>
);

export const ArchiveSvg = (
  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor"
       strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="feather feather-archive">
    <polyline points="21 8 21 21 3 21 3 8"/>
    <rect x="1" y="3" width="22" height="5"/>
    <line x1="10" y1="12" x2="14" y2="12"/>
  </svg>
);

export const SearchSvg = (
  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor"
       strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="feather feather-search">
    <circle cx="11" cy="11" r="8"/>
    <line x1="21" y1="21" x2="16.65" y2="16.65"/>
  </svg>
);

export const StarSvg = (
  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="#DA3743" stroke="currentColor"
       strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="feather feather-star">
    <polygon
  points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"/>
  </svg>
);

export const ReviewStarSvg = (
  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor"
       strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="feather feather-star">
    <polygon
      points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"/>
  </svg>
);

export const HomeIcon = () => (
  <SvgIcon>
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor"
         strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="feather feather-home">
      <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z" />
      <polyline points="9 22 9 12 15 12 15 22" />
    </svg>
  </SvgIcon>
);

export const MenuIcon = () => (
  <SvgIcon>
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
         stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"
         className="feather feather-menu">
      <line x1="3" y1="12" x2="21" y2="12" />
      <line x1="3" y1="6" x2="21" y2="6" />
      <line x1="3" y1="18" x2="21" y2="18" />
    </svg>
  </SvgIcon>
);

export const SlidersHorizontal = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M11.5625 13.4365L3.125 13.4365" stroke="#BAB9B8" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M16.875 13.4365L14.6875 13.4365" stroke="#BAB9B8" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M13.125 14.999C13.9879 14.999 14.6875 14.2995 14.6875 13.4365C14.6875 12.5736 13.9879 11.874 13.125 11.874C12.2621 11.874 11.5625 12.5736 11.5625 13.4365C11.5625 14.2995 12.2621 14.999 13.125 14.999Z" fill="#BAB9B8" stroke="#BAB9B8" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M6.5625 6.5616L3.125 6.56152" stroke="#BAB9B8" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M16.875 6.56152L9.6875 6.5616" stroke="#BAB9B8" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M8.125 8.12402C8.98794 8.12402 9.6875 7.42447 9.6875 6.56152C9.6875 5.69858 8.98794 4.99902 8.125 4.99902C7.26206 4.99902 6.5625 5.69858 6.5625 6.56152C6.5625 7.42447 7.26206 8.12402 8.125 8.12402Z" fill="#BAB9B8" stroke="#BAB9B8" strokeLinecap="round" strokeLinejoin="round"/>
  </svg>
)

export const ChevronUpDown = () => (
  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd" d="M15.1755 10.6243C15.4099 10.8586 15.7898 10.8586 16.0241 10.6243C16.2584 10.39 16.2584 10.0101 16.0241 9.77574L12.4241 6.17574C12.3115 6.06321 12.1589 6 11.9998 6C11.8407 6 11.6881 6.06321 11.5755 6.17574L7.97554 9.77574C7.74123 10.0101 7.74123 10.39 7.97554 10.6243C8.20986 10.8586 8.58975 10.8586 8.82407 10.6243L11.9998 7.44853L15.1755 10.6243ZM8.82407 13.3757C8.58975 13.1414 8.20986 13.1414 7.97554 13.3757C7.74123 13.61 7.74123 13.9899 7.97554 14.2243L11.5755 17.8243C11.6881 17.9368 11.8407 18 11.9998 18C12.1589 18 12.3115 17.9368 12.4241 17.8243L16.0241 14.2243C16.2584 13.9899 16.2584 13.61 16.0241 13.3757C15.7898 13.1414 15.4099 13.1414 15.1755 13.3757L11.9998 16.5515L8.82407 13.3757Z" fill="#BAB9B8"/>
  </svg>
)

export const OrdersIcon = () => (
  <SvgIcon>
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor"
         strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="feather feather-shopping-bag">
      <path d="M6 2L3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4z" />
      <line x1="3" y1="6" x2="21" y2="6" />
      <path d="M16 10a4 4 0 0 1-8 0" />
    </svg>
  </SvgIcon>
);

export const PayIcon = () => (
  <SvgIcon>
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor"
         strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="feather feather-credit-card">
      <rect x="1" y="4" width="22" height="16" rx="2" ry="2" />
      <line x1="1" y1="10" x2="23" y2="10" />
    </svg>
  </SvgIcon>
);

export const ScanIcon = ({ stroke = 2 }) => (
  <SvgIcon>
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
         stroke="currentColor" strokeWidth={stroke} strokeLinecap="round" strokeLinejoin="round"
         className="feather feather-maximize">
      <path d="M8 3H5a2 2 0 0 0-2 2v3m18 0V5a2 2 0 0 0-2-2h-3m0 18h3a2 2 0 0 0 2-2v-3M3 16v3a2 2 0 0 0 2 2h3" />
      <path d="M3 12H21" />
    </svg>
  </SvgIcon>
);

export const ChatIcon = ({ stroke = 2 }) => (
  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor"
       strokeWidth={stroke} strokeLinecap="round" strokeLinejoin="round" className="feather feather-message-circle">
    <path d="M21 11.5a8.38 8.38 0 0 1-.9 3.8 8.5 8.5 0 0 1-7.6 4.7 8.38 8.38 0 0 1-3.8-.9L3 21l1.9-5.7a8.38 8.38 0 0 1-.9-3.8 8.5 8.5 0 0 1 4.7-7.6 8.38 8.38 0 0 1 3.8-.9h.5a8.48 8.48 0 0 1 8 8v.5z" />
  </svg>
);

export const SendIcon = ({ stroke = 2 }) => (
  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor"
       strokeWidth={stroke} strokeLinecap="round" strokeLinejoin="round" className="feather feather-send">
    <line x1="22" y1="2" x2="11" y2="13"/>
    <polygon points="22 2 15 22 11 13 2 9 22 2"/>
  </svg>
);

export const UsersIcon = (props) => (
  <SvgIcon {...props}>
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor"
         strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="feather feather-users">
      <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2" />
      <circle cx="9" cy="7" r="4" />
      <path d="M23 21v-2a4 4 0 0 0-3-3.87" />
      <path d="M16 3.13a4 4 0 0 1 0 7.75" />
    </svg>
  </SvgIcon>
);

export const UserIcon = ({ stroke = 2 }) => (
  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
       stroke="currentColor" strokeWidth={stroke} strokeLinecap="round" strokeLinejoin="round"
       className="feather feather-user">
    <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2" />
    <circle cx="12" cy="7" r="4" />
  </svg>
);

export const SettingsIcon = () => (
  <SvgIcon>
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor"
         strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="feather feather-settings">
      <circle cx="12" cy="12" r="3" />
      <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z" />
    </svg>
  </SvgIcon>
);

export const CloseIcon = () => (
  <SvgIcon>
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor"
         strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="feather feather-x">
      <line x1="18" y1="6" x2="6" y2="18"/>
      <line x1="6" y1="6" x2="18" y2="18"/>
    </svg>
  </SvgIcon>
);

export const MoreIcon = () => (
  <SvgIcon>
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor"
         strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="feather feather-more-vertical">
      <circle cx="12" cy="12" r="1"/>
      <circle cx="12" cy="5" r="1"/>
      <circle cx="12" cy="19" r="1"/>
    </svg>
  </SvgIcon>
);

export const TableIcon = () => (
  <SvgIcon>
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor"
         strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="feather feather-user-plus">
      <path d="M16 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"/>
      <circle cx="8.5" cy="7" r="4"/>
      <line x1="20" y1="8" x2="20" y2="14"/>
      <line x1="23" y1="11" x2="17" y2="11"/>
    </svg>
  </SvgIcon>
);

export const GridIcon = ({ stroke = 2 }) => (
  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor"
       strokeWidth={stroke} strokeLinecap="round" strokeLinejoin="round" className="feather feather-grid">
    <rect x="3" y="3" width="7" height="7"/>
    <rect x="14" y="3" width="7" height="7"/>
    <rect x="14" y="14" width="7" height="7"/>
    <rect x="3" y="14" width="7" height="7"/>
  </svg>
);

export const CompassIcon = ({ stroke = 2 }) => (
  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor"
       strokeWidth={stroke} strokeLinecap="round" strokeLinejoin="round" className="feather feather-compass">
    <circle cx="12" cy="12" r="10"/>
    <polygon points="16.24 7.76 14.12 14.12 7.76 16.24 9.88 9.88 16.24 7.76"/>
  </svg>
);

export const UserTickIcon = ({ stroke = 2 }) => (
  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor"
       strokeWidth={stroke} strokeLinecap="round" strokeLinejoin="round" className="feather feather-user-check">
    <path d="M16 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"/>
    <circle cx="8.5" cy="7" r="4"/>
    <polyline points="17 11 19 13 23 9"/>
  </svg>
);

export const GluttonyIcon = () => (
  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" id="logo">
    <rect width="24" height="24" rx="6" fill="#DA3743"/>
    <path d="M12 14.4C12 13.0745 13.0745 12 14.4 12H21.6C22.9255 12 24 13.0745 24 14.4V18C24 21.3137 21.3137 24 18 24H14.4C13.0745 24 12 22.9255 12 21.6V14.4Z" fill="#E15F67"/>
  </svg>
);

export const NavigationIcon = ({ color }) => (
  <SvgIcon color={color}>
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor"
         strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="feather feather-chevron-right">
      <polyline points="9 18 15 12 9 6"/>
    </svg>
  </SvgIcon>
);

export const ExternalLinkIcon = ({ color }) => (
  <SvgIcon color={color}>
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor"
         strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="feather feather-external-link">
      <path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"/>
      <polyline points="15 3 21 3 21 9"/>
      <line x1="10" y1="14" x2="21" y2="3"/>
    </svg>
  </SvgIcon>
);

export const ActivityIcon = () => (
  <SvgIcon>
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor"
         strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="feather feather-trending-up">
      <polyline points="23 6 13.5 15.5 8.5 10.5 1 18"/>
      <polyline points="17 6 23 6 23 12"/>
    </svg>
  </SvgIcon>
);

export const PlannerIcon = () => (
  <SvgIcon>
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor"
         strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="feather feather-trello">
      <rect x="5" y="5" width="4" height="9"/>
      <rect x="14" y="5" width="4" height="5"/>
      <rect x="14" y="13" width="4" height="5"/>
    </svg>
  </SvgIcon>
);

export const HashIcon = () => (
  <SvgIcon>
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor"
         strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="feather feather-hash">
      <line x1="4" y1="9" x2="20" y2="9"/>
      <line x1="4" y1="15" x2="20" y2="15"/>
      <line x1="10" y1="3" x2="8" y2="21"/>
      <line x1="16" y1="3" x2="14" y2="21"/>
    </svg>
  </SvgIcon>
);

export const EyeIcon = () => (
  <SvgIcon>
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor"
         strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="feather feather-eye">
      <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/>
      <circle cx="12" cy="12" r="3"/>
    </svg>
  </SvgIcon>
);

export const ArrowRightIcon = () => (
  <SvgIcon>
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor"
         strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="feather feather-arrow-right">
      <line x1="5" y1="12" x2="19" y2="12"/>
      <polyline points="12 5 19 12 12 19"/>
    </svg>
  </SvgIcon>
);

export const CheckIcon = (props) => (
  <SvgIcon {...props} >
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor"
         strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="feather feather-check">
      <polyline points="20 6 9 17 4 12"/>
    </svg>
  </SvgIcon>
);

export const LockIcon = (props) => (
  <SvgIcon {...props} >
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor"
         strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="feather feather-lock">
      <rect x="3" y="11" width="18" height="11" rx="2" ry="2"/>
      <path d="M7 11V7a5 5 0 0 1 10 0v4"/>
    </svg>
  </SvgIcon>
);

export const UpIcon = (props) => (
  <SvgIcon {...props} >
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor"
         strokeWidth="2.5" strokeLinecap="round" strokeLinejoin="round" className="feather feather-arrow-up">
      <line x1="12" y1="19" x2="12" y2="5"/>
      <polyline points="5 12 12 5 19 12"/>
    </svg>
  </SvgIcon>
);

export const SpicyIcon = (props) => (
  <SvgIcon {...props} >
    <svg version="1.1" id="Capa_1" x="0px"
         y="0px"
         viewBox="0 0 453.064 453.064">
      <polygon fill="#C7B299" points="41.908,444.142 41.908,434.542 41.908,434.542 "/>
      <path fill="#84D10F" d="M373.108,179.342c-4.8-7.2-11.2-13.6-17.6-18.4h-0.8c-10.4,3.2-20.8,8.8-29.6,15.2l-4.8,4l-5.6-4
      c-9.6-7.2-21.6-12-33.6-13.6l-9.6-1.6l3.2-8.8c3.2-9.6,8.8-17.6,15.2-24.8c27.2-27.2,70.4-28,98.4-0.8c8.8,8.8,16,20,18.4,32.8
      l1.6,6.4l-5.6,3.2c-6.4,3.2-12.8,8-18.4,12.8l-7.2,5.6L373.108,179.342z"/>
      <g>
        <path fill="#A7E51F"
              d="M367.508,160.942c4.8,4,8.8,8.8,12,14.4l1.6-1.6C377.108,168.942,372.308,164.942,367.508,160.942z"
        />
        <path fill="#A7E51F" d="M304.308,139.342c11.2-12,27.2-18.4,44-18.4c12,0,24,4,34.4,11.2c-1.6-15.2-25.6-22.4-41.6-22.4
        c-18.4,0.8-36.8,8-50.4,20.8c-7.2,9.6-12.8,20-17.6,31.2c2.4,0.8,16,7.2,19.2-4C295.508,150.542,299.508,144.142,304.308,139.342z"
        />
      </g>
      <path fill="#50AC25" d="M350.708,108.142c-3.2,0-17.6,0-18.4,8c24.8,3.2,44.8,21.6,51.2,45.6c-3.2,1.6-6.4,3.2-9.6,5.6
      c-8,9.6,2.4,14.4,4,17.6c9.6-6.4,20.8-11.2,31.2-16C401.908,141.742,378.708,108.142,350.708,108.142z"/>
      <path fill="#D60606" d="M63.508,427.342l5.6-3.2c35.2-21.6,94.4-57.6,133.6-100c61.6-67.2,69.6-114.4,68.8-148
      c0-0.8,0-1.6,0-2.4v-11.2l9.6,1.6c11.2,2.4,31.2,7.2,36.8,17.6l0.8,0.8h0.8c8.8-7.2,19.2-13.6,30.4-18.4l4-1.6l3.2,3.2
      c8,6.4,14.4,14.4,18.4,24l0.8,0.8l0,0h0.8c6.4-6.4,12.8-12,20-16.8l14.4-8.8l-0.8,16c-2.4,60-51.2,131.2-128.8,192
      c-68.8,53.6-163.2,66.4-213.6,68.8L63.508,427.342z"/>
      <path fill="#BA0808" d="M381.108,187.342c-3.2,3.2-4,7.2-3.2,12c-16.8,47.2-58.4,98.4-116.8,144
      c-43.2,34.4-96.8,51.2-143.2,60c-23.2,0.8-38.4,12.8-51.2,20c-1.6,1.6-20,18.4-22.4,19.2c52.8-2.4,172.8-14.4,239.2-65.6
      c76.8-59.2,124.8-145.6,128-203.2C405.108,178.542,385.908,182.542,381.108,187.342z"/>
      <path fill="#E82525" d="M94.708,430.542c36-21.6,95.2-57.6,136-101.6c61.6-67.2,70.4-115.2,70.4-150.4
      c6.4-13.6-21.6-16.8-28.8-18.4c0,0.8-4.8,19.2-4.8,20c0.8,35.2-4,74.4-68,144c-40,44-95.2,74.4-130.4,96c-1.6,1.6-31.2,24-32.8,24.8
      c7.2,0,48-3.2,56-13.6L94.708,430.542z"/>
      <path fill="#1C1211" d="M417.108,17.742c4.8-1.6,7.2-7.2,4.8-12s-6.4-7.2-11.2-4.8l0,0c-56,22.4-69.6,75.2-72.8,96
      c-20.8,0.8-40,9.6-54.4,24c-14.4,15.2-23.2,35.2-22.4,56c0.8,32-6.4,76.8-66.4,140.8c-37.6,41.6-96,76.8-130.4,97.6
      c-30.4,18.4-36,21.6-33.6,30.4c1.6,4.8,6.4,8,11.2,7.2c46.4,0,162.4-7.2,245.6-72c84-65.6,132.8-140.8,132.8-205.6
      c0-4,0-7.2-0.8-11.2l0,0l0,0c-4.8-33.6-29.6-60-63.2-66.4C359.508,80.142,371.508,36.142,417.108,17.742z M276.308,367.342
      c-66.4,51.2-156,64-208.8,67.2l5.6-3.2c35.2-21.6,95.2-57.6,136-101.6c64-69.6,71.2-118.4,71.2-153.6c0-0.8,0-2.4,0-3.2
      c13.6,2.4,28,8,31.2,13.6c2.4,4.8,8,6.4,12,4c0.8,0,1.6-0.8,1.6-0.8c8-7.2,17.6-12.8,28-16.8c7.2,5.6,12,12,15.2,20
      c0.8,3.2,3.2,5.6,6.4,6.4c0.8,0,1.6,0,2.4,0c2.4,0,4.8-0.8,6.4-3.2c5.6-5.6,12-11.2,18.4-15.2
      C399.508,238.542,353.108,307.342,276.308,367.342z M400.308,160.942c-7.2,4-14.4,8.8-20.8,13.6c-5.6-8-12-15.2-20-20.8
      c-2.4-1.6-4.8-1.6-7.2-1.6c-11.2,3.2-22.4,8.8-32,16c-11.2-8-24-12.8-36.8-14.4c3.2-8,7.2-15.2,13.6-21.6c24-24,62.4-24.8,86.4-0.8
      C391.508,140.142,397.908,149.742,400.308,160.942z"/>
    </svg>
  </SvgIcon>
);

export const AdminDining = () => (
  <SvgIcon style={{ width: 27, height: 27 }}>
    <svg xmlns="http://www.w3.org/2000/svg" width="20pt" height="20pt" viewBox="0 0 20 20" version="1.1">
      <g id="surface1">
        <path style={{ stroke:'none', fillRule: 'nonzero', fill: 'rgb(0%,0%,0%)', fillOpacity: 1 }} d="M 19.761719 4.828125 C 19.59375 4.640625 19.351562 4.535156 19.101562 4.535156 L 18.832031 4.535156 C 18.132812 4.535156 17.558594 5.0625 17.496094 5.757812 L 17.207031 9.050781 C 17.183594 9.34375 16.941406 9.5625 16.648438 9.5625 L 14.148438 9.5625 C 13.347656 9.5625 12.695312 10.214844 12.695312 11.015625 L 12.695312 11.160156 C 12.695312 11.644531 13.089844 12.039062 13.574219 12.039062 L 14.109375 12.039062 L 13.582031 13.761719 C 13.582031 13.765625 13.582031 13.765625 13.578125 13.765625 L 13.175781 15.085938 C 13.128906 15.238281 13.214844 15.402344 13.371094 15.449219 C 13.398438 15.460938 13.429688 15.464844 13.457031 15.464844 C 13.582031 15.464844 13.699219 15.382812 13.738281 15.257812 L 14.078125 14.144531 L 18.09375 14.144531 L 18.4375 15.257812 C 18.472656 15.382812 18.589844 15.464844 18.714844 15.464844 C 18.742188 15.464844 18.773438 15.460938 18.800781 15.449219 C 18.957031 15.402344 19.042969 15.238281 18.996094 15.085938 L 18.039062 11.957031 C 18.207031 11.910156 18.367188 11.84375 18.519531 11.753906 C 18.660156 11.675781 18.710938 11.496094 18.628906 11.355469 C 18.550781 11.214844 18.371094 11.167969 18.230469 11.246094 C 17.992188 11.382812 17.722656 11.453125 17.449219 11.453125 L 13.574219 11.453125 C 13.410156 11.453125 13.28125 11.324219 13.28125 11.160156 L 13.28125 11.015625 C 13.28125 10.539062 13.671875 10.148438 14.148438 10.148438 L 16.648438 10.148438 C 17.25 10.148438 17.738281 9.699219 17.792969 9.101562 L 18.082031 5.8125 C 18.113281 5.417969 18.4375 5.121094 18.832031 5.121094 L 19.101562 5.121094 C 19.1875 5.121094 19.269531 5.15625 19.332031 5.222656 C 19.390625 5.289062 19.421875 5.375 19.414062 5.464844 L 19.011719 10.023438 C 19.003906 10.136719 18.980469 10.246094 18.949219 10.351562 C 18.898438 10.507812 18.984375 10.671875 19.140625 10.71875 C 19.296875 10.769531 19.460938 10.683594 19.507812 10.527344 C 19.554688 10.378906 19.582031 10.226562 19.597656 10.074219 L 19.996094 5.515625 C 20.019531 5.265625 19.933594 5.011719 19.761719 4.828125 Z M 14.722656 12.039062 L 17.449219 12.039062 L 17.914062 13.558594 L 14.257812 13.558594 Z M 14.722656 12.039062 "/>
        <path style={{ stroke:'none', fillRule: 'nonzero', fill: 'rgb(0%,0%,0%)', fillOpacity: 1 }} d="M 5.851562 9.5625 L 3.351562 9.5625 C 3.058594 9.5625 2.816406 9.34375 2.792969 9.050781 L 2.503906 5.757812 C 2.441406 5.0625 1.867188 4.535156 1.167969 4.535156 L 0.898438 4.535156 C 0.648438 4.535156 0.40625 4.640625 0.238281 4.828125 C 0.0664062 5.011719 -0.0195312 5.265625 0.00390625 5.515625 L 0.402344 10.074219 C 0.449219 10.613281 0.695312 11.109375 1.09375 11.472656 C 1.34375 11.703125 1.640625 11.867188 1.960938 11.957031 L 1.003906 15.085938 C 0.957031 15.238281 1.042969 15.402344 1.199219 15.449219 C 1.226562 15.460938 1.257812 15.464844 1.285156 15.464844 C 1.410156 15.464844 1.527344 15.382812 1.5625 15.257812 L 1.90625 14.144531 L 5.921875 14.144531 L 6.261719 15.257812 C 6.300781 15.382812 6.417969 15.464844 6.542969 15.464844 C 6.570312 15.464844 6.601562 15.460938 6.628906 15.449219 C 6.785156 15.402344 6.871094 15.238281 6.824219 15.085938 L 6.421875 13.765625 C 6.417969 13.765625 6.417969 13.765625 6.417969 13.761719 L 5.890625 12.039062 L 6.425781 12.039062 C 6.910156 12.039062 7.304688 11.644531 7.304688 11.160156 L 7.304688 11.015625 C 7.304688 10.214844 6.652344 9.5625 5.851562 9.5625 Z M 2.085938 13.558594 L 2.550781 12.039062 L 5.277344 12.039062 L 5.742188 13.558594 Z M 6.71875 11.160156 C 6.71875 11.324219 6.589844 11.453125 6.425781 11.453125 L 2.550781 11.453125 C 2.488281 11.453125 2.425781 11.449219 2.363281 11.441406 C 1.632812 11.359375 1.054688 10.777344 0.988281 10.023438 L 0.585938 5.464844 C 0.578125 5.375 0.609375 5.289062 0.667969 5.222656 C 0.730469 5.15625 0.8125 5.121094 0.898438 5.121094 L 1.167969 5.121094 C 1.5625 5.121094 1.886719 5.417969 1.917969 5.8125 L 2.207031 9.101562 C 2.261719 9.699219 2.75 10.148438 3.351562 10.148438 L 5.851562 10.148438 C 6.328125 10.148438 6.71875 10.539062 6.71875 11.015625 Z M 6.71875 11.160156 "/>
        <path style={{ stroke:'none', fillRule: 'nonzero', fill: 'rgb(0%,0%,0%)', fillOpacity: 1 }} d="M 12.222656 14.496094 C 12.105469 14.03125 11.683594 13.707031 11.203125 13.707031 L 11.023438 13.707031 L 11.023438 8.375 L 14.628906 8.375 C 14.835938 8.375 15.03125 8.28125 15.164062 8.121094 C 15.292969 7.960938 15.347656 7.75 15.304688 7.546875 C 15.179688 6.933594 14.632812 6.492188 14.007812 6.492188 L 7.917969 6.492188 C 7.753906 6.492188 7.625 6.621094 7.625 6.785156 C 7.625 6.945312 7.753906 7.078125 7.917969 7.078125 L 14.007812 7.078125 C 14.355469 7.078125 14.660156 7.324219 14.730469 7.664062 C 14.738281 7.707031 14.722656 7.738281 14.710938 7.75 C 14.699219 7.765625 14.671875 7.789062 14.628906 7.789062 L 5.371094 7.789062 C 5.328125 7.789062 5.300781 7.765625 5.289062 7.75 C 5.277344 7.738281 5.261719 7.707031 5.269531 7.664062 C 5.339844 7.324219 5.644531 7.078125 5.992188 7.078125 L 6.542969 7.078125 C 6.703125 7.078125 6.835938 6.945312 6.835938 6.785156 C 6.835938 6.621094 6.703125 6.492188 6.542969 6.492188 L 5.992188 6.492188 C 5.367188 6.492188 4.820312 6.933594 4.695312 7.546875 C 4.652344 7.75 4.703125 7.960938 4.835938 8.121094 C 4.96875 8.28125 5.164062 8.375 5.371094 8.375 L 8.976562 8.375 L 8.976562 13.707031 L 8.796875 13.707031 C 8.316406 13.707031 7.894531 14.03125 7.777344 14.496094 L 7.707031 14.761719 C 7.664062 14.929688 7.699219 15.109375 7.804688 15.246094 C 7.914062 15.382812 8.074219 15.464844 8.25 15.464844 L 11.75 15.464844 C 11.925781 15.464844 12.085938 15.382812 12.195312 15.246094 C 12.300781 15.109375 12.335938 14.929688 12.292969 14.761719 Z M 9.5625 8.375 L 10.4375 8.375 L 10.4375 13.707031 L 9.5625 13.707031 Z M 8.28125 14.878906 L 8.34375 14.644531 C 8.394531 14.4375 8.582031 14.292969 8.796875 14.292969 L 11.203125 14.292969 C 11.417969 14.292969 11.605469 14.4375 11.65625 14.644531 L 11.71875 14.878906 Z M 8.28125 14.878906 "/>
      </g>
    </svg>

    {/*<svg xmlns="http://www.w3.org/2000/svg" width="20pt" height="20pt" viewBox="0 0 20 20" version="1.1">*/}
    {/*  <g id="surface1">*/}
    {/*    <path style={{ stroke:'none', fillRule: 'nonzero', fill: 'rgb(0%,0%,0%)', fillOpacity: 1 }} d="M 13.488281 18.105469 L 14.074219 18.105469 L 14.074219 16.386719 L 19.414062 16.386719 L 19.414062 18.105469 L 20 18.105469 L 20 13.164062 L 13.488281 13.164062 Z M 19.414062 13.75 L 19.414062 15.800781 L 14.074219 15.800781 L 14.074219 13.75 Z M 19.414062 13.75 "/>*/}
    {/*    <path style={{ stroke:'none', fillRule: 'nonzero', fill: 'rgb(0%,0%,0%)', fillOpacity: 1 }} d="M 18.828125 5.480469 C 18.183594 5.480469 17.65625 6.007812 17.65625 6.652344 L 17.65625 7.597656 L 18.242188 7.597656 L 18.242188 6.652344 C 18.242188 6.332031 18.503906 6.066406 18.828125 6.066406 L 19.414062 6.066406 L 19.414062 12.578125 L 20 12.578125 L 20 5.480469 Z M 18.828125 5.480469 "/>*/}
    {/*    <path style={{ stroke:'none', fillRule: 'nonzero', fill: 'rgb(0%,0%,0%)', fillOpacity: 1 }} d="M 13.488281 12.578125 L 14.074219 12.578125 C 14.074219 12.253906 14.339844 11.992188 14.660156 11.992188 L 18.828125 11.992188 L 18.828125 11.40625 L 18.242188 11.40625 L 18.242188 8.183594 L 17.65625 8.183594 L 17.65625 11.40625 L 14.660156 11.40625 C 14.015625 11.40625 13.488281 11.933594 13.488281 12.578125 Z M 13.488281 12.578125 "/>*/}
    {/*    <path style={{ stroke:'none', fillRule: 'nonzero', fill: 'rgb(0%,0%,0%)', fillOpacity: 1 }} d="M 0.585938 6.066406 L 1.171875 6.066406 C 1.496094 6.066406 1.757812 6.332031 1.757812 6.652344 L 1.757812 7.597656 L 2.34375 7.597656 L 2.34375 6.652344 C 2.34375 6.007812 1.816406 5.480469 1.171875 5.480469 L 0 5.480469 L 0 12.578125 L 0.585938 12.578125 Z M 0.585938 6.066406 "/>*/}
    {/*    <path style={{ stroke:'none', fillRule: 'nonzero', fill: 'rgb(0%,0%,0%)', fillOpacity: 1 }} d="M 5.339844 11.40625 L 2.34375 11.40625 L 2.34375 8.183594 L 1.757812 8.183594 L 1.757812 11.40625 L 1.171875 11.40625 L 1.171875 11.992188 L 5.339844 11.992188 C 5.660156 11.992188 5.925781 12.253906 5.925781 12.578125 L 6.511719 12.578125 C 6.511719 11.933594 5.984375 11.40625 5.339844 11.40625 Z M 5.339844 11.40625 "/>*/}
    {/*    <path style={{ stroke:'none', fillRule: 'nonzero', fill: 'rgb(0%,0%,0%)', fillOpacity: 1 }} d="M 0 18.105469 L 0.585938 18.105469 L 0.585938 16.386719 L 5.925781 16.386719 L 5.925781 18.105469 L 6.511719 18.105469 L 6.511719 13.164062 L 0 13.164062 Z M 5.925781 13.75 L 5.925781 15.800781 L 0.585938 15.800781 L 0.585938 13.75 Z M 5.925781 13.75 "/>*/}
    {/*    <path style={{ stroke:'none', fillRule: 'nonzero', fill: 'rgb(0%,0%,0%)', fillOpacity: 1 }} d="M 11.171875 16.933594 L 11.171875 10.527344 C 11.171875 10.203125 11.433594 9.941406 11.757812 9.941406 L 16.484375 9.941406 L 16.484375 7.597656 L 10.292969 7.597656 L 10.292969 8.183594 L 15.898438 8.183594 L 15.898438 9.355469 L 4.101562 9.355469 L 4.101562 8.183594 L 9.707031 8.183594 L 9.707031 7.597656 L 3.515625 7.597656 L 3.515625 9.941406 L 8.242188 9.941406 C 8.566406 9.941406 8.828125 10.203125 8.828125 10.527344 L 8.828125 16.933594 C 8.828125 17.257812 8.566406 17.519531 8.242188 17.519531 L 7.070312 17.519531 L 7.070312 18.105469 L 12.929688 18.105469 L 12.929688 17.519531 L 11.757812 17.519531 C 11.433594 17.519531 11.171875 17.257812 11.171875 16.933594 Z M 9.257812 9.941406 L 10.742188 9.941406 C 10.644531 10.113281 10.585938 10.3125 10.585938 10.527344 L 10.585938 16.933594 C 10.585938 17.148438 10.644531 17.347656 10.742188 17.519531 L 9.257812 17.519531 C 9.355469 17.347656 9.414062 17.148438 9.414062 16.933594 L 9.414062 10.527344 C 9.414062 10.3125 9.355469 10.113281 9.257812 9.941406 Z M 9.257812 9.941406 "/>*/}
    {/*    <path style={{ stroke:'none', fillRule: 'nonzero', fill: 'rgb(0%,0%,0%)', fillOpacity: 1 }} d="M 14.511719 6.425781 L 13.621094 6.425781 C 13.476562 4.644531 12.054688 3.21875 10.273438 3.078125 L 10.273438 2.480469 L 10.859375 2.480469 L 10.859375 1.894531 L 9.101562 1.894531 L 9.101562 2.480469 L 9.6875 2.480469 L 9.6875 3.078125 C 8.75 3.152344 7.871094 3.589844 7.246094 4.296875 L 7.683594 4.6875 C 8.265625 4.03125 9.101562 3.652344 9.980469 3.652344 C 11.570312 3.652344 12.882812 4.871094 13.03125 6.425781 L 6.925781 6.425781 C 6.972656 5.960938 7.121094 5.515625 7.363281 5.117188 L 6.867188 4.808594 C 6.566406 5.300781 6.386719 5.851562 6.339844 6.425781 L 5.449219 6.425781 L 5.449219 7.011719 L 14.511719 7.011719 Z M 14.511719 6.425781 "/>*/}
    {/*    <path style={{ stroke:'none', fillRule: 'nonzero', fill: 'rgb(0%,0%,0%)', fillOpacity: 1 }} d="M 12.074219 5.390625 C 11.679688 4.769531 11.039062 4.355469 10.3125 4.261719 L 10.234375 4.839844 C 10.789062 4.914062 11.277344 5.230469 11.582031 5.703125 Z M 12.074219 5.390625 "/>*/}
    {/*    <path style={{ stroke:'none', fillRule: 'nonzero', fill: 'rgb(0%,0%,0%)', fillOpacity: 1 }} d="M 9.648438 4.261719 C 9.410156 4.292969 9.175781 4.359375 8.957031 4.457031 L 9.199219 4.992188 C 9.367188 4.914062 9.542969 4.863281 9.726562 4.839844 Z M 9.648438 4.261719 "/>*/}
    {/*  </g>*/}
    {/*</svg>*/}
  </SvgIcon>
);

export const AdminFloorPlan = () => (
  <SvgIcon style={{ width: 27, height: 27 }}>
    <svg xmlns="http://www.w3.org/2000/svg" width="24pt" height="24pt" viewBox="0 0 24 24" version="1.1">
      <g id="surface1">
        <path d="M 22.070312 19.570312 L 19.027344 19.570312 L 19.027344 16.195312 C 19.027344 16 18.871094 15.84375 18.675781 15.84375 L 13.433594 15.84375 L 13.433594 10.4375 C 13.433594 10.242188 13.277344 10.085938 13.082031 10.085938 C 12.886719 10.085938 12.730469 10.242188 12.730469 10.4375 L 12.730469 15.84375 L 9.707031 15.84375 L 9.707031 7.226562 L 12.730469 7.226562 L 12.730469 9.03125 C 12.730469 9.226562 12.886719 9.382812 13.082031 9.382812 C 13.277344 9.382812 13.433594 9.226562 13.433594 9.03125 L 13.433594 6.875 C 13.433594 6.679688 13.277344 6.523438 13.082031 6.523438 L 1.898438 6.523438 C 1.703125 6.523438 1.546875 6.679688 1.546875 6.875 C 1.546875 7.207031 1.550781 17.191406 1.550781 18.671875 C 1.550781 18.867188 1.710938 19.023438 1.902344 19.023438 C 2.097656 19.023438 2.253906 18.867188 2.253906 18.671875 L 2.253906 14.679688 L 5.277344 14.679688 L 5.277344 23.296875 L 2.253906 23.296875 L 2.253906 20.078125 C 2.253906 19.882812 2.097656 19.726562 1.902344 19.726562 C 1.710938 19.726562 1.550781 19.882812 1.550781 20.078125 L 1.550781 23.648438 C 1.550781 23.84375 1.710938 24 1.902344 24 L 22.070312 24 C 22.265625 24 22.421875 23.84375 22.421875 23.648438 L 22.421875 19.921875 C 22.421875 19.726562 22.265625 19.570312 22.070312 19.570312 Z M 18.324219 16.546875 L 18.324219 19.570312 L 9.707031 19.570312 L 9.707031 16.546875 Z M 5.980469 19.570312 L 5.980469 10.953125 L 9.003906 10.953125 L 9.003906 19.570312 Z M 2.25 7.226562 L 9.003906 7.226562 L 9.003906 10.25 L 2.25 10.25 Z M 2.25 13.976562 L 2.25 10.953125 L 5.277344 10.953125 L 5.277344 13.976562 Z M 5.980469 20.273438 L 14.597656 20.273438 L 14.597656 23.296875 L 5.980469 23.296875 Z M 21.71875 23.296875 L 15.300781 23.296875 L 15.300781 20.273438 L 21.71875 20.273438 Z M 21.71875 23.296875 "/>
        <path d="M 22.070312 0 L 12.75 0 C 12.554688 0 12.398438 0.15625 12.398438 0.351562 L 12.398438 4.078125 C 12.398438 4.273438 12.554688 4.429688 12.75 4.429688 L 17.992188 4.429688 L 17.992188 13.398438 C 17.992188 13.59375 18.152344 13.75 18.34375 13.75 L 22.070312 13.75 C 22.265625 13.75 22.421875 13.59375 22.421875 13.398438 L 22.421875 0.351562 C 22.421875 0.15625 22.265625 0 22.070312 0 Z M 13.101562 0.703125 L 21.71875 0.703125 L 21.71875 3.726562 L 13.101562 3.726562 Z M 21.71875 13.046875 L 18.695312 13.046875 L 18.695312 4.429688 L 21.71875 4.429688 Z M 21.71875 13.046875 "/>
      </g>
    </svg>
  </SvgIcon>
);

export const HeartIcon = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M13 4.00001C12.4179 3.99901 11.8436 4.13385 11.3228 4.39381C10.802 4.65377 10.3491 5.03168 10 5.49747C9.52723 4.86844 8.86861 4.404 8.11736 4.16988C7.3661 3.93576 6.56024 3.94382 5.81381 4.19291C5.06738 4.442 4.41819 4.91951 3.95808 5.55787C3.49798 6.19623 3.25027 6.96311 3.25 7.75001C3.25 12.2414 9.49005 15.7875 9.75568 15.9363C9.83032 15.9781 9.91445 16 10 16C10.0856 16 10.1697 15.9781 10.2443 15.9363C11.392 15.2641 12.4691 14.4782 13.4595 13.5904C15.6429 11.6253 16.75 9.66032 16.75 7.75001C16.7489 6.75579 16.3534 5.80262 15.6504 5.0996C14.9474 4.39658 13.9942 4.00113 13 4.00001Z" fill="#BAB9B8"/>
  </svg>
)

export const TerminalMenuIcon = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M8.00684 3.62162L13.3896 4.57377C13.9609 4.67533 14.437 4.83402 14.8306 5.04984L14.8242 4.6309C14.7988 3.25346 13.8403 2.5933 12.2725 2.8726L8.00684 3.62162ZM6.07715 15.917L13.0786 17.1485C14.4053 17.3833 15.2241 16.628 15.2241 15.1807V7.51908C15.2241 6.19242 14.5767 5.67826 13.231 5.4434L6.44531 4.24369C5.44873 4.0723 4.76953 4.63724 4.76953 5.62748V14.3999C4.76953 15.2505 5.22021 15.7647 6.07715 15.917ZM7.34033 8.03324C7.09277 7.99515 6.95312 7.80472 6.95312 7.55082C6.95312 7.24613 7.19434 7.04301 7.53076 7.10013L12.4121 7.95707C12.6724 8.00785 12.812 8.16019 12.812 8.43949C12.812 8.74418 12.5898 8.95365 12.2534 8.90287L7.34033 8.03324ZM7.34033 10.6358C7.09912 10.5913 6.95312 10.4073 6.95312 10.1534C6.95312 9.85502 7.19434 9.65189 7.53076 9.70902L12.4121 10.566C12.6724 10.6167 12.812 10.7691 12.812 11.0357C12.812 11.3531 12.5898 11.5625 12.2534 11.5118L7.34033 10.6358Z" fill="#BAB9B8"/>
  </svg>
)

export const CaretRightIcon = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M8.18213 5.45401L12.7276 9.99947L8.18213 14.5449" stroke="#929191" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
  </svg>
)

export const CaretUpIcon = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M5 12L10 7L15 12" stroke="#737372" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
  </svg>

)

export const CaretDownIcon = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M15 8L10 13L5 8" stroke="#737372" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
  </svg>

)

export const ScanQRCodeIcon = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M13.5 3H16C16.5523 3 17 3.44772 17 4V6.5" stroke="#BAB9B8" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M5.52002 5.71953C5.52002 5.60907 5.60956 5.51953 5.72002 5.51953H14.28C14.3905 5.51953 14.48 5.60907 14.48 5.71953V14.2795C14.48 14.39 14.3905 14.4795 14.28 14.4795H5.72002C5.60956 14.4795 5.52002 14.39 5.52002 14.2795V5.71953Z" fill="#BAB9B8" stroke="#BAB9B8" strokeWidth="1.5" strokeLinejoin="round"/>
    <path d="M6.5 17H4C3.44772 17 3 16.5523 3 16V13.5" stroke="#BAB9B8" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M17 13.5V16C17 16.5523 16.5523 17 16 17H13.5" stroke="#BAB9B8" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M3 6.5V4C3 3.44772 3.44772 3 4 3H6.5" stroke="#BAB9B8" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M3 10L17 10" stroke="#F2F2F2" strokeWidth="1.2" strokeLinecap="round"/>
  </svg>
)

export const MagnifierIcon = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M10.5 15.0001C7.46243 15.0001 5 12.5376 5 9.50006C5 6.4625 7.46243 4.00006 10.5 4.00006C13.5376 4.00006 16 6.4625 16 9.50006C16 12.5376 13.5376 15.0001 10.5 15.0001Z" stroke="#333332" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M5.61285 13.5383L6.03711 13.114L6.88564 13.9625L6.46137 14.3868L5.61285 13.5383ZM4.42385 16.4243C4.18954 16.6586 3.80964 16.6586 3.57532 16.4243C3.34101 16.19 3.34101 15.8101 3.57532 15.5758L4.42385 16.4243ZM6.46137 14.3868L4.42385 16.4243L3.57532 15.5758L5.61285 13.5383L6.46137 14.3868Z" fill="#333332"/>
  </svg>
)

export const CoffeeIcon16 = () => (
  <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M5.875 2.7998V4.4998" stroke="#929191" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M8 2.7998V4.4998" stroke="#929191" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M10.125 2.7998V4.4998" stroke="#929191" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M2.90039 12.2744H13.1004" stroke="#929191" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M2.4751 6.39941V6.47442C2.4751 9.52579 4.94872 11.9994 8.0001 11.9994C11.0515 11.9994 13.5251 9.52579 13.5251 6.47442V6.39941H2.4751Z" fill="#929191" stroke="#929191" strokeLinejoin="round"/>
  </svg>
)

export const DishesIcon16 = () => (
  <svg width="16" height="17" viewBox="0 0 16 17" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd" d="M6.59941 3.91836C6.59941 3.58699 6.33078 3.31836 5.99941 3.31836C5.66804 3.31836 5.39941 3.58699 5.39941 3.91836V5.51836C5.39941 5.84973 5.66804 6.11836 5.99941 6.11836C6.33078 6.11836 6.59941 5.84973 6.59941 5.51836V3.91836ZM8.59941 3.91836C8.59941 3.58699 8.33078 3.31836 7.99941 3.31836C7.66804 3.31836 7.39941 3.58699 7.39941 3.91836V5.51836C7.39941 5.84973 7.66804 6.11836 7.99941 6.11836C8.33078 6.11836 8.59941 5.84973 8.59941 5.51836V3.91836ZM9.99941 3.31836C10.3308 3.31836 10.5994 3.58699 10.5994 3.91836V5.51836C10.5994 5.84973 10.3308 6.11836 9.99941 6.11836C9.66804 6.11836 9.39941 5.84973 9.39941 5.51836V3.91836C9.39941 3.58699 9.66804 3.31836 9.99941 3.31836ZM2.39844 7.61875C2.39844 7.39784 2.57752 7.21875 2.79844 7.21875H13.1984C13.4194 7.21875 13.5984 7.39784 13.5984 7.61875C13.5984 9.52385 12.6471 11.2068 11.1936 12.2184H12.7982C13.1296 12.2184 13.3982 12.487 13.3982 12.8184C13.3982 13.1497 13.1296 13.4184 12.7982 13.4184H3.19824C2.86687 13.4184 2.59824 13.1497 2.59824 12.8184C2.59824 12.487 2.86687 12.2184 3.19824 12.2184H4.80326C3.34975 11.2068 2.39844 9.52385 2.39844 7.61875Z" fill="#BAB9B8"/>
  </svg>

)

export const DishesIcon16Active = () => (
  <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M5.875 2.80005V4.50005" stroke="#FAE0DA" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M8 2.80005V4.50005" stroke="#FAE0DA" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M10.125 2.80005V4.50005" stroke="#FAE0DA" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M2.90002 12.2751H13.1" stroke="#FAE0DA" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M2.47504 6.40015V6.47515C2.47504 9.52652 4.94866 12.0001 8.00004 12.0001C11.0514 12.0001 13.525 9.52652 13.525 6.47515V6.40015H2.47504Z" fill="#FAE0DA" stroke="#FAE0DA" strokeLinejoin="round"/>
  </svg>

)

export const DishesIcon16Taken = () => (
  <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M5.875 2.80005V4.50005" stroke="#BD7269" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M8 2.80005V4.50005" stroke="#BD7269" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M10.125 2.80005V4.50005" stroke="#BD7269" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M2.90002 12.2751H13.1" stroke="#BD7269" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M2.47504 6.40015V6.47515C2.47504 9.52652 4.94866 12.0001 8.00004 12.0001C11.0514 12.0001 13.525 9.52652 13.525 6.47515V6.40015H2.47504Z" fill="#BD7269" stroke="#BD7269" strokeLinejoin="round"/>
  </svg>

)

export const MartiniIcon16 = () => (
  <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M2.14171 2.94103C2.01572 2.81504 2.10495 2.59961 2.28314 2.59961H11.7174C11.8956 2.59961 11.9849 2.81504 11.8589 2.94103L7.14171 7.65819C7.06361 7.73629 6.93698 7.73629 6.85887 7.65819L2.14171 2.94103Z" fill="#929191" stroke="#929191" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M7 7.7998V11.3998" stroke="#929191" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M5 11.3994H9" stroke="#929191" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M3.3999 4.19922H10.5999" stroke="#929191" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
  </svg>
)

export const DrinksIcon16 = () => (
  <svg width="16" height="17" viewBox="0 0 16 17" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd" d="M2.71639 4.78365C2.21242 4.27968 2.56935 3.41797 3.28207 3.41797L12.7164 3.41797C13.4291 3.41797 13.786 4.27968 13.2821 4.78365L8.59902 9.4667L8.59902 12.2179H10.499C10.8304 12.2179 11.099 12.4865 11.099 12.8179C11.099 13.1492 10.8304 13.4179 10.499 13.4179L5.49902 13.4179C5.16765 13.4179 4.89902 13.1492 4.89902 12.8179C4.89902 12.4865 5.16765 12.2179 5.49902 12.2179H7.39902L7.39902 9.46629L2.71639 4.78365Z" fill="#BAB9B8"/>
  </svg>

)

export const DrinksIcon16Taken = () => (
  <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M3.14141 3.94152C3.01542 3.81553 3.10465 3.6001 3.28283 3.6001H12.7171C12.8953 3.6001 12.9846 3.81553 12.8586 3.94152L8.14141 8.65868C8.0633 8.73678 7.93667 8.73678 7.85857 8.65868L3.14141 3.94152Z" fill="#BD7269" stroke="#BD7269" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M7.99997 8.80005V12.4" stroke="#BD7269" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M5.99997 12.4001H9.99997" stroke="#BD7269" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M4.39996 5.2002H11.6" stroke="#BD7269" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
  </svg>

)

export const DrinksIcon16Active = () => (
  <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M3.14141 3.94152C3.01542 3.81553 3.10465 3.6001 3.28283 3.6001H12.7171C12.8953 3.6001 12.9846 3.81553 12.8586 3.94152L8.14141 8.65868C8.0633 8.73678 7.93667 8.73678 7.85857 8.65868L3.14141 3.94152Z" fill="#FAE0DA" stroke="#FAE0DA" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M8 8.80005V12.4" stroke="#FAE0DA" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M6 12.4001H10" stroke="#FAE0DA" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M4.39996 5.2002H11.6" stroke="#FAE0DA" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
  </svg>

)

export const CoffeeIcon = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M7.5 4.5V6.5" stroke="#BAB9B8" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M10 4.5V6.5" stroke="#BAB9B8" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M12.5 4.5V6.5" stroke="#BAB9B8" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M4 15.5H16" stroke="#BAB9B8" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M9.5 15.5H10.5C13.8137 15.5 16.5 12.8137 16.5 9.5V9H3.5V9.5C3.5 12.8137 6.18629 15.5 9.5 15.5Z" fill="#BAB9B8" stroke="#BAB9B8" strokeLinejoin="round"/>
  </svg>

)

export const DineIn = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M9 4L9.5 7C9.5 7.59674 9.26295 8.16903 8.84099 8.59099C8.41903 9.01295 7.84674 9.25 7.25 9.25C6.65326 9.25 6.08097 9.01295 5.65901 8.59099C5.23705 8.16903 5 7.59674 5 7L5.5 4" stroke="#BAB9B8" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M8.84099 8.59099C9.26295 8.16903 9.5 7.59674 9.5 7H5C5 7.59674 5.23705 8.16903 5.65901 8.59099C6.08097 9.01295 6.65326 9.25 7.25 9.25C7.84674 9.25 8.41903 9.01295 8.84099 8.59099Z" fill="#BAB9B8" stroke="#BAB9B8" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M7.25 4V6.5" stroke="#BAB9B8" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M7.25 9.25V16" stroke="#BAB9B8" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M14.75 12H11.25C11.25 12 12 4 14.75 4V16" fill="#BAB9B8"/>
    <path d="M14.75 12H11.25C11.25 12 12 4 14.75 4V16" stroke="#BAB9B8" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
  </svg>
)

export const ShoppingBag = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd" d="M3.50063 6.00014C3.50088 4.61929 4.6222 3.50045 6.00305 3.50085C9.01395 3.50175 10.9859 3.50175 13.9964 3.50085C15.3779 3.50045 16.4995 4.62034 16.4991 6.00187C16.4984 8.62223 16.4984 11.3778 16.4991 13.9982C16.4995 15.3797 15.3779 16.4996 13.9964 16.4992C10.9857 16.4983 9.01343 16.4983 6.00235 16.4992C4.6215 16.4996 3.50018 15.3807 3.50007 13.9999C3.49986 11.3784 3.50015 8.62159 3.50063 6.00014ZM8 7.00001C8 6.72387 7.77614 6.50001 7.5 6.50001C7.22386 6.50001 7 6.72387 7 7.00001C7 8.65687 8.34315 10 10 10C11.6569 10 13 8.65687 13 7.00001C13 6.72387 12.7761 6.50001 12.5 6.50001C12.2239 6.50001 12 6.72387 12 7.00001C12 8.10458 11.1046 9.00001 10 9.00001C8.89543 9.00001 8 8.10458 8 7.00001Z" fill="#BAB9B8"/>
  </svg>
)

export const ReservationsIcon = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd" d="M4.5 15.5C4.5 16.3284 5.17157 17 6 17H14.5C14.7761 17 15 16.7761 15 16.5C15 16.2239 14.7761 16 14.5 16H6C5.72386 16 5.5 15.7761 5.5 15.5C5.5 15.2239 5.72386 15 6 15L14 15C14.8284 15 15.5 14.3284 15.5 13.5L15.5 5.58072C15.5 5.31715 15.5 5.08977 15.4847 4.90249C15.4686 4.70481 15.4329 4.50821 15.3365 4.31902C15.1927 4.03677 14.9632 3.8073 14.681 3.66349C14.4918 3.56709 14.2952 3.53144 14.0975 3.51529C13.9102 3.49998 13.6829 3.49999 13.4193 3.5H13.4193L6.5 3.5C5.96957 3.5 5.46086 3.71072 5.08579 4.08579C4.71071 4.46086 4.5 4.96957 4.5 5.5L4.5 15.5ZM6.75 6.5C6.75 6.22386 6.97386 6 7.25 6L12.75 6C13.0261 6 13.25 6.22386 13.25 6.5C13.25 6.77614 13.0261 7 12.75 7L7.25 7C6.97386 7 6.75 6.77614 6.75 6.5ZM7.25 8.5C6.97386 8.5 6.75 8.72386 6.75 9C6.75 9.27614 6.97386 9.5 7.25 9.5H12.75C13.0261 9.5 13.25 9.27614 13.25 9C13.25 8.72386 13.0261 8.5 12.75 8.5H7.25Z" fill="#BAB9B8"/>
  </svg>
)

export const ReservationsIconActive = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd" d="M4.5 15.5C4.5 16.3284 5.17157 17 6 17H14.5C14.7761 17 15 16.7761 15 16.5C15 16.2239 14.7761 16 14.5 16H6C5.72386 16 5.5 15.7761 5.5 15.5C5.5 15.2239 5.72386 15 6 15L14 15C14.8284 15 15.5 14.3284 15.5 13.5L15.5 5.58072C15.5 5.31715 15.5 5.08977 15.4847 4.90249C15.4686 4.70481 15.4329 4.50821 15.3365 4.31902C15.1927 4.03677 14.9632 3.8073 14.681 3.66349C14.4918 3.56709 14.2952 3.53144 14.0975 3.51529C13.9102 3.49998 13.6829 3.49999 13.4193 3.5H13.4193L6.5 3.5C5.96957 3.5 5.46086 3.71072 5.08579 4.08579C4.71071 4.46086 4.5 4.96957 4.5 5.5L4.5 15.5ZM6.75 6.5C6.75 6.22386 6.97386 6 7.25 6L12.75 6C13.0261 6 13.25 6.22386 13.25 6.5C13.25 6.77614 13.0261 7 12.75 7L7.25 7C6.97386 7 6.75 6.77614 6.75 6.5ZM7.25 8.5C6.97386 8.5 6.75 8.72386 6.75 9C6.75 9.27614 6.97386 9.5 7.25 9.5H12.75C13.0261 9.5 13.25 9.27614 13.25 9C13.25 8.72386 13.0261 8.5 12.75 8.5H7.25Z" fill="#FAE0DA"/>
  </svg>
)

export const ReservationsIcon24 = () => (
  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd" d="M5.3999 18.6002C5.3999 19.5943 6.20579 20.4002 7.1999 20.4002H17.3999C17.7313 20.4002 17.9999 20.1316 17.9999 19.8002C17.9999 19.4688 17.7313 19.2002 17.3999 19.2002H7.1999C6.86853 19.2002 6.5999 18.9316 6.5999 18.6002C6.5999 18.2688 6.86853 18.0002 7.1999 18.0002H16.7999C17.794 18.0002 18.5999 17.1943 18.5999 16.2002V6.69706C18.5999 6.38077 18.5999 6.10792 18.5816 5.88318C18.5622 5.64597 18.5194 5.41004 18.4037 5.18302C18.2311 4.84432 17.9558 4.56896 17.6171 4.39639C17.3901 4.28071 17.1541 4.23792 16.9169 4.21854C16.6922 4.20018 16.4193 4.20019 16.1031 4.2002H16.103H7.7999C7.16338 4.2002 6.55293 4.45305 6.10285 4.90314C5.65276 5.35323 5.3999 5.96368 5.3999 6.6002V18.6002ZM8.1999 7.8002C8.1999 7.52405 8.42376 7.3002 8.6999 7.3002H15.2999C15.576 7.3002 15.7999 7.52405 15.7999 7.8002C15.7999 8.07634 15.576 8.3002 15.2999 8.3002H8.6999C8.42376 8.3002 8.1999 8.07634 8.1999 7.8002ZM8.6999 10.3002C8.42376 10.3002 8.1999 10.5241 8.1999 10.8002C8.1999 11.0763 8.42376 11.3002 8.6999 11.3002H15.2999C15.576 11.3002 15.7999 11.0763 15.7999 10.8002C15.7999 10.5241 15.576 10.3002 15.2999 10.3002H8.6999Z" fill="#BAB9B8"/>
  </svg>

)

export const Lightning = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M10.3235 15.397C9.75041 16.0236 9.46384 16.3369 9.25909 16.3475C9.08239 16.3566 8.91401 16.2717 8.81635 16.1241C8.70318 15.9532 8.78484 15.5365 8.94818 14.7032L9.32054 12.8033C9.36656 12.5686 9.38957 12.4512 9.3657 12.3474C9.34465 12.2559 9.29828 12.1722 9.23188 12.1058C9.15657 12.0306 9.04484 11.9878 8.82138 11.9023L6.34754 10.9557C5.9796 10.8149 5.79563 10.7445 5.70814 10.6223C5.63163 10.5155 5.60026 10.3828 5.6208 10.253C5.6443 10.1046 5.77724 9.95928 6.04313 9.66858L10.6764 4.60301C11.2496 3.9764 11.5362 3.66309 11.7409 3.65251C11.9176 3.64337 12.086 3.72831 12.1837 3.87585C12.2968 4.04681 12.2152 4.46348 12.0518 5.29682L11.6795 7.19664C11.6334 7.43143 11.6104 7.54882 11.6343 7.6526C11.6553 7.74408 11.7017 7.82779 11.7681 7.89415C11.8434 7.96942 11.9552 8.01218 12.1786 8.09768L14.6525 9.0443C15.0204 9.1851 15.2044 9.2555 15.2919 9.37766C15.3684 9.4845 15.3997 9.61716 15.3792 9.74695C15.3557 9.89536 15.2228 10.0407 14.9569 10.3314L10.3235 15.397Z" fill="#BAB9B8" stroke="#BAB9B8" strokeLinecap="round" strokeLinejoin="round"/>
  </svg>
)

export const CustomersIcon = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd" d="M10 3C6.13401 3 3 6.13401 3 10C3 11.9243 3.7765 13.6673 5.03327 14.9327C6.1126 13.3438 7.9344 12.3 10 12.3C12.0656 12.3 13.8874 13.3438 14.9667 14.9327C16.2235 13.6673 17 11.9243 17 10C17 6.13401 13.866 3 10 3ZM9.94973 11C11.3304 11 12.4497 9.88071 12.4497 8.5C12.4497 7.11929 11.3304 6 9.94973 6C8.56902 6 7.44973 7.11929 7.44973 8.5C7.44973 9.88071 8.56902 11 9.94973 11Z" fill="#BAB9B8"/>
    <path d="M3.5 10C3.5 6.41015 6.41015 3.5 10 3.5C13.5899 3.5 16.5 6.41015 16.5 10C16.5 13.5899 13.5899 16.5 10 16.5C6.41015 16.5 3.5 13.5899 3.5 10Z" stroke="#BAB9B8"/>
  </svg>
)

export const CustomersIconLight = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd" d="M10 3C6.13401 3 3 6.13401 3 10C3 11.9243 3.7765 13.6673 5.03327 14.9327C6.1126 13.3438 7.9344 12.3 10 12.3C12.0656 12.3 13.8874 13.3438 14.9667 14.9327C16.2235 13.6673 17 11.9243 17 10C17 6.13401 13.866 3 10 3ZM9.94973 11C11.3304 11 12.4497 9.88071 12.4497 8.5C12.4497 7.11929 11.3304 6 9.94973 6C8.56902 6 7.44973 7.11929 7.44973 8.5C7.44973 9.88071 8.56902 11 9.94973 11Z" fill="#EFEFEE"/>
    <path d="M3.5 10C3.5 6.41015 6.41015 3.5 10 3.5C13.5899 3.5 16.5 6.41015 16.5 10C16.5 13.5899 13.5899 16.5 10 16.5C6.41015 16.5 3.5 13.5899 3.5 10Z" stroke="#EFEFEE"/>
  </svg>
)

export const CustomersIcon24 = () => (
  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd" d="M10 3C6.13401 3 3 6.13401 3 10C3 11.9243 3.7765 13.6673 5.03327 14.9327C6.1126 13.3438 7.9344 12.3 10 12.3C12.0656 12.3 13.8874 13.3438 14.9667 14.9327C16.2235 13.6673 17 11.9243 17 10C17 6.13401 13.866 3 10 3ZM9.94973 11C11.3304 11 12.4497 9.88071 12.4497 8.5C12.4497 7.11929 11.3304 6 9.94973 6C8.56902 6 7.44973 7.11929 7.44973 8.5C7.44973 9.88071 8.56902 11 9.94973 11Z" fill="#BAB9B8"/>
    <path d="M3.5 10C3.5 6.41015 6.41015 3.5 10 3.5C13.5899 3.5 16.5 6.41015 16.5 10C16.5 13.5899 13.5899 16.5 10 16.5C6.41015 16.5 3.5 13.5899 3.5 10Z" stroke="#BAB9B8"/>
  </svg>
)

export const ReceiptsIcon = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd" d="M12.3 3.50004C13.4201 3.50005 13.9801 3.50005 14.4079 3.71804C14.7843 3.90979 15.0902 4.21575 15.282 4.59207C15.5 5.01989 15.5 5.57966 15.5 6.69918C15.5 9.00684 15.5 12.0468 15.5 13.9957V15.9991C15.5 16.191 15.3906 16.366 15.2183 16.4497C15.046 16.5335 14.8411 16.5113 14.6906 16.3926L13.9403 15.8008C13.8484 15.7283 13.8025 15.692 13.7511 15.6755C13.7056 15.6608 13.6574 15.6573 13.6103 15.6651C13.557 15.6739 13.5063 15.703 13.4048 15.7613L12.2326 16.4338C12.0787 16.5221 11.8895 16.5221 11.7356 16.4338L10.4766 15.7115C10.3897 15.6616 10.3462 15.6367 10.3 15.6269C10.2592 15.6183 10.217 15.6183 10.1761 15.6269C10.1299 15.6367 10.0865 15.6616 9.99952 15.7115L8.74054 16.4338C8.58661 16.5221 8.39749 16.5221 8.24356 16.4338L6.98458 15.7115C6.89764 15.6616 6.85417 15.6367 6.80799 15.6269C6.76713 15.6183 6.72492 15.6183 6.68406 15.6269C6.63788 15.6367 6.59441 15.6616 6.50747 15.7115L5.24849 16.4338C5.09375 16.5225 4.90353 16.522 4.74925 16.4325C4.59497 16.3429 4.5 16.1778 4.5 15.9991C4.5 15.3823 4.50001 14.7857 4.50002 14.2036C4.50007 12.1945 4.50697 9.05146 4.51299 6.69009C4.51583 5.57374 4.51725 5.01557 4.73564 4.58885C4.92768 4.21361 5.23368 3.90838 5.60941 3.7173C6.03669 3.50001 6.59516 3.50001 7.7121 3.50002L12.3 3.50004Z" fill="#BAB9B8"/>
    <path d="M7.25 6.5H12.75" stroke="white" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M7.25 9H12.75" stroke="white" strokeLinecap="round" strokeLinejoin="round"/>
  </svg>
)

export const AppStoreIcon = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M6.75 9.5C8.26878 9.5 9.5 8.26878 9.5 6.75C9.5 5.23122 8.26878 4 6.75 4C5.23122 4 4 5.23122 4 6.75C4 8.26878 5.23122 9.5 6.75 9.5Z" fill="#BAB9B8"/>
    <path d="M13.25 9.5C14.7688 9.5 16 8.26878 16 6.75C16 5.23122 14.7688 4 13.25 4C11.7312 4 10.5 5.23122 10.5 6.75C10.5 8.26878 11.7312 9.5 13.25 9.5Z" fill="#BAB9B8"/>
    <path d="M6.75 16C8.26878 16 9.5 14.7688 9.5 13.25C9.5 11.7312 8.26878 10.5 6.75 10.5C5.23122 10.5 4 11.7312 4 13.25C4 14.7688 5.23122 16 6.75 16Z" fill="#BAB9B8"/>
    <path d="M13.25 16C14.7688 16 16 14.7688 16 13.25C16 11.7312 14.7688 10.5 13.25 10.5C11.7312 10.5 10.5 11.7312 10.5 13.25C10.5 14.7688 11.7312 16 13.25 16Z" fill="#BAB9B8"/>
  </svg>
)

export const MyAppsIcon = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    {/*<path d="M6.75 9.5C8.26878 9.5 9.5 8.26878 9.5 6.75C9.5 5.23122 8.26878 4 6.75 4C5.23122 4 4 5.23122 4 6.75C4 8.26878 5.23122 9.5 6.75 9.5Z" fill="#73AF9F"/>*/}
    <path d="M13.25 9.5C14.7688 9.5 16 8.26878 16 6.75C16 5.23122 14.7688 4 13.25 4C11.7312 4 10.5 5.23122 10.5 6.75C10.5 8.26878 11.7312 9.5 13.25 9.5Z" fill="#BAB9B8"/>
    <path d="M6.75 16C8.26878 16 9.5 14.7688 9.5 13.25C9.5 11.7312 8.26878 10.5 6.75 10.5C5.23122 10.5 4 11.7312 4 13.25C4 14.7688 5.23122 16 6.75 16Z" fill="#BAB9B8"/>
    <path d="M13.25 16C14.7688 16 16 14.7688 16 13.25C16 11.7312 14.7688 10.5 13.25 10.5C11.7312 10.5 10.5 11.7312 10.5 13.25C10.5 14.7688 11.7312 16 13.25 16Z" fill="#BAB9B8"/>
  </svg>
)

export const ReportingIcon = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd" d="M14.3499 6.50006C13.7899 6.50008 13.5099 6.50009 13.296 6.60909C13.1078 6.70496 12.9549 6.85794 12.859 7.0461C12.75 7.26001 12.75 7.54003 12.75 8.10007L12.75 14.9C12.75 15.4601 12.75 15.7401 12.859 15.954C12.9549 16.1422 13.1079 16.2951 13.296 16.391C13.5099 16.5 13.79 16.5 14.35 16.5H14.65C15.2101 16.5 15.4901 16.5 15.704 16.391C15.8922 16.2951 16.0451 16.1422 16.141 15.954C16.25 15.7401 16.25 15.4601 16.25 14.9V8.10005C16.25 7.53998 16.25 7.25995 16.141 7.04603C16.0451 6.85787 15.8921 6.70489 15.704 6.60902C15.4901 6.50002 15.21 6.50003 14.6499 6.50005L14.3499 6.50006Z" fill="#BAB9B8"/>
    <path fillRule="evenodd" clipRule="evenodd" d="M9.84993 3.50008C9.2899 3.5001 9.00989 3.50012 8.79598 3.60911C8.60783 3.70499 8.45486 3.85797 8.35899 4.04613C8.25 4.26004 8.25 4.54005 8.25 5.10008L8.25001 14.9C8.25001 15.4601 8.25001 15.7401 8.35901 15.954C8.45488 16.1422 8.60786 16.2951 8.79602 16.391C9.00993 16.5 9.28996 16.5 9.85001 16.5H10.15C10.7101 16.5 10.9901 16.5 11.204 16.391C11.3922 16.2951 11.5451 16.1422 11.641 15.954C11.75 15.7401 11.75 15.4601 11.75 14.9V5.10007C11.75 4.53999 11.75 4.25996 11.641 4.04604C11.5451 3.85787 11.3921 3.70489 11.204 3.60902C10.99 3.50003 10.71 3.50004 10.1499 3.50007L9.84993 3.50008Z" fill="#BAB9B8"/>
    <path fillRule="evenodd" clipRule="evenodd" d="M5.34997 10.5C4.78993 10.5 4.50991 10.5001 4.296 10.609C4.10784 10.7049 3.95486 10.8579 3.85899 11.0461C3.75 11.26 3.75 11.54 3.75 12.1L3.75001 14.9C3.75001 15.4601 3.75001 15.7401 3.85901 15.954C3.95488 16.1422 4.10786 16.2951 4.29602 16.391C4.50993 16.5 4.78996 16.5 5.35001 16.5H5.65C6.21005 16.5 6.49008 16.5 6.70399 16.391C6.89215 16.2951 7.04513 16.1422 7.14101 15.954C7.25 15.7401 7.25 15.4601 7.25 14.9V12.1C7.25 11.54 7.25 11.2599 7.141 11.046C7.04513 10.8579 6.89214 10.7049 6.70398 10.609C6.49006 10.5 6.21003 10.5 5.64997 10.5L5.34997 10.5Z" fill="#BAB9B8"/>
  </svg>
)

export const ManagementIcon = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M9.6377 17.5264H10.8887C11.0938 17.5264 11.2669 17.4694 11.4082 17.3555C11.5495 17.2461 11.6429 17.0911 11.6885 16.8906L12.0303 15.4346C12.1488 15.3936 12.2627 15.3503 12.3721 15.3047C12.486 15.2637 12.5931 15.2204 12.6934 15.1748L13.9648 15.9473C14.1335 16.0521 14.3089 16.0954 14.4912 16.0771C14.6735 16.0635 14.8353 15.986 14.9766 15.8447L15.8516 14.9766C15.9928 14.8353 16.0726 14.6712 16.0908 14.4844C16.109 14.293 16.0612 14.113 15.9473 13.9443L15.168 12.6865C15.2227 12.5817 15.2705 12.4769 15.3115 12.3721C15.3525 12.2627 15.3913 12.1533 15.4277 12.0439L16.8975 11.6953C17.0934 11.6543 17.2461 11.5632 17.3555 11.4219C17.4694 11.276 17.5264 11.1006 17.5264 10.8955V9.67188C17.5264 9.47135 17.4694 9.30046 17.3555 9.15918C17.2461 9.01335 17.0934 8.91992 16.8975 8.87891L15.4414 8.53027C15.4004 8.40723 15.3571 8.29102 15.3115 8.18164C15.266 8.07227 15.2227 7.96973 15.1816 7.87402L15.9609 6.5957C16.0703 6.42708 16.1182 6.25163 16.1045 6.06934C16.0908 5.88704 16.0111 5.72526 15.8652 5.58398L14.9697 4.70215C14.8285 4.57454 14.6712 4.49935 14.498 4.47656C14.3294 4.45378 14.1608 4.49023 13.9922 4.58594L12.6934 5.38574C12.5977 5.33561 12.4928 5.29004 12.3789 5.24902C12.2695 5.20345 12.1533 5.16016 12.0303 5.11914L11.6885 3.64258C11.6429 3.44661 11.5495 3.29167 11.4082 3.17773C11.2669 3.05924 11.0938 3 10.8887 3H9.6377C9.43717 3 9.264 3.05924 9.11816 3.17773C8.97689 3.29167 8.88346 3.44661 8.83789 3.64258L8.49609 5.10547C8.3776 5.14648 8.26139 5.18978 8.14746 5.23535C8.03353 5.28092 7.92643 5.32878 7.82617 5.37891L6.53418 4.58594C6.36556 4.49023 6.19466 4.45378 6.02148 4.47656C5.85286 4.49479 5.69564 4.57227 5.5498 4.70898L4.66797 5.58398C4.52214 5.72526 4.4401 5.88704 4.42188 6.06934C4.4082 6.25163 4.45605 6.42708 4.56543 6.5957L5.33789 7.87402C5.29688 7.96973 5.25358 8.07227 5.20801 8.18164C5.16243 8.29102 5.12142 8.40723 5.08496 8.53027L3.63574 8.87891C3.43522 8.91992 3.27799 9.01335 3.16406 9.15918C3.05469 9.30046 3 9.47135 3 9.67188V10.8955C3 11.1006 3.05469 11.276 3.16406 11.4219C3.27799 11.5632 3.43522 11.6543 3.63574 11.6953L5.09863 12.0439C5.13053 12.1533 5.16927 12.2627 5.21484 12.3721C5.26042 12.4769 5.30599 12.5817 5.35156 12.6865L4.5791 13.9443C4.46973 14.113 4.42415 14.293 4.44238 14.4844C4.46061 14.6712 4.54036 14.8353 4.68164 14.9766L5.5498 15.8447C5.69108 15.986 5.85286 16.0635 6.03516 16.0771C6.21745 16.0954 6.3929 16.0521 6.56152 15.9473L7.83301 15.1748C7.93327 15.2204 8.03809 15.2637 8.14746 15.3047C8.26139 15.3503 8.3776 15.3936 8.49609 15.4346L8.83789 16.8906C8.88346 17.0911 8.97689 17.2461 9.11816 17.3555C9.264 17.4694 9.43717 17.5264 9.6377 17.5264ZM10.2666 12.6592C9.8291 12.6592 9.42806 12.5521 9.06348 12.3379C8.69889 12.1191 8.40951 11.8275 8.19531 11.4629C7.98112 11.0983 7.87402 10.6973 7.87402 10.2598C7.87402 9.82227 7.98112 9.4235 8.19531 9.06348C8.41406 8.70345 8.70345 8.41634 9.06348 8.20215C9.42806 7.9834 9.8291 7.87402 10.2666 7.87402C10.7041 7.87402 11.1029 7.9834 11.4629 8.20215C11.8275 8.41634 12.1169 8.70345 12.3311 9.06348C12.5452 9.4235 12.6523 9.82227 12.6523 10.2598C12.6523 10.6973 12.5452 11.0983 12.3311 11.4629C12.1169 11.8275 11.8275 12.1191 11.4629 12.3379C11.1029 12.5521 10.7041 12.6592 10.2666 12.6592Z" fill="#BAB9B8"/>
  </svg>

)

export const CheersIllustration = () => (
  <svg width="75" height="71" viewBox="0 0 75 71" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M21.6152 16.019L42.1764 31.1311L17.7897 58.1558L3.10313 47.3615L21.6152 16.019Z" fill="#BAB9B8"/>
    <path d="M64.8008 28.2358L40.2817 35.3035L53.816 69.0951L71.3296 64.0467L64.8008 28.2358Z" fill="#BAB9B8"/>
    <path d="M57.4276 63.1607C61.9302 64.9643 67.0513 62.7539 68.8659 58.2236L52.5605 51.6924C50.7459 56.2226 52.925 61.3572 57.4276 63.1607Z" fill="#737372"/>
    <rect x="35.2461" y="1.61523" width="2.2966" height="20.6694" transform="rotate(-6.88787 35.2461 1.61523)" fill="#929191"/>
    <rect x="55.5488" y="0.333496" width="3.07471" height="27.6724" transform="rotate(23.1121 55.5488 0.333496)" fill="#929191"/>
    <path d="M18.5258 33.4641C17.4408 28.2572 12.3403 24.9158 7.13342 26.0008L11.0625 44.8564C16.2693 43.7715 19.6108 38.6709 18.5258 33.4641Z" fill="#929191"/>
  </svg>

)

export const SunIcon = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M10 14.2188C12.33 14.2188 14.2188 12.33 14.2188 10C14.2188 7.67005 12.33 5.78125 10 5.78125C7.67005 5.78125 5.78125 7.67005 5.78125 10C5.78125 12.33 7.67005 14.2188 10 14.2188Z" fill="#BAB9B8" stroke="#BAB9B8" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M10 3.53125V2.125" stroke="#BAB9B8" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M5.42552 5.4254L4.43115 4.43103" stroke="#BAB9B8" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M3.53125 10H2.125" stroke="#BAB9B8" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M5.42552 14.5736L4.43115 15.568" stroke="#BAB9B8" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M10 16.4688V17.875" stroke="#BAB9B8" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M14.5737 14.5736L15.5681 15.568" stroke="#BAB9B8" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M16.4688 10H17.875" stroke="#BAB9B8" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M14.5737 5.4254L15.5681 4.43103" stroke="#BAB9B8" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
  </svg>

)

export const RevenueIcon = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M10 17C13.866 17 17 13.866 17 10C17 6.13401 13.866 3 10 3C6.13401 3 3 6.13401 3 10C3 13.866 6.13401 17 10 17Z" fill="#BAB9B8"/>
    <path d="M10 6.5V7.5" stroke="#F2F2F2" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M10 12.5V13.5" stroke="#F2F2F2" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M8.5 12.5H10.75C11.0815 12.5 11.3995 12.3683 11.6339 12.1339C11.8683 11.8995 12 11.5815 12 11.25C12 10.9185 11.8683 10.6005 11.6339 10.3661C11.3995 10.1317 11.0815 10 10.75 10H9.25C8.91848 10 8.60054 9.8683 8.36612 9.63388C8.1317 9.39946 8 9.08152 8 8.75C8 8.41848 8.1317 8.10054 8.36612 7.86612C8.60054 7.6317 8.91848 7.5 9.25 7.5H11.5" stroke="#F2F2F2" strokeLinecap="round" strokeLinejoin="round"/>
  </svg>
)

export const RevenueIcon24 = () => (
  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M11.9998 19.1998C15.9763 19.1998 19.1998 15.9763 19.1998 11.9998C19.1998 8.02335 15.9763 4.7998 11.9998 4.7998C8.02335 4.7998 4.7998 8.02335 4.7998 11.9998C4.7998 15.9763 8.02335 19.1998 11.9998 19.1998Z" fill="#BAB9B8" stroke="#BAB9B8" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M11.9998 7.7998V8.9998" stroke="#EFEFEE" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M11.9998 15V16.2" stroke="#EFEFEE" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M10.1999 15H12.8999C13.2977 15 13.6792 14.842 13.9605 14.5607C14.2418 14.2794 14.3999 13.8978 14.3999 13.5C14.3999 13.1022 14.2418 12.7206 13.9605 12.4393C13.6792 12.158 13.2977 12 12.8999 12H11.0999C10.702 12 10.3205 11.842 10.0392 11.5607C9.75789 11.2794 9.59985 10.8978 9.59985 10.5C9.59985 10.1022 9.75789 9.72064 10.0392 9.43934C10.3205 9.15804 10.702 9 11.0999 9H13.7999" stroke="#EFEFEE" strokeLinecap="round" strokeLinejoin="round"/>
  </svg>

)

export const CashJournalIcon = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd" d="M4.5 15.5C4.5 16.3284 5.17157 17 6 17H14.5C14.7761 17 15 16.7761 15 16.5C15 16.2239 14.7761 16 14.5 16H6C5.72386 16 5.5 15.7761 5.5 15.5C5.5 15.2239 5.72386 15 6 15H14C14.8284 15 15.5 14.3284 15.5 13.5V5.58072C15.5 5.31715 15.5 5.08977 15.4847 4.90249C15.4686 4.70481 15.4329 4.5082 15.3365 4.31902C15.1927 4.03677 14.9632 3.8073 14.681 3.66349C14.4918 3.56709 14.2952 3.53144 14.0975 3.51529C13.9102 3.49998 13.6828 3.49999 13.4193 3.5L6.5 3.5C5.96957 3.5 5.46086 3.71071 5.08579 4.08579C4.71071 4.46086 4.5 4.96957 4.5 5.5V15.5Z" fill="#BAB9B8"/>
    <path d="M10.2146 6V6.85714" stroke="#E8E7E6" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M10.2146 11.1427V11.9998" stroke="#E8E7E6" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M8.92857 11.143H10.8571C11.1413 11.143 11.4138 11.0301 11.6148 10.8292C11.8157 10.6283 11.9286 10.3557 11.9286 10.0716C11.9286 9.78742 11.8157 9.5149 11.6148 9.31397C11.4138 9.11304 11.1413 9.00016 10.8571 9.00016H9.57143C9.28727 9.00016 9.01475 8.88727 8.81381 8.68634C8.61288 8.48541 8.5 8.21289 8.5 7.92873C8.5 7.64457 8.61288 7.37205 8.81381 7.17111C9.01475 6.97018 9.28727 6.8573 9.57143 6.8573H11.5" stroke="#E8E7E6" strokeLinecap="round" strokeLinejoin="round"/>
  </svg>

)

export const CashJournalIcon24 = () => (
  <svg width="24" height="24" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd" d="M4.5 15.5C4.5 16.3284 5.17157 17 6 17H14.5C14.7761 17 15 16.7761 15 16.5C15 16.2239 14.7761 16 14.5 16H6C5.72386 16 5.5 15.7761 5.5 15.5C5.5 15.2239 5.72386 15 6 15H14C14.8284 15 15.5 14.3284 15.5 13.5V5.58072C15.5 5.31715 15.5 5.08977 15.4847 4.90249C15.4686 4.70481 15.4329 4.5082 15.3365 4.31902C15.1927 4.03677 14.9632 3.8073 14.681 3.66349C14.4918 3.56709 14.2952 3.53144 14.0975 3.51529C13.9102 3.49998 13.6828 3.49999 13.4193 3.5L6.5 3.5C5.96957 3.5 5.46086 3.71071 5.08579 4.08579C4.71071 4.46086 4.5 4.96957 4.5 5.5V15.5Z" fill="#BAB9B8"/>
    <path d="M10.2146 6V6.85714" stroke="#E8E7E6" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M10.2146 11.1427V11.9998" stroke="#E8E7E6" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M8.92857 11.143H10.8571C11.1413 11.143 11.4138 11.0301 11.6148 10.8292C11.8157 10.6283 11.9286 10.3557 11.9286 10.0716C11.9286 9.78742 11.8157 9.5149 11.6148 9.31397C11.4138 9.11304 11.1413 9.00016 10.8571 9.00016H9.57143C9.28727 9.00016 9.01475 8.88727 8.81381 8.68634C8.61288 8.48541 8.5 8.21289 8.5 7.92873C8.5 7.64457 8.61288 7.37205 8.81381 7.17111C9.01475 6.97018 9.28727 6.8573 9.57143 6.8573H11.5" stroke="#E8E7E6" strokeLinecap="round" strokeLinejoin="round"/>
  </svg>

)

export const DiscountIcon24 = () => (
  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd" d="M11.1433 3.31234C11.6407 2.89589 12.3593 2.89589 12.8568 3.31234L13.9651 4.24013C14.2283 4.4604 14.564 4.57114 14.9045 4.54987L16.3385 4.46045C16.9821 4.42032 17.5634 4.84896 17.7247 5.48261L18.0841 6.89438C18.1694 7.22957 18.3769 7.51939 18.6646 7.70534L19.8765 8.48842C20.4205 8.83992 20.6425 9.53347 20.406 10.1424L19.8791 11.4988C19.754 11.8209 19.754 12.1791 19.8791 12.5012L20.406 13.8577C20.6425 14.4666 20.4205 15.1601 19.8765 15.5116L18.6646 16.2947C18.3769 16.4806 18.1694 16.7705 18.0841 17.1057L17.7247 18.5174C17.5634 19.1511 16.9821 19.5797 16.3385 19.5396L14.9045 19.4501C14.564 19.4289 14.2283 19.5396 13.9651 19.7599L12.8568 20.6877C12.3593 21.1041 11.6407 21.1041 11.1433 20.6877L10.0349 19.7599C9.77177 19.5396 9.43607 19.4289 9.09558 19.4501L7.66157 19.5396C7.01792 19.5797 6.43661 19.1511 6.27528 18.5174L5.91598 17.1057C5.83066 16.7705 5.62317 16.4806 5.33542 16.2947L4.12351 15.5116C3.57951 15.1601 3.35746 14.4666 3.59399 13.8577L4.12096 12.5012C4.24605 12.1791 4.24605 11.8209 4.12096 11.4988L3.59399 10.1424C3.35746 9.53347 3.57951 8.83992 4.12351 8.48842L5.33542 7.70534C5.62317 7.51939 5.83066 7.22957 5.91598 6.89438L6.27528 5.48261C6.43661 4.84896 7.01792 4.42032 7.66157 4.46045L9.09558 4.54987C9.43607 4.57114 9.77177 4.4604 10.0349 4.24013L11.1433 3.31234ZM15.1661 9.54105C15.3613 9.34579 15.3613 9.02921 15.1661 8.83395C14.9708 8.63868 14.6542 8.63868 14.4589 8.83395L8.83395 14.4589C8.63868 14.6542 8.63868 14.9708 8.83395 15.1661C9.02921 15.3613 9.34579 15.3613 9.54105 15.1661L15.1661 9.54105ZM11.0625 9.96875C11.0625 10.5728 10.5728 11.0625 9.96875 11.0625C9.36469 11.0625 8.875 10.5728 8.875 9.96875C8.875 9.36469 9.36469 8.875 9.96875 8.875C10.5728 8.875 11.0625 9.36469 11.0625 9.96875ZM14.0312 15.125C14.6353 15.125 15.125 14.6353 15.125 14.0312C15.125 13.4272 14.6353 12.9375 14.0312 12.9375C13.4272 12.9375 12.9375 13.4272 12.9375 14.0312C12.9375 14.6353 13.4272 15.125 14.0312 15.125Z" fill="#929191"/>
  </svg>

)

export const DiscountIcon20 = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd" d="M9.28605 2.76028C9.7006 2.41324 10.2994 2.41324 10.714 2.76028L11.6376 3.53344C11.8569 3.717 12.1367 3.80928 12.4204 3.79156L13.6154 3.71704C14.1518 3.6836 14.6362 4.0408 14.7706 4.56884L15.0701 5.74532C15.1412 6.02464 15.3141 6.26616 15.5538 6.42112L16.5638 7.07368C17.0171 7.3666 17.2021 7.94456 17.005 8.45196L16.5659 9.58236C16.4617 9.85076 16.4617 10.1493 16.5659 10.4177L17.005 11.5481C17.2021 12.0555 17.0171 12.6334 16.5638 12.9264L15.5538 13.5789C15.3141 13.7338 15.1412 13.9754 15.0701 14.2547L14.7706 15.4312C14.6362 15.9592 14.1518 16.3164 13.6154 16.283L12.4204 16.2084C12.1367 16.1908 11.8569 16.283 11.6376 16.4666L10.714 17.2397C10.2994 17.5868 9.7006 17.5868 9.28605 17.2397L8.36244 16.4666C8.14314 16.283 7.86339 16.1908 7.57965 16.2084L6.38464 16.283C5.84827 16.3164 5.36384 15.9592 5.2294 15.4312L4.92998 14.2547C4.85888 13.9754 4.68597 13.7338 4.44618 13.5789L3.43626 12.9264C2.98293 12.6334 2.79788 12.0555 2.99499 11.5481L3.43413 10.4177C3.53838 10.1493 3.53838 9.85076 3.43413 9.58236L2.99499 8.45196C2.79788 7.94456 2.98293 7.3666 3.43626 7.07368L4.44618 6.42112C4.68597 6.26616 4.85888 6.02464 4.92998 5.74532L5.2294 4.56884C5.36384 4.0408 5.84827 3.6836 6.38464 3.71704L7.57965 3.79156C7.86339 3.80928 8.14314 3.717 8.36244 3.53344L9.28605 2.76028ZM12.6973 8.0098C12.8926 7.81454 12.8926 7.49796 12.6973 7.3027C12.502 7.10743 12.1855 7.10743 11.9902 7.3027L7.3027 11.9902C7.10743 12.1855 7.10743 12.502 7.3027 12.6973C7.49796 12.8926 7.81454 12.8926 8.0098 12.6973L12.6973 8.0098ZM9.21875 8.30729C9.21875 8.81068 8.81068 9.21875 8.30729 9.21875C7.80391 9.21875 7.39583 8.81068 7.39583 8.30729C7.39583 7.80391 7.80391 7.39583 8.30729 7.39583C8.81068 7.39583 9.21875 7.80391 9.21875 8.30729ZM11.6927 12.6042C12.1961 12.6042 12.6042 12.1961 12.6042 11.6927C12.6042 11.1893 12.1961 10.7812 11.6927 10.7812C11.1893 10.7812 10.7813 11.1893 10.7813 11.6927C10.7813 12.1961 11.1893 12.6042 11.6927 12.6042Z" fill="#BAB9B8"/>
  </svg>
)

export const PayrollIcon = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M6.5005 16C5.67207 16 5.00049 15.3284 5.0005 14.5L5.00057 5.49986C5.00058 4.67141 5.67159 3.99984 6.50003 3.99987C7.94594 3.99992 10.0613 3.99999 11.0861 4.00001C11.3513 4.00001 11.6055 4.10537 11.793 4.29291L14.7072 7.20712C14.8948 7.39466 15.0001 7.64724 15.0001 7.91246C15.0001 9.60944 15.0001 12.67 15.0001 14.5005C15.0001 15.3289 14.3285 16 13.5001 16H6.5005Z" fill="#BAB9B8" stroke="#BAB9B8" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M10.0142 7V7.85714" stroke="#F2F2F2" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M10.0142 12.1429V13" stroke="#F2F2F2" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M8.72838 12.1428H10.6569C10.9411 12.1428 11.2136 12.0299 11.4146 11.829C11.6155 11.6281 11.7284 11.3556 11.7284 11.0714C11.7284 10.7872 11.6155 10.5147 11.4146 10.3138C11.2136 10.1129 10.9411 9.99997 10.6569 9.99997H9.37123C9.08707 9.99997 8.81455 9.88709 8.61362 9.68616C8.41269 9.48523 8.2998 9.21271 8.2998 8.92855C8.2998 8.64438 8.41269 8.37186 8.61362 8.17093C8.81455 7.97 9.08707 7.85712 9.37123 7.85712H11.2998" stroke="white" strokeLinecap="round" strokeLinejoin="round"/>
  </svg>
)

export const GiftCardIcon = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M4.3746 7.78642C4.37452 8.26143 4.37484 8.73643 4.37464 9.21143C4.37449 9.5432 4.64328 9.81246 4.97505 9.81246H15.0248C15.356 9.81246 15.6246 9.54417 15.6247 9.21301C15.6249 8.7377 15.6249 8.26239 15.6247 7.78707C15.6246 7.45579 15.3558 7.18743 15.0246 7.18752C11.6742 7.18842 8.32389 7.18429 4.97356 7.18723C4.64274 7.18752 4.37465 7.45561 4.3746 7.78642Z" fill="#BAB9B8" stroke="#BAB9B8" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M14.6875 10C14.6875 11.375 14.6875 12.75 14.6875 14.125C14.6875 14.9534 14.0159 15.625 13.1875 15.625C11.0625 15.625 8.93749 15.625 6.81248 15.625C5.98406 15.625 5.3125 14.9534 5.31249 14.125C5.31249 12.75 5.3125 11.375 5.3125 10" fill="#BAB9B8"/>
    <path d="M14.6875 10C14.6875 11.375 14.6875 12.75 14.6875 14.125C14.6875 14.9534 14.0159 15.625 13.1875 15.625C11.0625 15.625 8.93749 15.625 6.81248 15.625C5.98406 15.625 5.3125 14.9534 5.31249 14.125C5.31249 12.75 5.3125 11.375 5.3125 10" stroke="#BAB9B8" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M12.8406 6.69573C12.1305 7.40588 10 7.40588 10 7.40588C10 7.40588 10 5.27542 10.7102 4.56526C10.9927 4.28301 11.3758 4.12452 11.7752 4.12463C12.1746 4.12475 12.5576 4.28346 12.84 4.56587C13.1224 4.84828 13.2811 5.23128 13.2812 5.63067C13.2814 6.03006 13.1229 6.41315 12.8406 6.69573V6.69573Z" stroke="#BAB9B8" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M7.15938 6.69573C7.86953 7.40588 10 7.40588 10 7.40588C10 7.40588 10 5.27542 9.28984 4.56526C9.00727 4.28301 8.62418 4.12452 8.22479 4.12463C7.8254 4.12475 7.4424 4.28346 7.15998 4.56587C6.87757 4.84828 6.71886 5.23128 6.71875 5.63067C6.71864 6.03006 6.87713 6.41315 7.15938 6.69573V6.69573Z" stroke="#BAB9B8" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M10 7.1875V15.625" stroke="#F2F2F2" strokeLinecap="square"/>
    <path d="M15.2761 10.7498H4.96362" stroke="#F2F2F2" strokeLinecap="square"/>
  </svg>
)

export const TeamIcon = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect x="2.5" y="4" width="15" height="12" rx="2.5" fill="#BAB9B8"/>
    <path fillRule="evenodd" clipRule="evenodd" d="M7.37872 10.0636C8.43182 10.0636 9.28552 9.20989 9.28552 8.1568C9.28552 7.1037 8.43182 6.25 7.37872 6.25C6.32563 6.25 5.47192 7.1037 5.47192 8.1568C5.47192 9.20989 6.32563 10.0636 7.37872 10.0636Z" fill="#F2F2F2"/>
    <path d="M7.41616 13.7501C8.50396 13.7501 9.50059 13.3597 10.2736 12.7114C10.4827 12.536 10.4782 12.2188 10.2646 12.0488C9.48301 11.4265 8.49313 11.0547 7.41641 11.0547C6.33956 11.0547 5.34957 11.4266 4.56792 12.049C4.35438 12.2191 4.34986 12.5362 4.55902 12.7116C5.33196 13.3598 6.32848 13.7501 7.41616 13.7501Z" fill="#F2F2F2"/>
    <path d="M12 10.8037H15" stroke="#F2F2F2" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M12 8.55371H15" stroke="#F2F2F2" strokeLinecap="round" strokeLinejoin="round"/>
  </svg>
)

export const TeamIcon24 = () => (
  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect x="3" y="4.7998" width="18" height="14.4" rx="2.5" fill="#BAB9B8"/>
    <path fillRule="evenodd" clipRule="evenodd" d="M8.85432 12.0763C10.118 12.0763 11.1425 11.0519 11.1425 9.78816C11.1425 8.52444 10.118 7.5 8.85432 7.5C7.59061 7.5 6.56616 8.52444 6.56616 9.78816C6.56616 11.0519 7.59061 12.0763 8.85432 12.0763Z" fill="#EFEFEE"/>
    <path d="M8.89944 16.5001C10.2402 16.5001 11.4655 16.0059 12.4031 15.1897C12.609 15.0105 12.6045 14.6944 12.394 14.5206C11.4446 13.7366 10.2272 13.2656 8.89974 13.2656C7.57217 13.2656 6.35462 13.7367 5.40514 14.5208C5.19467 14.6946 5.19016 15.0108 5.39607 15.19C6.33368 16.006 7.55886 16.5001 8.89944 16.5001Z" fill="#EFEFEE"/>
    <path d="M14.4001 12.9644H18.0001" stroke="#EFEFEE" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M14.4001 10.2646H18.0001" stroke="#EFEFEE" strokeLinecap="round" strokeLinejoin="round"/>
  </svg>

)

export const BoxIcon = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <mask id="path-1-inside-1_438_4199" fill="white">
      <path fillRule="evenodd" clipRule="evenodd" d="M3.52407 8.70719C3.52452 9.95763 3.52497 11.2081 3.52428 12.4585C3.52387 13.1994 3.93373 13.878 4.57907 14.242C5.1889 14.586 5.79348 14.9408 6.39807 15.2957L6.39809 15.2957L6.39811 15.2957C7.29415 15.8217 8.1902 16.3476 9.10331 16.838C9.22892 16.9055 9.36021 16.9577 9.49463 16.9946L9.56384 10.2921L3.60325 6.96152C3.55109 7.14072 3.52363 7.32867 3.52368 7.52057L3.52407 8.70719ZM10.4947 16.9945L10.5639 10.2915L13.6745 8.51284C13.6889 8.5055 13.703 8.49744 13.7167 8.48868L16.3863 6.96223C16.4352 7.13624 16.461 7.31832 16.461 7.5041V12.4967C16.461 13.2185 16.0716 13.8837 15.4466 14.2448C15.0934 14.4489 14.7405 14.6546 14.3874 14.8605L14.3873 14.8606L14.3871 14.8607L14.387 14.8607C13.2273 15.5367 12.0654 16.2139 10.8826 16.8407C10.758 16.9067 10.6278 16.958 10.4947 16.9945ZM13.4439 7.49275L15.8833 6.09791C15.7547 5.96786 15.6072 5.85442 15.4435 5.76208L10.9774 3.24314C10.3661 2.89838 9.61888 2.89923 9.00839 3.24539L7.43712 4.13635L13.4439 7.49275ZM6.4196 4.71331L4.53701 5.7808C4.3803 5.86965 4.23857 5.97787 4.11414 6.10147L10.0634 9.42575L12.4307 8.07212L6.67985 4.85873L6.4196 4.71331Z"/>
    </mask>
    <path fillRule="evenodd" clipRule="evenodd" d="M3.52407 8.70719C3.52452 9.95763 3.52497 11.2081 3.52428 12.4585C3.52387 13.1994 3.93373 13.878 4.57907 14.242C5.1889 14.586 5.79348 14.9408 6.39807 15.2957L6.39809 15.2957L6.39811 15.2957C7.29415 15.8217 8.1902 16.3476 9.10331 16.838C9.22892 16.9055 9.36021 16.9577 9.49463 16.9946L9.56384 10.2921L3.60325 6.96152C3.55109 7.14072 3.52363 7.32867 3.52368 7.52057L3.52407 8.70719ZM10.4947 16.9945L10.5639 10.2915L13.6745 8.51284C13.6889 8.5055 13.703 8.49744 13.7167 8.48868L16.3863 6.96223C16.4352 7.13624 16.461 7.31832 16.461 7.5041V12.4967C16.461 13.2185 16.0716 13.8837 15.4466 14.2448C15.0934 14.4489 14.7405 14.6546 14.3874 14.8605L14.3873 14.8606L14.3871 14.8607L14.387 14.8607C13.2273 15.5367 12.0654 16.2139 10.8826 16.8407C10.758 16.9067 10.6278 16.958 10.4947 16.9945ZM13.4439 7.49275L15.8833 6.09791C15.7547 5.96786 15.6072 5.85442 15.4435 5.76208L10.9774 3.24314C10.3661 2.89838 9.61888 2.89923 9.00839 3.24539L7.43712 4.13635L13.4439 7.49275ZM6.4196 4.71331L4.53701 5.7808C4.3803 5.86965 4.23857 5.97787 4.11414 6.10147L10.0634 9.42575L12.4307 8.07212L6.67985 4.85873L6.4196 4.71331Z" fill="#BAB9B8"/>
    <path d="M3.52428 12.4585L4.52428 12.459L3.52428 12.4585ZM3.52407 8.70719L2.52407 8.70752L2.52407 8.70755L3.52407 8.70719ZM4.57907 14.242L4.0878 15.113H4.0878L4.57907 14.242ZM6.39807 15.2957L5.89187 16.1581C5.90199 16.164 5.91222 16.1698 5.92255 16.1754L6.39807 15.2957ZM6.39809 15.2957L6.93715 14.4534C6.91643 14.4402 6.89524 14.4277 6.87361 14.416L6.39809 15.2957ZM6.39811 15.2957L5.85906 16.138C5.86988 16.1449 5.88083 16.1516 5.89191 16.1581L6.39811 15.2957ZM9.10331 16.838L8.63016 17.719L8.63016 17.719L9.10331 16.838ZM9.49463 16.9946L9.22952 17.9588C9.52864 18.0411 9.84899 17.9798 10.0967 17.7931C10.3444 17.6063 10.4914 17.3151 10.4946 17.0049L9.49463 16.9946ZM9.56384 10.2921L10.5638 10.3025C10.5676 9.93648 10.3711 9.59769 10.0516 9.41916L9.56384 10.2921ZM3.60325 6.96152L4.09103 6.08856C3.82507 5.93995 3.50588 5.92068 3.22398 6.03623C2.94208 6.15178 2.72823 6.38953 2.64309 6.68205L3.60325 6.96152ZM3.52368 7.52057L2.52368 7.52084V7.5209L3.52368 7.52057ZM10.5639 10.2915L10.0675 9.4234C9.75928 9.59965 9.56762 9.92613 9.56395 10.2812L10.5639 10.2915ZM10.4947 16.9945L9.49473 16.9842C9.49149 17.2979 9.63565 17.595 9.8841 17.7865C10.1326 17.978 10.4565 18.0419 10.759 17.959L10.4947 16.9945ZM13.6745 8.51284L13.2209 7.62162C13.2065 7.62898 13.1922 7.63669 13.1781 7.64474L13.6745 8.51284ZM13.7167 8.48868L13.2203 7.62058C13.2062 7.62865 13.1923 7.63706 13.1786 7.6458L13.7167 8.48868ZM16.3863 6.96223L17.3488 6.69123C17.2654 6.39482 17.05 6.15338 16.765 6.03674C16.48 5.9201 16.1572 5.94127 15.8899 6.09413L16.3863 6.96223ZM15.4466 14.2448L15.947 15.1107L15.947 15.1107L15.4466 14.2448ZM14.3874 14.8605L14.888 15.7262L14.891 15.7244L14.3874 14.8605ZM14.3871 14.8607L13.8866 13.9949C13.8786 13.9995 13.8708 14.0042 13.863 14.009L14.3871 14.8607ZM14.387 14.8607L14.8906 15.7246C14.8975 15.7206 14.9043 15.7165 14.9111 15.7123L14.387 14.8607ZM10.8826 16.8407L10.4144 15.9571H10.4144L10.8826 16.8407ZM13.4439 7.49275L12.9561 8.36571C13.2623 8.53681 13.6358 8.53497 13.9403 8.36085L13.4439 7.49275ZM15.8833 6.09791L16.3796 6.96601C16.6493 6.81182 16.832 6.5411 16.874 6.23335C16.9161 5.9256 16.8128 5.61577 16.5944 5.39488L15.8833 6.09791ZM15.4435 5.76208L14.9523 6.63309L14.9523 6.63309L15.4435 5.76208ZM10.9774 3.24314L10.4862 4.11416L10.9774 3.24314ZM9.00839 3.24539L8.51514 2.37551V2.37551L9.00839 3.24539ZM7.43712 4.13635L6.94387 3.26646C6.6297 3.44461 6.43599 3.77832 6.43713 4.13949C6.43826 4.50065 6.63405 4.83314 6.94934 5.00931L7.43712 4.13635ZM6.4196 4.71331L6.90739 3.84035C6.6023 3.66988 6.23036 3.67104 5.92635 3.84343L6.4196 4.71331ZM4.53701 5.7808L4.04376 4.91091H4.04376L4.53701 5.7808ZM4.11414 6.10147L3.40942 5.39198C3.18667 5.61322 3.08076 5.92624 3.1234 6.23728C3.16604 6.54832 3.35228 6.82129 3.62635 6.97443L4.11414 6.10147ZM10.0634 9.42575L9.57562 10.2987C9.88183 10.4698 10.2553 10.468 10.5598 10.2938L10.0634 9.42575ZM12.4307 8.07212L12.9271 8.94022C13.24 8.76126 13.4325 8.42769 13.4307 8.06718C13.4289 7.70666 13.2332 7.37501 12.9185 7.19915L12.4307 8.07212ZM6.67985 4.85873L6.19207 5.7317L6.67985 4.85873ZM4.52428 12.459C4.52497 11.2081 4.52452 9.95719 4.52407 8.70683L2.52407 8.70755C2.52452 9.95806 2.52497 11.208 2.52428 12.4579L4.52428 12.459ZM5.07033 13.371C4.72826 13.1781 4.52407 12.8262 4.52428 12.459L2.52428 12.4579C2.52366 13.5726 3.13921 14.578 4.0878 15.113L5.07033 13.371ZM6.90427 14.4333C6.30093 14.0791 5.68892 13.7199 5.07033 13.371L4.0878 15.113C4.68888 15.452 5.28604 15.8025 5.89187 16.1581L6.90427 14.4333ZM6.87361 14.416L6.87358 14.416L5.92255 16.1754L5.92257 16.1754L6.87361 14.416ZM6.93716 14.4534L6.93715 14.4534L5.85904 16.138L5.85906 16.138L6.93716 14.4534ZM9.57647 15.957C8.68284 15.4771 7.80311 14.9609 6.90431 14.4333L5.89191 16.1581C6.7852 16.6825 7.69755 17.2181 8.63016 17.719L9.57647 15.957ZM9.75973 16.0304C9.69692 16.0131 9.63548 15.9887 9.57647 15.957L8.63016 17.719C8.82237 17.8222 9.0235 17.9022 9.22952 17.9588L9.75973 16.0304ZM8.56389 10.2818L8.49468 16.9843L10.4946 17.0049L10.5638 10.3025L8.56389 10.2818ZM3.11546 7.83448L9.07605 11.1651L10.0516 9.41916L4.09103 6.08856L3.11546 7.83448ZM4.52368 7.5203C4.52366 7.42415 4.53739 7.33035 4.5634 7.24099L2.64309 6.68205C2.56478 6.95109 2.5236 7.23319 2.52368 7.52084L4.52368 7.5203ZM4.52407 8.70686L4.52368 7.52024L2.52368 7.5209L2.52407 8.70752L4.52407 8.70686ZM9.56395 10.2812L9.49473 16.9842L11.4946 17.0049L11.5638 10.3018L9.56395 10.2812ZM13.1781 7.64474L10.0675 9.4234L11.0603 11.1596L14.1709 9.38094L13.1781 7.64474ZM13.1786 7.6458C13.1924 7.637 13.2065 7.62894 13.2209 7.62162L14.1281 9.40407C14.1713 9.38205 14.2136 9.35788 14.2548 9.33157L13.1786 7.6458ZM15.8899 6.09413L13.2203 7.62058L14.2131 9.35679L16.8826 7.83033L15.8899 6.09413ZM17.461 7.5041C17.461 7.22568 17.4224 6.95245 17.3488 6.69123L15.4237 7.23323C15.4481 7.32003 15.461 7.41095 15.461 7.5041H17.461ZM17.461 12.4967V7.5041H15.461V12.4967H17.461ZM15.947 15.1107C16.8766 14.5735 17.461 13.5809 17.461 12.4967H15.461C15.461 12.8561 15.2667 13.1939 14.9463 13.379L15.947 15.1107ZM14.891 15.7244C15.2444 15.5184 15.5957 15.3137 15.947 15.1107L14.9463 13.379C14.5912 13.5842 14.2367 13.7909 13.8839 13.9965L14.891 15.7244ZM14.8878 15.7263L14.8879 15.7262L13.8869 13.9947L13.8868 13.9948L14.8878 15.7263ZM14.8876 15.7264L14.8878 15.7263L13.8868 13.9948L13.8866 13.9949L14.8876 15.7264ZM14.9111 15.7123L14.9112 15.7123L13.863 14.009L13.8629 14.009L14.9111 15.7123ZM11.3508 17.7243C12.5545 17.0865 13.7339 16.3989 14.8906 15.7246L13.8835 13.9967C12.7208 14.6744 11.5763 15.3414 10.4144 15.9571L11.3508 17.7243ZM10.759 17.959C10.9624 17.9032 11.161 17.8249 11.3508 17.7243L10.4144 15.9571C10.355 15.9886 10.2932 16.0129 10.2303 16.0301L10.759 17.959ZM13.9403 8.36085L16.3796 6.96601L15.3869 5.22981L12.9475 6.62465L13.9403 8.36085ZM16.5944 5.39488C16.4014 5.19966 16.1801 5.02945 15.9348 4.89107L14.9523 6.63309C15.0344 6.67939 15.108 6.73606 15.1721 6.80094L16.5944 5.39488ZM15.9348 4.89107L11.4687 2.37213L10.4862 4.11416L14.9523 6.63309L15.9348 4.89107ZM11.4687 2.37213C10.5518 1.85498 9.43087 1.85626 8.51514 2.37551L9.50164 4.11528C9.80689 3.9422 10.1805 3.94177 10.4862 4.11416L11.4687 2.37213ZM8.51514 2.37551L6.94387 3.26646L7.93037 5.00624L9.50164 4.11528L8.51514 2.37551ZM6.94934 5.00931L12.9561 8.36571L13.9317 6.61979L7.92491 3.26339L6.94934 5.00931ZM5.92635 3.84343L4.04376 4.91091L5.03026 6.65068L6.91285 5.5832L5.92635 3.84343ZM4.04376 4.91091C3.80892 5.04407 3.59623 5.20643 3.40942 5.39198L4.81885 6.81096C4.88092 6.74931 4.95168 6.69524 5.03026 6.65068L4.04376 4.91091ZM3.62635 6.97443L9.57562 10.2987L10.5512 8.55278L4.60192 5.2285L3.62635 6.97443ZM10.5598 10.2938L12.9271 8.94022L11.9343 7.20402L9.56702 8.55765L10.5598 10.2938ZM12.9185 7.19915L7.16764 3.98577L6.19207 5.7317L11.9429 8.94508L12.9185 7.19915ZM7.16764 3.98577L6.90739 3.84035L5.93182 5.58628L6.19207 5.7317L7.16764 3.98577Z" fill="#BAB9B8" mask="url(#path-1-inside-1_438_4199)"/>
  </svg>
)

export const DownloadIcon = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M6.5005 16C5.67207 16 5.00049 15.3284 5.0005 14.5L5.00057 5.49986C5.00058 4.67141 5.67159 3.99984 6.50003 3.99987C7.94594 3.99992 10.0613 3.99999 11.0861 4.00001C11.3513 4.00001 11.6055 4.10537 11.793 4.29291L14.7072 7.20712C14.8948 7.39466 15.0001 7.64724 15.0001 7.91246C15.0001 9.60944 15.0001 12.67 15.0001 14.5005C15.0001 15.3289 14.3285 16 13.5001 16H6.5005Z" fill="#BAB9B8" stroke="#BAB9B8" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M7.87012 10.6289L10.0014 12.7502L12.1298 10.6289" stroke="#F9F9F9" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M10.001 7.75V12.75" stroke="#F9F9F9" strokeLinecap="round" strokeLinejoin="round"/>
  </svg>

)

export const CalendarBlankIcon = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M13.0004 4.5H6.00379C4.89774 4.5 4.00176 5.39774 4.00257 6.50379C4.00429 8.83585 4.00204 11.1679 4.00095 13.5C4.00044 14.6046 4.89585 15.5 6.00042 15.5H13.0004C14.105 15.5 15.0004 14.6046 15.0004 13.5V6.5C15.0004 5.39543 14.105 4.5 13.0004 4.5Z" stroke="#BAB9B8" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M12.0005 3.5V5.5" stroke="#BAB9B8" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M7.00049 3.5V5.5" stroke="#BAB9B8" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M15.0005 8H4.00049V13.5C4.00049 14.6046 4.89592 15.5 6.00049 15.5H13.0005C14.1051 15.5 15.0005 14.6046 15.0005 13.5V8Z" fill="#BAB9B8" stroke="#BAB9B8" strokeLinecap="square" strokeLinejoin="round"/>
  </svg>

)

export const ArrowLeftIcon = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd" d="M10 17C11.3845 17 12.7378 16.5895 13.889 15.8203C15.0401 15.0511 15.9373 13.9579 16.4672 12.6788C16.997 11.3997 17.1356 9.99224 16.8655 8.63437C16.5954 7.2765 15.9287 6.02922 14.9497 5.05025C13.9708 4.07129 12.7235 3.4046 11.3656 3.13451C10.0078 2.86441 8.6003 3.00303 7.32122 3.53284C6.04213 4.06266 4.94888 4.95987 4.17971 6.11101C3.41054 7.26215 3 8.61553 3 10C3.00214 11.8559 3.74033 13.6351 5.05262 14.9474C6.36491 16.2597 8.14414 16.9979 10 17ZM9.98375 7.51568C10.1795 7.71048 10.1802 8.02707 9.98541 8.22279L8.71327 9.50093L12.5099 9.50093C12.7861 9.50093 13.0099 9.72478 13.0099 10.0009C13.0099 10.2771 12.7861 10.5009 12.5099 10.5009L8.71355 10.5009L9.98517 11.7767C10.1801 11.9723 10.1796 12.2889 9.984 12.4839C9.78841 12.6788 9.47183 12.6783 9.27689 12.4827L7.15558 10.3543C6.99213 10.1904 6.96604 9.94122 7.07734 9.75004C7.10117 9.70903 7.13067 9.67172 7.16476 9.63918L9.27664 7.51734C9.47145 7.32162 9.78803 7.32088 9.98375 7.51568Z" fill="#BAB9B8"/>
  </svg>

)

export const BadgeIconGreen16 = () => (
  <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M10.4001 9.59961V12.7909C10.4001 13.1626 10.009 13.4043 9.67652 13.2381L8.22336 12.5116C8.08259 12.4413 7.91691 12.4413 7.77615 12.5117L6.32373 13.238C5.99128 13.4042 5.6001 13.1625 5.6001 12.7908V9.59996" fill="#4D9985"/>
    <path d="M10.4001 9.59961V12.7909C10.4001 13.1626 10.009 13.4043 9.67652 13.2381L8.22336 12.5116C8.08259 12.4413 7.91691 12.4413 7.77615 12.5117L6.32373 13.238C5.99128 13.4042 5.6001 13.1625 5.6001 12.7908V9.59996" stroke="#4D9985" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M7.99976 10.3999C10.2089 10.3999 11.9998 8.60904 11.9998 6.3999C11.9998 4.19076 10.2089 2.3999 7.99976 2.3999C5.79062 2.3999 3.99976 4.19076 3.99976 6.3999C3.99976 8.60904 5.79062 10.3999 7.99976 10.3999Z" fill="#E1EAE8" stroke="#4D9985" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M9.39985 6.4C9.39985 7.1732 8.77305 7.8 7.99985 7.8C7.22665 7.8 6.59985 7.1732 6.59985 6.4C6.59985 5.6268 7.22665 5 7.99985 5C8.77305 5 9.39985 5.6268 9.39985 6.4Z" fill="#4D9985" stroke="#4D9985" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
  </svg>

)

export const NoItemsIllustration = () => (
  <svg width="81" height="80" viewBox="0 0 81 80" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M3.69052 21.7142H40.8334L35.5273 73.1428H8.99664L3.69052 21.7142Z" fill="#BAB9B8"/>
    <path d="M43.6905 21.7142L0.833378 21.7142L4.09868 15.9999L40.4252 16L43.6905 21.7142Z" fill="#929191"/>
    <rect x="76.5477" y="20.2858" width="4.28571" height="41.4286" transform="rotate(30 76.5477 20.2858)" fill="#C4C4C4"/>
    <path d="M5.11908 34.5714H39.4048L37.2619 57.4286H7.26194L5.11908 34.5714Z" fill="#D8D7D6"/>
    <path d="M52.2619 73.1428C68.0415 73.1428 80.8334 60.351 80.8334 44.5714H23.6905C23.6905 60.351 36.4824 73.1428 52.2619 73.1428Z" fill="#929191"/>
  </svg>

)

export const NoPendingItemsIllustration = () => (
  <svg width="81" height="58" viewBox="0 0 81 58" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M3.69049 5.71436L40.8333 5.71436L35.5272 57.1429H8.99661L3.69049 5.71436Z" fill="#BAB9B8"/>
    <path d="M43.6905 5.71436L0.833347 5.71435L4.09865 6.99717e-05L40.4252 7.31475e-05L43.6905 5.71436Z" fill="#929191"/>
    <rect x="76.5477" y="4.28564" width="4.28571" height="41.4286" transform="rotate(30 76.5477 4.28564)" fill="#C4C4C4"/>
    <path d="M5.11908 18.5713H39.4048L37.2619 41.4284H7.26194L5.11908 18.5713Z" fill="#D8D7D6"/>
    <path d="M52.2619 57.1427C68.0415 57.1427 80.8333 44.3509 80.8333 28.5713H23.6905C23.6905 44.3509 36.4824 57.1427 52.2619 57.1427Z" fill="#929191"/>
  </svg>

)

export const AlloIllustration = () => (
  <svg width="79" height="32" viewBox="0 0 79 32" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M28.7969 31.4005L33.7635 31.4005L33.7635 0.596923L28.7969 0.596924L28.7969 31.4005Z" fill="#F9F9F9"/>
    <path fillRule="evenodd" clipRule="evenodd" d="M62.873 32C71.7096 32 78.873 24.8366 78.873 16C78.873 7.16344 71.7096 0 62.873 0C54.0365 0 46.873 7.16344 46.873 16C46.873 24.8366 54.0365 32 62.873 32ZM62.8725 27.0339C68.9661 27.0339 73.9059 22.0941 73.9059 16.0005C73.9059 9.90699 68.9661 4.96719 62.8725 4.96719C56.779 4.96719 51.8392 9.90699 51.8392 16.0005C51.8392 22.0941 56.779 27.0339 62.8725 27.0339Z" fill="#F9F9F9"/>
    <path d="M38.1357 31.3547H43.1025V0.596924C40.3594 0.596924 38.1357 2.8206 38.1357 5.56364V31.3547Z" fill="#F9F9F9"/>
    <path fillRule="evenodd" clipRule="evenodd" d="M19.4696 10.2786C17.557 8.67721 15.1879 7.57227 12.2143 7.57227C5.46851 7.57227 0 13.0408 0 19.7865C0 26.5323 5.46851 32.0008 12.2143 32.0008C15.1872 32.0008 17.557 30.9055 19.4696 29.3043L19.6148 31.4037H24.4287L24.4287 8.17075L19.6165 8.17075L19.4696 10.2786ZM19.4689 19.787C19.4689 23.7939 16.2207 27.0421 12.2138 27.0421C8.20698 27.0421 4.95877 23.7939 4.95877 19.787C4.95877 15.7801 8.20698 12.5319 12.2138 12.5319C16.2207 12.5319 19.4689 15.7801 19.4689 19.787Z" fill="#F9F9F9"/>
  </svg>

)

export const AuthenticationFullIllustration = () => (
  <svg viewBox="0 0 635 834" fill="none" xmlns="http://www.w3.org/2000/svg">
    <g clipPath="url(#clip0_1_4753)">
      <mask id="mask0_1_4753" style={{ maskType: "alpha" }} maskUnits="userSpaceOnUse" x="0" y="0" width="635" height="834">
        <rect width="635" height="834" fill="#ADD1F6"/>
      </mask>
      <g mask="url(#mask0_1_4753)">
        <rect width="635" height="834" fill="#C0DAD3"/>
      </g>
      <rect x="129" y="191" width="376" height="452" rx="51" fill="#2B574B"/>
      <path d="M279 0H355L345 191H289L279 0Z" fill="#2B574B"/>
      <circle cx="317" cy="417" r="226" fill="#FFEABB"/>
      <circle cx="317" cy="417" r="209" fill="#FFDB88"/>
      <circle cx="349" cy="514" r="42" fill="white"/>
      <path d="M338 538H353L355 592H336L338 538Z" fill="#4D9985"/>
      <circle cx="427.5" cy="387.5" r="29.5" fill="#994B37"/>
      <path d="M229.733 470.691L237.739 483.376L193.141 513.889L183.001 497.821L229.733 470.691Z" fill="#4D9985"/>
      <path d="M305.675 269.359L293.041 277.446L266.494 239.679L282.497 229.437L305.675 269.359Z" fill="#4D9985"/>
      <path d="M202.181 367.923L189.547 376.009L163 338.243L179.003 328L202.181 367.923Z" fill="#4D9985"/>
      <path d="M435.667 398.389L448.173 390.105L479.661 434.019L463.822 444.512L435.667 398.389Z" fill="#4D9985"/>
      <path d="M461.942 288.33L468.421 301.859L420.582 326.987L412.375 309.85L461.942 288.33Z" fill="#4D9985"/>
      <circle cx="364.5" cy="278.5" r="29.5" fill="#994B37"/>
      <path d="M367.709 417.846L382.236 421.58L374.819 458.472L356.418 453.742L367.709 417.846Z" fill="#4D9985"/>
      <circle cx="355" cy="335" r="42" fill="white"/>
      <circle cx="224" cy="445" r="42" fill="white"/>
      <path d="M282.701 351.921L287.96 337.873L331.853 352.168L325.193 369.962L282.701 351.921Z" fill="#4D9985"/>
      <circle cx="423.5" cy="520.5" r="29.5" fill="#994B37"/>
      <circle cx="176.5" cy="424.5" r="29.5" fill="#994B37"/>
      <circle cx="259.5" cy="541.5" r="29.5" fill="#994B37"/>
      <circle cx="305.5" cy="435.5" r="29.5" fill="#994B37"/>
      <circle cx="235.5" cy="307.5" r="29.5" fill="#994B37"/>
      <rect x="635.068" y="487" width="151.987" height="222.135" transform="rotate(30 635.068 487)" fill="white"/>
      <circle cy="184" r="62" fill="#2B574B"/>
      <circle cx="617" r="137" fill="#4D9985"/>
    </g>
    <defs>
      <clipPath id="clip0_1_4753">
        <rect width="635" height="834" fill="white"/>
      </clipPath>
    </defs>
  </svg>

)

export const AuthenticationThreeIllustration = () => (
  <svg viewBox="0 0 635 834" fill="none" xmlns="http://www.w3.org/2000/svg">
    <g clipPath="url(#clip0_1_4790)">
      <mask id="mask0_1_4790" style={{ maskType: "alpha" }} maskUnits="userSpaceOnUse" x="0" y="0" width="635" height="834">
        <rect width="635" height="834" fill="#ADD1F6"/>
      </mask>
      <g mask="url(#mask0_1_4790)">
        <rect width="635" height="834" fill="#C0DAD3"/>
      </g>
      <rect x="129" y="191" width="376" height="452" rx="51" fill="#366B5D"/>
      <path d="M279 0H355L345 191H289L279 0Z" fill="#366B5D"/>
      <mask id="mask1_1_4790" style={{ maskType: "alpha" }} maskUnits="userSpaceOnUse" x="90" y="191" width="453" height="452">
        <path d="M543 417C543 461.699 529.745 505.393 504.912 542.559C480.079 579.724 444.783 608.691 403.486 625.797C362.19 642.902 316.749 647.378 272.91 638.657C229.07 629.937 188.801 608.413 157.194 576.806C125.587 545.199 104.063 504.93 95.3425 461.09C86.6223 417.251 91.0978 371.81 108.203 330.514C125.309 289.217 154.276 253.921 191.441 229.088C228.607 204.255 272.301 191 317 191L317 417H543Z" fill="#FFEABB"/>
      </mask>
      <g mask="url(#mask1_1_4790)">
        <circle cx="317" cy="417" r="226" fill="#FFEABB"/>
        <circle cx="317" cy="417" r="209" fill="#FFDB88"/>
        <circle cx="349" cy="514" r="42" fill="white"/>
        <path d="M338 538H353L355 592H336L338 538Z" fill="#4D9985"/>
        <circle cx="427.5" cy="387.5" r="29.5" fill="#994B37"/>
        <path d="M229.733 470.691L237.739 483.376L193.141 513.889L183.001 497.821L229.733 470.691Z" fill="#4D9985"/>
        <path d="M305.675 269.359L293.041 277.446L266.494 239.679L282.497 229.437L305.675 269.359Z" fill="#4D9985"/>
        <path d="M202.181 367.923L189.547 376.009L163 338.243L179.003 328L202.181 367.923Z" fill="#4D9985"/>
        <path d="M435.667 398.389L448.173 390.105L479.661 434.019L463.822 444.512L435.667 398.389Z" fill="#4D9985"/>
        <path d="M461.942 288.33L468.421 301.859L420.582 326.987L412.375 309.85L461.942 288.33Z" fill="#4D9985"/>
        <circle cx="364.5" cy="278.5" r="29.5" fill="#994B37"/>
        <path d="M367.709 417.846L382.236 421.58L374.819 458.472L356.418 453.742L367.709 417.846Z" fill="#4D9985"/>
        <circle cx="355" cy="335" r="42" fill="white"/>
        <circle cx="224" cy="445" r="42" fill="white"/>
        <path d="M282.701 351.921L287.96 337.873L331.853 352.168L325.193 369.962L282.701 351.921Z" fill="#4D9985"/>
        <circle cx="423.5" cy="520.5" r="29.5" fill="#994B37"/>
        <circle cx="176.5" cy="424.5" r="29.5" fill="#994B37"/>
        <circle cx="259.5" cy="541.5" r="29.5" fill="#994B37"/>
        <circle cx="305.5" cy="435.5" r="29.5" fill="#994B37"/>
        <circle cx="235.5" cy="307.5" r="29.5" fill="#994B37"/>
      </g>
      <circle cx="429.5" cy="288.5" r="29.5" fill="#FFDB88"/>
      <circle cx="357" cy="310" r="8" fill="#FFDB88"/>
      <circle cx="414" cy="359" r="14" fill="#FFEABB"/>
      <rect x="635.068" y="487" width="151.987" height="222.135" transform="rotate(30 635.068 487)" fill="white"/>
      <circle cy="184" r="62" fill="#2B574B"/>
      <circle cx="617" r="137" fill="#4D9985"/>
    </g>
    <defs>
      <clipPath id="clip0_1_4790">
        <rect width="635" height="834" fill="white"/>
      </clipPath>
    </defs>
  </svg>

)

export const AuthenticationHalfIllustration = () => (
  <svg viewBox="0 0 635 834" fill="none" xmlns="http://www.w3.org/2000/svg">
    <g clipPath="url(#clip0_1_4832)">
      <mask id="mask0_1_4832" style={{ maskType: "alpha" }} maskUnits="userSpaceOnUse" x="0" y="0" width="635" height="834">
        <rect width="635" height="834" fill="#ADD1F6"/>
      </mask>
      <g mask="url(#mask0_1_4832)">
        <rect width="635" height="834" fill="#C0DAD3"/>
      </g>
      <rect x="129" y="191" width="376" height="452" rx="51" fill="#366B5D"/>
      <path d="M279 0H355L345 191H289L279 0Z" fill="#366B5D"/>
      <mask id="mask1_1_4832" style={{ maskType: "alpha" }} maskUnits="userSpaceOnUse" x="91" y="417" width="452" height="226">
        <path d="M543 417C543 446.679 537.154 476.067 525.797 503.486C514.439 530.906 497.792 555.82 476.806 576.806C455.82 597.792 430.906 614.439 403.486 625.797C376.067 637.154 346.679 643 317 643C287.321 643 257.933 637.154 230.514 625.797C203.094 614.439 178.18 597.792 157.194 576.806C136.208 555.82 119.561 530.906 108.203 503.486C96.8457 476.067 91 446.679 91 417L317 417H543Z" fill="#FFEABB"/>
      </mask>
      <g mask="url(#mask1_1_4832)">
        <circle cx="317" cy="417" r="226" fill="#FFEABB"/>
        <circle cx="317" cy="417" r="209" fill="#FFDB88"/>
        <circle cx="349" cy="514" r="42" fill="white"/>
        <path d="M338 538H353L355 592H336L338 538Z" fill="#4D9985"/>
        <path d="M229.733 470.691L237.739 483.376L193.141 513.889L183.001 497.821L229.733 470.691Z" fill="#4D9985"/>
        <path d="M435.667 398.389L448.173 390.105L479.661 434.019L463.822 444.512L435.667 398.389Z" fill="#4D9985"/>
        <path d="M367.709 417.846L382.236 421.58L374.819 458.472L356.418 453.742L367.709 417.846Z" fill="#4D9985"/>
        <circle cx="224" cy="445" r="42" fill="white"/>
        <circle cx="423.5" cy="520.5" r="29.5" fill="#994B37"/>
        <circle cx="176.5" cy="424.5" r="29.5" fill="#994B37"/>
        <circle cx="259.5" cy="541.5" r="29.5" fill="#994B37"/>
        <circle cx="305.5" cy="435.5" r="29.5" fill="#994B37"/>
      </g>
      <path d="M241.732 287.691L249.738 300.376L205.14 330.889L195 314.821L241.732 287.691Z" fill="#4D9985"/>
      <circle cx="429.5" cy="288.5" r="29.5" fill="#FFDB88"/>
      <circle cx="357" cy="310" r="8" fill="#FFDB88"/>
      <circle cx="259" cy="374" r="8" fill="#FFDB88"/>
      <circle cx="414" cy="359" r="14" fill="#FFEABB"/>
      <circle cx="232.5" cy="363.5" r="18.5" fill="#FFEABB"/>
      <rect x="635.068" y="487" width="151.987" height="222.135" transform="rotate(30 635.068 487)" fill="white"/>
      <circle cy="184" r="62" fill="#2B574B"/>
      <circle cx="617" r="137" fill="#4D9985"/>
    </g>
    <defs>
      <clipPath id="clip0_1_4832">
        <rect width="635" height="834" fill="white"/>
      </clipPath>
    </defs>
  </svg>

)

export const AuthenticationOneIllustration = () => (
  <svg viewBox="0 0 635 834" fill="none" xmlns="http://www.w3.org/2000/svg">
    <g clipPath="url(#clip0_1_4877)">
      <mask id="mask0_1_4877" style={{ maskType: "alpha" }} maskUnits="userSpaceOnUse" x="0" y="0" width="635" height="834">
        <rect width="635" height="834" fill="#ADD1F6"/>
      </mask>
      <g mask="url(#mask0_1_4877)">
        <rect width="635" height="834" fill="#C0DAD3"/>
      </g>
      <rect x="129" y="191" width="376" height="452" rx="51" fill="#366B5D"/>
      <path d="M279 0H355L345 191H289L279 0Z" fill="#366B5D"/>
      <mask id="mask1_1_4877" style={{ maskType: "alpha" }} maskUnits="userSpaceOnUse" x="317" y="417" width="226" height="226">
        <path d="M543 417C543 446.679 537.154 476.067 525.797 503.486C514.439 530.906 497.792 555.82 476.806 576.806C455.82 597.792 430.906 614.439 403.486 625.797C376.067 637.154 346.679 643 317 643L317 417H543Z" fill="#FFEABB"/>
      </mask>
      <g mask="url(#mask1_1_4877)">
        <circle cx="317" cy="417" r="226" fill="#FFEABB"/>
        <circle cx="317" cy="417" r="209" fill="#FFDB88"/>
        <circle cx="349" cy="514" r="42" fill="white"/>
        <path d="M338 538H353L355 592H336L338 538Z" fill="#4D9985"/>
        <path d="M435.667 398.389L448.173 390.105L479.661 434.019L463.822 444.512L435.667 398.389Z" fill="#4D9985"/>
        <path d="M367.709 417.846L382.236 421.58L374.819 458.472L356.418 453.742L367.709 417.846Z" fill="#4D9985"/>
        <circle cx="423.5" cy="520.5" r="29.5" fill="#994B37"/>
        <circle cx="305.5" cy="435.5" r="29.5" fill="#994B37"/>
      </g>
      <circle cx="429.5" cy="288.5" r="29.5" fill="#FFDB88"/>
      <circle cx="357" cy="310" r="8" fill="#FFDB88"/>
      <circle cx="414" cy="359" r="14" fill="#FFEABB"/>
      <path d="M241.732 287.691L249.738 300.376L205.14 330.889L195 314.821L241.732 287.691Z" fill="#4D9985"/>
      <path d="M213.849 533.258L228.663 530.904L236.524 567.618L217.759 570.601L213.849 533.258Z" fill="#4D9985"/>
      <circle cx="259" cy="374" r="8" fill="#FFDB88"/>
      <circle cx="232.5" cy="363.5" r="18.5" fill="#FFEABB"/>
      <circle cx="195.5" cy="475.5" r="18.5" fill="#FFEABB"/>
      <circle cx="267.5" cy="522.5" r="18.5" fill="#FFEABB"/>
      <rect x="635.068" y="487" width="151.987" height="222.135" transform="rotate(30 635.068 487)" fill="white"/>
      <circle cy="184" r="62" fill="#2B574B"/>
      <circle cx="617" r="137" fill="#4D9985"/>
    </g>
    <defs>
      <clipPath id="clip0_1_4877">
        <rect width="635" height="834" fill="white"/>
      </clipPath>
    </defs>
  </svg>

)

export const MoreOptionsIcon = () => (
  <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd" d="M8 4C8.55228 4 9 3.55228 9 3C9 2.44772 8.55228 2 8 2C7.44772 2 7 2.44772 7 3C7 3.55228 7.44772 4 8 4ZM8 9C8.55228 9 9 8.55228 9 8C9 7.44772 8.55228 7 8 7C7.44772 7 7 7.44772 7 8C7 8.55228 7.44772 9 8 9ZM9 13C9 13.5523 8.55228 14 8 14C7.44772 14 7 13.5523 7 13C7 12.4477 7.44772 12 8 12C8.55228 12 9 12.4477 9 13Z" fill="#333332"/>
  </svg>

)

export const MoreOptionsIcon20 = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd" d="M10 6C10.5523 6 11 5.55228 11 5C11 4.44772 10.5523 4 10 4C9.44772 4 9 4.44772 9 5C9 5.55228 9.44772 6 10 6ZM10 11C10.5523 11 11 10.5523 11 10C11 9.44772 10.5523 9 10 9C9.44772 9 9 9.44772 9 10C9 10.5523 9.44772 11 10 11ZM11 15C11 15.5523 10.5523 16 10 16C9.44772 16 9 15.5523 9 15C9 14.4477 9.44772 14 10 14C10.5523 14 11 14.4477 11 15Z" fill="#333332"/>
  </svg>
)

export const CloseDialogIcon = () => (
  <svg width="16" height="16" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M15.625 4.375L4.375 15.625" stroke="#333332" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M15.625 15.625L4.375 4.375" stroke="#333332" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
  </svg>

)

export const CloseDialogIcon20 = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M15.625 4.375L4.375 15.625" stroke="#333332" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M15.625 15.625L4.375 4.375" stroke="#333332" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
  </svg>

)

export const CollapseIcon = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd" d="M13.6112 15.4046C12.5423 16.1188 11.2856 16.5 10 16.5C8.2767 16.498 6.62456 15.8125 5.406 14.594C4.18745 13.3754 3.50199 11.7233 3.5 10C3.5 8.71442 3.88122 7.45771 4.59545 6.38879C5.30968 5.31987 6.32484 4.48675 7.51256 3.99478C8.70028 3.50281 10.0072 3.37409 11.2681 3.6249C12.529 3.8757 13.6872 4.49476 14.5962 5.40381C15.5052 6.31285 16.1243 7.47104 16.3751 8.73191C16.6259 9.99279 16.4972 11.2997 16.0052 12.4874C15.5132 13.6752 14.6801 14.6903 13.6112 15.4046ZM12.4589 11.1536C12.6542 11.3489 12.9708 11.3489 13.1661 11.1536C13.3613 10.9583 13.3613 10.6418 13.1661 10.4465L10.3536 7.63399C10.1583 7.43873 9.84171 7.43873 9.64645 7.63399L6.83395 10.4465C6.63868 10.6418 6.63868 10.9583 6.83395 11.1536C7.02921 11.3489 7.34579 11.3489 7.54105 11.1536L10 8.69465L12.4589 11.1536Z" fill="#929191"/>
  </svg>

)

export const UserIcon20 = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd" d="M3 10C3 6.13401 6.13401 3 10 3C13.866 3 17 6.13401 17 10C17 10.1203 16.997 10.2398 16.991 10.3586C16.9011 12.1404 16.145 13.7464 14.9667 14.9327C13.6987 16.2095 11.9417 17 10 17C8.05835 17 6.30134 16.2095 5.03327 14.9327C3.7765 13.6673 3 11.9243 3 10ZM5.66669 14.15C6.75885 15.2901 8.29652 16 10 16C11.7035 16 13.2411 15.2901 14.3333 14.15C13.2411 13.0099 11.7035 12.3 10 12.3C8.29652 12.3 6.75885 13.0099 5.66669 14.15ZM12.4497 8.5C12.4497 9.88071 11.3304 11 9.94973 11C8.56902 11 7.44973 9.88071 7.44973 8.5C7.44973 7.11929 8.56902 6 9.94973 6C11.3304 6 12.4497 7.11929 12.4497 8.5Z" fill="#929191"/>
  </svg>

)

export const ReceiptDarkIcon20 = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd" d="M14.4079 3.71802C13.9801 3.50003 13.4201 3.50003 12.3 3.50003L7.7121 3.5C6.59516 3.49999 6.03669 3.49999 5.60941 3.71728C5.23368 3.90837 4.92768 4.21359 4.73564 4.58883C4.51725 5.01555 4.51583 5.57372 4.51299 6.69007C4.50697 9.05144 4.50007 12.1945 4.50002 14.2036L4.5 15.9991C4.5 16.1778 4.59497 16.3429 4.74925 16.4324C4.90353 16.522 5.09375 16.5225 5.24849 16.4337L6.50747 15.7115C6.59441 15.6616 6.63788 15.6366 6.68406 15.6269C6.72492 15.6182 6.76713 15.6182 6.80799 15.6269C6.85417 15.6366 6.89764 15.6616 6.98458 15.7115L8.24356 16.4337C8.39749 16.5221 8.58661 16.5221 8.74054 16.4337L9.99952 15.7115C10.0865 15.6616 10.1299 15.6366 10.1761 15.6269C10.217 15.6182 10.2592 15.6182 10.3 15.6269C10.3462 15.6366 10.3897 15.6616 10.4766 15.7115L11.7356 16.4337C11.8895 16.5221 12.0787 16.5221 12.2326 16.4337L13.4048 15.7613C13.5063 15.703 13.557 15.6739 13.6103 15.6651C13.6574 15.6573 13.7056 15.6608 13.7511 15.6755C13.8025 15.692 13.8484 15.7282 13.9403 15.8007L14.6906 16.3926C14.8411 16.5113 15.046 16.5335 15.2183 16.4497C15.3906 16.3659 15.5 16.191 15.5 15.9991V13.9957V6.69916C15.5 5.57964 15.5 5.01988 15.282 4.59205C15.0902 4.21573 14.7843 3.90977 14.4079 3.71802ZM6.75 6.49998C6.75 6.22384 6.97386 5.99998 7.25 5.99998H12.75C13.0261 5.99998 13.25 6.22384 13.25 6.49998C13.25 6.77613 13.0261 6.99998 12.75 6.99998H7.25C6.97386 6.99998 6.75 6.77613 6.75 6.49998ZM6.75 8.99998C6.75 8.72384 6.97386 8.49998 7.25 8.49998H12.75C13.0261 8.49998 13.25 8.72384 13.25 8.99998C13.25 9.27612 13.0261 9.49998 12.75 9.49998H7.25C6.97386 9.49998 6.75 9.27612 6.75 8.99998Z" fill="#929191"/>
  </svg>

)

export const RevenueDarkIcon = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M10 17C13.866 17 17 13.866 17 10C17 6.13401 13.866 3 10 3C6.13401 3 3 6.13401 3 10C3 13.866 6.13401 17 10 17Z" fill="#929191"/>
    <path d="M10 6.5V7.5" stroke="#F2F2F2" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M10 12.5V13.5" stroke="#F2F2F2" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M8.5 12.5H10.75C11.0815 12.5 11.3995 12.3683 11.6339 12.1339C11.8683 11.8995 12 11.5815 12 11.25C12 10.9185 11.8683 10.6005 11.6339 10.3661C11.3995 10.1317 11.0815 10 10.75 10H9.25C8.91848 10 8.60054 9.8683 8.36612 9.63388C8.1317 9.39946 8 9.08152 8 8.75C8 8.41848 8.1317 8.10054 8.36612 7.86612C8.60054 7.6317 8.91848 7.5 9.25 7.5H11.5" stroke="#F2F2F2" strokeLinecap="round" strokeLinejoin="round"/>
  </svg>
)

export const SortIcon20 = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd" d="M6.11101 4.17971C7.26215 3.41054 8.61553 3 10 3C11.8559 3.00214 13.6351 3.74033 14.9474 5.05262C16.2597 6.36491 16.9979 8.14414 17 10C17 11.3845 16.5895 12.7378 15.8203 13.889C15.0511 15.0401 13.9579 15.9373 12.6788 16.4672C11.3997 16.997 9.99224 17.1356 8.63437 16.8655C7.2765 16.5954 6.02922 15.9287 5.05026 14.9497C4.07129 13.9708 3.4046 12.7235 3.13451 11.3656C2.86441 10.0078 3.00303 8.6003 3.53285 7.32122C4.06266 6.04213 4.95987 4.94888 6.11101 4.17971ZM12.2507 6.75C12.1226 6.74992 11.9945 6.79874 11.8968 6.89645L10.2968 8.49645C10.1016 8.69171 10.1016 9.00829 10.2968 9.20355C10.4921 9.39882 10.8087 9.39882 11.0039 9.20355L11.751 8.45652V12.75C11.751 13.0261 11.9748 13.25 12.251 13.25C12.5271 13.25 12.751 13.0261 12.751 12.75V8.45769L13.4968 9.20355C13.6921 9.39882 14.0087 9.39882 14.2039 9.20355C14.3992 9.00829 14.3992 8.69171 14.2039 8.49645L12.6187 6.91117C12.5273 6.81208 12.3964 6.75 12.251 6.75H12.2507ZM8.27344 7.25C8.27344 6.97386 8.04958 6.75 7.77344 6.75C7.49729 6.75 7.27344 6.97386 7.27344 7.25V11.1504H6.15039L7.27344 12.2734V12.75C7.27344 13.0261 7.4973 13.25 7.77344 13.25C8.04958 13.25 8.27344 13.0261 8.27344 12.75V12.2273L9.35039 11.1504H8.27344V7.25Z" fill="#BAB9B8"/>
  </svg>

)

export const FilterIcon20 = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M16.2448 5.09217C16.1681 4.91505 16.041 4.76444 15.8792 4.65913C15.7175 4.55382 15.5283 4.49849 15.3353 4.50003H4.65469C4.46201 4.50004 4.27349 4.55601 4.11202 4.66114C3.95056 4.76627 3.82311 4.91604 3.74517 5.09225C3.66723 5.26845 3.64214 5.46351 3.67297 5.6537C3.70379 5.84389 3.7892 6.02104 3.91881 6.1636L7.74589 10.3733C7.91323 10.5574 8.00596 10.7973 8.00596 11.046V15.5052C8.00583 15.6852 8.05461 15.8619 8.14709 16.0164C8.23956 16.1709 8.37226 16.2974 8.53101 16.3824C8.68976 16.4673 8.86861 16.5076 9.04845 16.4988C9.2283 16.4901 9.40238 16.4326 9.55213 16.3326L11.5411 15.0067C11.6775 14.916 11.7893 14.793 11.8666 14.6486C11.9439 14.5042 11.9842 14.3429 11.9841 14.1792V11.0461C11.9841 10.7973 12.0768 10.5575 12.2441 10.3734L16.0712 6.16354C16.2022 6.02176 16.2885 5.84453 16.3194 5.654C16.3502 5.46346 16.3243 5.26806 16.2448 5.09217Z" fill="#BAB9B8" stroke="#BAB9B8" strokeWidth="1.2"/>
  </svg>


)

export const PropertiesIcon20 = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M10 8.75C11.3807 8.75 12.5 7.63071 12.5 6.25C12.5 4.86929 11.3807 3.75 10 3.75C8.61929 3.75 7.5 4.86929 7.5 6.25C7.5 7.63071 8.61929 8.75 10 8.75Z" fill="#BAB9B8" stroke="#BAB9B8" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M13.75 15.25C15.1307 15.25 16.25 14.1307 16.25 12.75C16.25 11.3693 15.1307 10.25 13.75 10.25C12.3693 10.25 11.25 11.3693 11.25 12.75C11.25 14.1307 12.3693 15.25 13.75 15.25Z" fill="#BAB9B8" stroke="#BAB9B8" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M6.25 15.25C7.63071 15.25 8.75 14.1307 8.75 12.75C8.75 11.3693 7.63071 10.25 6.25 10.25C4.86929 10.25 3.75 11.3693 3.75 12.75C3.75 14.1307 4.86929 15.25 6.25 15.25Z" fill="#BAB9B8" stroke="#BAB9B8" strokeLinecap="round" strokeLinejoin="round"/>
  </svg>


)

export const FileIcon20 = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd" d="M6.40302 3.50005L10.3558 3.5C11.0326 3.49999 11.6805 3.7744 12.1514 4.2605L14.3891 6.5704C14.8409 7.03675 15.0935 7.66055 15.0935 8.30986V14.4835C15.0935 15.5972 14.1866 16.4944 13.0739 16.4851C11.3685 16.4709 8.56499 16.4549 6.46023 16.4802C5.33896 16.4937 4.40573 15.5925 4.40553 14.4616C4.40535 13.4488 4.40466 12.4357 4.40396 11.4224C4.4026 9.44718 4.40125 7.47134 4.40372 5.49596C4.40509 4.39258 5.30038 3.50002 6.40302 3.50005Z" fill="#BAB9B8"/>
    <path fillRule="evenodd" clipRule="evenodd" d="M12.5004 7.70013H13.2416C13.6051 7.70013 13.7868 7.70013 13.871 7.62825C13.944 7.56588 13.9827 7.47232 13.9752 7.37659C13.9665 7.26625 13.838 7.13774 13.581 6.88072L11.7198 5.01954C11.4628 4.76251 11.3343 4.634 11.2239 4.62532C11.1282 4.61778 11.0346 4.65653 10.9723 4.72956C10.9004 4.81372 10.9004 4.99546 10.9004 5.35895V6.10013C10.9004 6.66018 10.9004 6.94021 11.0094 7.15412C11.1053 7.34228 11.2582 7.49526 11.4464 7.59113C11.6603 7.70013 11.9403 7.70013 12.5004 7.70013ZM7.3125 10.4999C7.3125 10.2238 7.53636 9.99993 7.8125 9.99993H11.6875C11.9636 9.99993 12.1875 10.2238 12.1875 10.4999C12.1875 10.7761 11.9636 10.9999 11.6875 10.9999H7.8125C7.53636 10.9999 7.3125 10.7761 7.3125 10.4999ZM7.8125 11.9999C7.53636 11.9999 7.3125 12.2238 7.3125 12.4999C7.3125 12.7761 7.53636 12.9999 7.8125 12.9999H11.6875C11.9636 12.9999 12.1875 12.7761 12.1875 12.4999C12.1875 12.2238 11.9636 11.9999 11.6875 11.9999H7.8125Z" fill="#F9F9F9"/>
  </svg>

)

export const TableIcon20 = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd" d="M7.15899 3.1499C6.82762 3.1499 6.55899 3.41853 6.55899 3.7499C6.55899 4.08127 6.82762 4.3499 7.15899 4.3499H9.9999H12.8408C13.1722 4.3499 13.4408 4.08127 13.4408 3.7499C13.4408 3.41853 13.1722 3.1499 12.8408 3.1499H9.9999H7.15899ZM4.3499 7.15899C4.3499 6.82762 4.08127 6.55899 3.7499 6.55899C3.41853 6.55899 3.1499 6.82762 3.1499 7.15899V9.9999V12.8408C3.1499 13.1722 3.41853 13.4408 3.7499 13.4408C4.08127 13.4408 4.3499 13.1722 4.3499 12.8408V9.9999V7.15899ZM16.8499 7.15899C16.8499 6.82762 16.5813 6.55899 16.2499 6.55899C15.9185 6.55899 15.6499 6.82762 15.6499 7.15899V9.9999V12.8408C15.6499 13.1722 15.9185 13.4408 16.2499 13.4408C16.5813 13.4408 16.8499 13.1722 16.8499 12.8408V9.9999V7.15899ZM7.15899 15.6499C6.82762 15.6499 6.55899 15.9185 6.55899 16.2499C6.55899 16.5813 6.82762 16.8499 7.15899 16.8499H9.9999H12.8408C13.1722 16.8499 13.4408 16.5813 13.4408 16.2499C13.4408 15.9185 13.1722 15.6499 12.8408 15.6499H9.9999H7.15899ZM12.6258 5.522C12.3552 5.49989 12.023 5.4999 11.6206 5.4999H11.6206H8.37925H8.37924C7.97676 5.4999 7.64459 5.49989 7.37399 5.522C7.09294 5.54496 6.83459 5.59424 6.59192 5.71789C6.2156 5.90964 5.90964 6.2156 5.71789 6.59192C5.59424 6.83459 5.54496 7.09294 5.522 7.37399C5.49989 7.64459 5.4999 7.97676 5.4999 8.37924V8.37925V11.6206V11.6206C5.4999 12.023 5.49989 12.3552 5.522 12.6258C5.54496 12.9069 5.59424 13.1652 5.71789 13.4079C5.90964 13.7842 6.2156 14.0902 6.59192 14.2819C6.83459 14.4056 7.09294 14.4548 7.37399 14.4778C7.64457 14.4999 7.97673 14.4999 8.37919 14.4999H8.37924H11.6206H11.6206C12.0231 14.4999 12.3552 14.4999 12.6258 14.4778C12.9069 14.4548 13.1652 14.4056 13.4079 14.2819C13.7842 14.0902 14.0902 13.7842 14.2819 13.4079C14.4056 13.1652 14.4548 12.9069 14.4778 12.6258C14.4999 12.3552 14.4999 12.023 14.4999 11.6206V8.37924C14.4999 7.97676 14.4999 7.64458 14.4778 7.37399C14.4548 7.09294 14.4056 6.83459 14.2819 6.59192C14.0902 6.2156 13.7842 5.90964 13.4079 5.71789C13.1652 5.59424 12.9069 5.54496 12.6258 5.522Z" fill="#BAB9B8"/>
  </svg>

)

export const TileIcon20 = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M6.26166 4.5H6.28H7.73834H7.73837C7.94667 4.49999 8.13218 4.49998 8.28616 4.51256C8.45048 4.52599 8.62239 4.5562 8.79019 4.64169C9.0348 4.76633 9.23367 4.9652 9.35831 5.20981C9.44381 5.37761 9.47402 5.54952 9.48744 5.71385C9.50002 5.86783 9.50001 6.05335 9.5 6.26166V6.26167V7.73833V7.73834C9.50001 7.94665 9.50002 8.13217 9.48744 8.28616C9.47402 8.45048 9.44381 8.62239 9.35831 8.79019C9.23368 9.0348 9.0348 9.23367 8.79019 9.35831C8.62239 9.44381 8.45048 9.47402 8.28616 9.48744C8.13217 9.50002 7.94665 9.50001 7.73834 9.5H7.73833H6.26167H6.26166C6.05335 9.50001 5.86783 9.50002 5.71385 9.48744C5.54952 9.47402 5.37761 9.44381 5.20981 9.35831C4.9652 9.23368 4.76633 9.0348 4.64169 8.79019C4.5562 8.62239 4.52599 8.45048 4.51256 8.28616C4.49998 8.13218 4.49999 7.94667 4.5 7.73837V7.73834V6.28V6.26166V6.26164C4.49999 6.05333 4.49998 5.86782 4.51256 5.71385C4.52599 5.54952 4.5562 5.37761 4.64169 5.20981C4.76633 4.9652 4.9652 4.76633 5.20981 4.64169C5.37761 4.5562 5.54952 4.52599 5.71385 4.51256C5.86782 4.49998 6.05333 4.49999 6.26164 4.5H6.26166Z" fill="#BAB9B8"/>
    <path d="M12.2617 4.5H12.28H13.7383H13.7384C13.9467 4.49999 14.1322 4.49998 14.2862 4.51256C14.4505 4.52599 14.6224 4.5562 14.7902 4.64169C15.0348 4.76633 15.2337 4.9652 15.3583 5.20981C15.4438 5.37761 15.474 5.54952 15.4874 5.71385C15.5 5.86783 15.5 6.05335 15.5 6.26166V6.26167V7.73833V7.73834C15.5 7.94665 15.5 8.13217 15.4874 8.28616C15.474 8.45048 15.4438 8.62239 15.3583 8.79019C15.2337 9.0348 15.0348 9.23367 14.7902 9.35831C14.6224 9.44381 14.4505 9.47402 14.2862 9.48744C14.1322 9.50002 13.9467 9.50001 13.7383 9.5H13.7383H12.2617H12.2617C12.0534 9.50001 11.8678 9.50002 11.7138 9.48744C11.5495 9.47402 11.3776 9.44381 11.2098 9.35831C10.9652 9.23368 10.7663 9.0348 10.6417 8.79019C10.5562 8.62239 10.526 8.45048 10.5126 8.28616C10.5 8.13218 10.5 7.94667 10.5 7.73837V7.73834V6.28V6.26166V6.26164C10.5 6.05333 10.5 5.86782 10.5126 5.71385C10.526 5.54952 10.5562 5.37761 10.6417 5.20981C10.7663 4.9652 10.9652 4.76633 11.2098 4.64169C11.3776 4.5562 11.5495 4.52599 11.7138 4.51256C11.8678 4.49998 12.0533 4.49999 12.2616 4.5H12.2617Z" fill="#BAB9B8"/>
    <path d="M6.26166 10.5H6.28H7.73834H7.73837C7.94667 10.5 8.13218 10.5 8.28616 10.5126C8.45048 10.526 8.62239 10.5562 8.79019 10.6417C9.0348 10.7663 9.23367 10.9652 9.35831 11.2098C9.44381 11.3776 9.47402 11.5495 9.48744 11.7138C9.50002 11.8678 9.50001 12.0534 9.5 12.2617V12.2617V13.7383V13.7383C9.50001 13.9467 9.50002 14.1322 9.48744 14.2862C9.47402 14.4505 9.44381 14.6224 9.35831 14.7902C9.23368 15.0348 9.0348 15.2337 8.79019 15.3583C8.62239 15.4438 8.45048 15.474 8.28616 15.4874C8.13217 15.5 7.94665 15.5 7.73834 15.5H7.73833H6.26167H6.26166C6.05335 15.5 5.86783 15.5 5.71385 15.4874C5.54952 15.474 5.37761 15.4438 5.20981 15.3583C4.9652 15.2337 4.76633 15.0348 4.64169 14.7902C4.5562 14.6224 4.52599 14.4505 4.51256 14.2862C4.49998 14.1322 4.49999 13.9467 4.5 13.7384V13.7383V12.28V12.2617V12.2616C4.49999 12.0533 4.49998 11.8678 4.51256 11.7138C4.52599 11.5495 4.5562 11.3776 4.64169 11.2098C4.76633 10.9652 4.9652 10.7663 5.20981 10.6417C5.37761 10.5562 5.54952 10.526 5.71385 10.5126C5.86782 10.5 6.05333 10.5 6.26164 10.5H6.26166Z" fill="#BAB9B8"/>
    <path d="M12.2617 10.5H12.28H13.7383H13.7384C13.9467 10.5 14.1322 10.5 14.2862 10.5126C14.4505 10.526 14.6224 10.5562 14.7902 10.6417C15.0348 10.7663 15.2337 10.9652 15.3583 11.2098C15.4438 11.3776 15.474 11.5495 15.4874 11.7138C15.5 11.8678 15.5 12.0534 15.5 12.2617V12.2617V13.7383V13.7383C15.5 13.9467 15.5 14.1322 15.4874 14.2862C15.474 14.4505 15.4438 14.6224 15.3583 14.7902C15.2337 15.0348 15.0348 15.2337 14.7902 15.3583C14.6224 15.4438 14.4505 15.474 14.2862 15.4874C14.1322 15.5 13.9467 15.5 13.7383 15.5H13.7383H12.2617H12.2617C12.0534 15.5 11.8678 15.5 11.7138 15.4874C11.5495 15.474 11.3776 15.4438 11.2098 15.3583C10.9652 15.2337 10.7663 15.0348 10.6417 14.7902C10.5562 14.6224 10.526 14.4505 10.5126 14.2862C10.5 14.1322 10.5 13.9467 10.5 13.7384V13.7383V12.28V12.2617V12.2616C10.5 12.0533 10.5 11.8678 10.5126 11.7138C10.526 11.5495 10.5562 11.3776 10.6417 11.2098C10.7663 10.9652 10.9652 10.7663 11.2098 10.6417C11.3776 10.5562 11.5495 10.526 11.7138 10.5126C11.8678 10.5 12.0533 10.5 12.2616 10.5H12.2617Z" fill="#BAB9B8"/>
  </svg>

)

export const ScaleIcon20 = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M10 15.75C13.1756 15.75 15.75 13.1756 15.75 10C15.75 6.82436 13.1756 4.25 10 4.25C6.82436 4.25 4.25 6.82436 4.25 10C4.25 13.1756 6.82436 15.75 10 15.75Z" stroke="#333332" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M10 4.25V6.75" stroke="#333332" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M4.25 10H6.75" stroke="#333332" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M10 15.75V13.25" stroke="#333332" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M15.75 10H13.25" stroke="#333332" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
  </svg>

)

export const PlusIcon16 = () => (
  <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M2.5 8H13.5" stroke="#333332" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M8 2.5V13.5" stroke="#333332" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
  </svg>

)

export const MinusIcon16 = () => (
  <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M2.5 8H13.5" stroke="#333332" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
  </svg>

)

export const PlusIconFilled20 = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd" d="M17 10C17 13.866 13.866 17 10 17C6.13401 17 3 13.866 3 10C3 6.13401 6.13401 3 10 3C13.866 3 17 6.13401 17 10ZM10 7C10.2761 7 10.5 7.22386 10.5 7.5V9.5H12.5C12.7761 9.5 13 9.72386 13 10C13 10.2761 12.7761 10.5 12.5 10.5H10.5V12.5C10.5 12.7761 10.2761 13 10 13C9.72386 13 9.5 12.7761 9.5 12.5V10.5H7.5C7.22386 10.5 7 10.2761 7 10C7 9.72386 7.22386 9.5 7.5 9.5H9.5V7.5C9.5 7.22386 9.72386 7 10 7Z" fill="#BAB9B8"/>
  </svg>

)

export const PlusIcon20 = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd" d="M10.6004 4.50039C10.6004 4.16902 10.3318 3.90039 10.0004 3.90039C9.66902 3.90039 9.40039 4.16902 9.40039 4.50039V9.40039H4.50039C4.16902 9.40039 3.90039 9.66902 3.90039 10.0004C3.90039 10.3318 4.16902 10.6004 4.50039 10.6004H9.40039V15.5004C9.40039 15.8318 9.66902 16.1004 10.0004 16.1004C10.3318 16.1004 10.6004 15.8318 10.6004 15.5004V10.6004H15.5004C15.8318 10.6004 16.1004 10.3318 16.1004 10.0004C16.1004 9.66902 15.8318 9.40039 15.5004 9.40039H10.6004V4.50039Z" fill="#333332"/>
  </svg>

)

export const MinusIcon20 = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M4.5 10H15.5" stroke="#333332" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
  </svg>

)

export const ChevronUp20 = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M5 12L10 7L15 12" stroke="#929191" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
  </svg>

)

export const ChevronDown20 = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M15 8L10 13L5 8" stroke="#BAB9B8" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
  </svg>

)

export const BoardIcon20 = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M14.5 11.9016V8.09925C14.5 7.53966 14.5 7.25986 14.391 7.04594C14.2952 6.85787 14.142 6.70474 13.9539 6.60892C13.74 6.49994 13.4604 6.49996 12.9012 6.5L12.6 6.50004C12.0399 6.50011 11.7598 6.50015 11.5459 6.60916C11.3578 6.70505 11.2048 6.858 11.109 7.04617C11 7.26006 11 7.54013 11 8.10025V11.9016C11 12.4611 11 12.7409 11.109 12.9548C11.2048 13.1428 11.3579 13.296 11.546 13.3918C11.7599 13.5008 12.0394 13.5008 12.5985 13.5009H12.9015C13.4606 13.5008 13.7401 13.5008 13.954 13.3918C14.1421 13.296 14.2952 13.1428 14.391 12.9548C14.5 12.7409 14.5 12.4611 14.5 11.9016Z" fill="#BAB9B8"/>
    <path fillRule="evenodd" clipRule="evenodd" d="M4.40039 4.50039C4.40039 4.16902 4.66902 3.90039 5.00039 3.90039H15.0004C15.3318 3.90039 15.6004 4.16902 15.6004 4.50039C15.6004 4.83176 15.3318 5.10039 15.0004 5.10039H5.00039C4.66902 5.10039 4.40039 4.83176 4.40039 4.50039ZM15.0004 11.9212V11.9213V11.9213C15.0004 12.1846 15.0004 12.4118 14.9851 12.5989C14.969 12.7965 14.9333 12.993 14.8369 13.1822C14.6932 13.4643 14.4636 13.6939 14.1815 13.8377C13.9924 13.934 13.796 13.9697 13.5984 13.9859C13.4114 14.0012 13.1843 14.0012 12.9212 14.0013H12.5795C12.3165 14.0012 12.0894 14.0012 11.9024 13.9859C11.7048 13.9697 11.5084 13.934 11.3193 13.8377C11.0372 13.6939 10.8076 13.4643 10.6639 13.1822C10.5675 12.993 10.5318 12.7965 10.5157 12.5989C10.5004 12.4118 10.5004 12.1845 10.5004 11.9212V11.9212V8.08138V8.08136C10.5004 7.81776 10.5004 7.59036 10.5157 7.40306C10.5318 7.20539 10.5675 7.00877 10.6639 6.81958C10.8076 6.53734 11.0371 6.3079 11.3193 6.16407C11.5084 6.06766 11.7051 6.03198 11.9027 6.01581C12.09 6.00048 12.3174 6.00046 12.5811 6.00043L12.9208 6.00039C13.184 6.00036 13.4111 6.00034 13.5982 6.01563C13.7957 6.03178 13.9922 6.06745 14.1813 6.1638C14.4635 6.30755 14.6931 6.53718 14.8369 6.81933C14.9333 7.00848 14.9689 7.205 14.9851 7.40262C15.0004 7.58979 15.0004 7.817 15.0004 8.08032V8.08033V8.08035V8.08037V11.9212V11.9212ZM8.09772 6.01616C7.91044 6.00086 7.68307 6.00087 7.4195 6.00088H7.41948H7.07898H7.07896C6.81577 6.00087 6.58868 6.00086 6.40159 6.01616C6.20407 6.0323 6.00764 6.06795 5.81856 6.16424C5.5365 6.30789 5.30691 6.53735 5.16308 6.81932C5.06669 7.0083 5.03091 7.20463 5.01464 7.40208C4.99923 7.58907 4.99909 7.81603 4.99894 8.07902V8.07902L4.99893 8.09829C4.99794 9.70313 4.99863 11.308 4.99932 12.9126V12.9128L4.99932 12.9129L4.99992 14.401L4.99992 14.4203C5 14.684 5.00007 14.9114 5.01542 15.0987C5.03163 15.2964 5.06733 15.493 5.16376 15.6822C5.30763 15.9644 5.53702 16.1937 5.81926 16.3375C6.00843 16.4338 6.20503 16.4695 6.40269 16.4856C6.58997 16.5009 6.81736 16.5009 7.08096 16.5009H7.41949C7.68306 16.5009 7.91044 16.5009 8.09772 16.4856C8.2954 16.4694 8.49201 16.4338 8.6812 16.3374C8.96344 16.1936 9.19291 15.9641 9.33672 15.6819C9.43312 15.4927 9.46878 15.2961 9.48493 15.0984C9.50023 14.9111 9.50022 14.6837 9.50021 14.4202V14.4202V8.0816V8.08158C9.50022 7.81802 9.50023 7.59065 9.48493 7.40337C9.46878 7.20569 9.43312 7.00908 9.33672 6.81989C9.19291 6.53765 8.96344 6.30818 8.6812 6.16437C8.49201 6.06797 8.2954 6.03232 8.09772 6.01616Z" fill="#BAB9B8"/>
  </svg>

)

export const ListViewIcon20 = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd" d="M4.75 5.05078C4.05964 5.05078 3.5 5.61043 3.5 6.30078C3.5 6.99114 4.05964 7.55078 4.75 7.55078C5.44036 7.55078 6 6.99114 6 6.30078C6 5.61043 5.44036 5.05078 4.75 5.05078ZM7.01921 5.85569C7 5.95229 7 6.06845 7 6.30078C7 6.53311 7 6.64927 7.01921 6.74587C7.09812 7.14256 7.40822 7.45266 7.80491 7.53157C7.90151 7.55078 8.01767 7.55078 8.25 7.55078H15.25C15.4823 7.55078 15.5985 7.55078 15.6951 7.53157C16.0918 7.45266 16.4019 7.14256 16.4808 6.74587C16.5 6.64927 16.5 6.53311 16.5 6.30078C16.5 6.06845 16.5 5.95229 16.4808 5.85569C16.4019 5.459 16.0918 5.1489 15.6951 5.07C15.5985 5.05078 15.4823 5.05078 15.25 5.05078H8.25C8.01767 5.05078 7.90151 5.05078 7.80491 5.07C7.40822 5.1489 7.09812 5.459 7.01921 5.85569ZM7 10.001C7 9.76865 7 9.65249 7.01921 9.55589C7.09812 9.1592 7.40822 8.8491 7.80491 8.77019C7.90151 8.75098 8.01767 8.75098 8.25 8.75098H15.25C15.4823 8.75098 15.5985 8.75098 15.6951 8.77019C16.0918 8.8491 16.4019 9.1592 16.4808 9.55589C16.5 9.65249 16.5 9.76865 16.5 10.001C16.5 10.2333 16.5 10.3495 16.4808 10.4461C16.4019 10.8428 16.0918 11.1529 15.6951 11.2318C15.5985 11.251 15.4823 11.251 15.25 11.251H8.25C8.01767 11.251 7.90151 11.251 7.80491 11.2318C7.40822 11.1529 7.09812 10.8428 7.01921 10.4461C7 10.3495 7 10.2333 7 10.001ZM4.75 8.75098C4.05964 8.75098 3.5 9.31062 3.5 10.001C3.5 10.6913 4.05964 11.251 4.75 11.251C5.44036 11.251 6 10.6913 6 10.001C6 9.31062 5.44036 8.75098 4.75 8.75098ZM7 13.7012C7 13.4688 7 13.3527 7.01921 13.2561C7.09812 12.8594 7.40822 12.5493 7.80491 12.4704C7.90151 12.4512 8.01767 12.4512 8.25 12.4512H15.25C15.4823 12.4512 15.5985 12.4512 15.6951 12.4704C16.0918 12.5493 16.4019 12.8594 16.4808 13.2561C16.5 13.3527 16.5 13.4688 16.5 13.7012C16.5 13.9335 16.5 14.0497 16.4808 14.1463C16.4019 14.543 16.0918 14.8531 15.6951 14.932C15.5985 14.9512 15.4823 14.9512 15.25 14.9512H8.25C8.01767 14.9512 7.90151 14.9512 7.80491 14.932C7.40822 14.8531 7.09812 14.543 7.01921 14.1463C7 14.0497 7 13.9335 7 13.7012ZM4.75 12.4512C4.05964 12.4512 3.5 13.0108 3.5 13.7012C3.5 14.3915 4.05964 14.9512 4.75 14.9512C5.44036 14.9512 6 14.3915 6 13.7012C6 13.0108 5.44036 12.4512 4.75 12.4512Z" fill="#BAB9B8"/>
  </svg>

)

export const TimelineIcon = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd" d="M4.49954 15.6001C4.16817 15.6001 3.89954 15.3315 3.89954 15.0001L3.89954 5.0001C3.89954 4.66873 4.16816 4.4001 4.49954 4.4001C4.83091 4.4001 5.09954 4.66873 5.09954 5.0001L5.09954 15.0001C5.09954 15.3315 4.83091 15.6001 4.49954 15.6001ZM11.9214 5.0001L11.9214 5.0001L11.9214 5.0001C12.1847 5.00009 12.4119 5.00008 12.599 5.01538C12.7966 5.03154 12.9931 5.06721 13.1823 5.16357C13.4644 5.30731 13.694 5.53692 13.8378 5.81903C13.9342 6.00812 13.9699 6.20453 13.986 6.40206C14.0014 6.58913 14.0014 6.81616 14.0014 7.07924L14.0014 7.42095C14.0014 7.68403 14.0014 7.91107 13.986 8.09814C13.9699 8.29566 13.9342 8.49207 13.8378 8.68116C13.694 8.96327 13.4644 9.19288 13.1823 9.33662C12.9931 9.43299 12.7966 9.46866 12.599 9.48481C12.4119 9.50011 12.1847 9.50011 11.9214 9.5001L11.9213 9.5001L8.0815 9.5001L8.08149 9.5001C7.81788 9.50011 7.59048 9.50011 7.40318 9.48482C7.20551 9.46867 7.00889 9.43302 6.81971 9.33664C6.53746 9.19284 6.30803 8.96344 6.16419 8.68122C6.06778 8.49204 6.0321 8.29543 6.01593 8.09776C6.00061 7.91046 6.00058 7.68305 6.00055 7.41944L6.00052 7.07969C6.00049 6.81653 6.00046 6.58943 6.01575 6.40232C6.0319 6.20476 6.06757 6.00831 6.16392 5.81919C6.30768 5.53702 6.5373 5.30738 6.81945 5.1636C7.00861 5.06722 7.20513 5.03154 7.40274 5.01539C7.58992 5.00008 7.81712 5.00009 8.08045 5.0001L8.08046 5.0001L8.08047 5.0001L8.08049 5.0001L11.9213 5.0001L11.9214 5.0001ZM6.01482 11.9028C5.99952 12.09 5.99953 12.3174 5.99954 12.581L5.99954 12.581L5.99954 12.9215L5.99954 12.9215C5.99953 13.1847 5.99952 13.4118 6.01481 13.5989C6.03096 13.7964 6.0666 13.9928 6.1629 14.1819C6.30655 14.464 6.536 14.6936 6.81798 14.8374C7.00696 14.9338 7.20328 14.9696 7.40073 14.9859C7.58773 15.0013 7.81469 15.0014 8.07767 15.0015L8.07768 15.0015L8.09695 15.0016C9.70178 15.0025 11.3067 15.0019 12.9113 15.0012L12.9114 15.0012L12.9116 15.0012L14.3997 15.0006L14.419 15.0006C14.6826 15.0005 14.9101 15.0004 15.0974 14.9851C15.2951 14.9689 15.4917 14.9332 15.6808 14.8367C15.963 14.6929 16.1923 14.4635 16.3361 14.1812C16.4325 13.9921 16.4681 13.7955 16.4843 13.5978C16.4996 13.4105 16.4995 13.1831 16.4995 12.9195L16.4995 12.581C16.4995 12.3174 16.4996 12.09 16.4843 11.9028C16.4681 11.7051 16.4324 11.5085 16.336 11.3193C16.1922 11.037 15.9628 10.8076 15.6805 10.6638C15.4913 10.5674 15.2947 10.5317 15.0971 10.5156C14.9098 10.5003 14.6824 10.5003 14.4188 10.5003L14.4188 10.5003L8.08025 10.5003L8.08024 10.5003C7.81668 10.5003 7.5893 10.5003 7.40202 10.5156C7.20435 10.5317 7.00774 10.5674 6.81855 10.6638C6.53631 10.8076 6.30684 11.037 6.16303 11.3193C6.06663 11.5085 6.03097 11.7051 6.01482 11.9028Z" fill="#BAB9B8"/>
  </svg>

)

export const AllOProviderLogo20 = () => (
  <svg width="20" height="21" viewBox="0 0 20 21" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect y="0.417969" width="20" height="20" rx="6" fill="#FF8769"/>
    <path d="M7.83789 12.6541C7.83789 12.9083 8.04399 13.1144 8.29823 13.1144C8.55247 13.1144 8.75857 12.9083 8.75857 12.6541L8.75857 7.86463C8.75857 7.6104 8.55247 7.4043 8.29823 7.4043C8.04399 7.4043 7.83789 7.6104 7.83789 7.86463L7.83789 12.6541Z" fill="#F9F9F9"/>
    <path fillRule="evenodd" clipRule="evenodd" d="M14.1554 13.2249C15.7934 13.2249 17.1213 11.897 17.1213 10.2589C17.1213 8.62087 15.7934 7.29297 14.1554 7.29297C12.5174 7.29297 11.1895 8.62087 11.1895 10.2589C11.1895 11.897 12.5174 13.2249 14.1554 13.2249ZM14.1553 12.3043C15.2849 12.3043 16.2006 11.3886 16.2006 10.259C16.2006 9.12944 15.2849 8.21374 14.1553 8.21374C13.0257 8.21374 12.11 9.12944 12.11 10.259C12.11 11.3886 13.0257 12.3043 14.1553 12.3043Z" fill="#F9F9F9"/>
    <path d="M9.56836 12.6456C9.56836 12.8998 9.77446 13.1059 10.0287 13.1059C10.2829 13.1059 10.489 12.8998 10.489 12.6456V8.85057C10.489 8.05181 9.56836 7.52623 9.56836 8.32498V12.6456Z" fill="#F9F9F9"/>
    <path fillRule="evenodd" clipRule="evenodd" d="M6.12757 8.93417C6.11988 9.04446 5.98889 9.10107 5.89679 9.03989C5.58295 8.8314 5.21026 8.69727 4.76418 8.69727C3.51371 8.69727 2.5 9.71097 2.5 10.9614C2.5 12.2119 3.51371 13.2256 4.76418 13.2256C5.21053 13.2256 5.58354 13.0924 5.89758 12.8841C5.98931 12.8232 6.11976 12.8797 6.12735 12.9895C6.13224 13.0601 6.19098 13.1149 6.26179 13.1149H6.5668C6.82173 13.1149 7.02839 12.9083 7.02839 12.6533L7.02839 9.26976C7.02839 9.01485 6.82175 8.80821 6.56684 8.80821L6.26262 8.80821C6.1915 8.80821 6.13251 8.86323 6.12757 8.93417ZM6.10898 10.9615C6.10898 11.7043 5.50686 12.3064 4.7641 12.3064C4.02134 12.3064 3.41922 11.7043 3.41922 10.9615C3.41922 10.2188 4.02134 9.61665 4.7641 9.61665C5.50686 9.61665 6.10898 10.2188 6.10898 10.9615Z" fill="#F9F9F9"/>
  </svg>

)

export const PaymentBadgeIcon20 = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <circle cx="10" cy="10" r="9" fill="#428271"/>
    <path d="M6.5 10.1923L8.71053 12.5L13.5 7.5" stroke="white" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
  </svg>

)

export const PaymentPartialBadgeIcon20 = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <circle cx="10" cy="10" r="9" fill="#428271"/>
    <path d="M3 10C3 13.866 6.13401 17 10 17V3C6.13401 3 3 6.13401 3 10Z" fill="#F2F2F2"/>
  </svg>

)

export const CircularProgressBar68 = ({ progress, color }) => (
  <svg width="68" height="68" viewBox="0 0 68 68" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect x="32" width="4" height="4" rx="2" fill={progress >= 24 ? "#E8E7E6" : color} />
    <rect x="40" y="1" width="4" height="4" rx="2" fill={progress >= 23 ? "#E8E7E6" : color} />
    <rect x="48" y="4" width="4" height="4" rx="2" fill={progress >= 22 ? "#E8E7E6" : color}/>
    <rect x="55" y="9" width="4" height="4" rx="2" fill={progress >= 21 ? "#E8E7E6" : color}/>
    <rect x="60" y="16" width="4" height="4" rx="2" fill={progress >= 20 ? "#E8E7E6" : color}/>
    <rect x="63" y="24" width="4" height="4" rx="2" fill={progress >= 19 ? "#E8E7E6" : color}/>
    <rect x="64" y="32" width="4" height="4" rx="2" fill={progress >= 18 ? "#E8E7E6" : color}/>
    <rect x="63" y="40" width="4" height="4" rx="2" fill={progress >= 17 ? "#E8E7E6" : color}/>
    <rect x="60" y="48" width="4" height="4" rx="2" fill={progress >= 16 ? "#E8E7E6" : color}/>
    <rect x="55" y="55" width="4" height="4" rx="2" fill={progress >= 15 ? "#E8E7E6" : color}/>
    <rect x="48" y="60" width="4" height="4" rx="2" fill={progress >= 14 ? "#E8E7E6" : color}/>
    <rect x="40" y="63" width="4" height="4" rx="2" fill={progress >= 13 ? "#E8E7E6" : color}/>
    <rect x="32" y="64" width="4" height="4" rx="2" fill={progress >= 12 ? "#E8E7E6" : color}/>
    <rect x="24" y="63" width="4" height="4" rx="2" fill={progress >= 11 ? "#E8E7E6" : color}/>
    <rect x="16" y="60" width="4" height="4" rx="2" fill={progress >= 10 ? "#E8E7E6" : color}/>
    <rect x="9" y="55" width="4" height="4" rx="2" fill={progress >= 9 ? "#E8E7E6" : color}/>
    <rect x="4" y="48" width="4" height="4" rx="2" fill={progress >= 8 ? "#E8E7E6" : color}/>
    <rect x="1" y="40" width="4" height="4" rx="2" fill={progress >= 7 ? "#E8E7E6" : color}/>
    <rect y="32" width="4" height="4" rx="2" fill={progress >= 6 ? "#E8E7E6" : color}/>
    <rect x="1" y="24" width="4" height="4" rx="2" fill={progress >= 5 ? "#E8E7E6" : color}/>
    <rect x="4" y="16" width="4" height="4" rx="2" fill={progress >= 4 ? "#E8E7E6" : color}/>
    <rect x="9" y="9" width="4" height="4" rx="2" fill={progress >= 3 ? "#E8E7E6" : color}/>
    <rect x="16" y="4" width="4" height="4" rx="2" fill={progress >= 2 ? "#E8E7E6" : color}/>
    <rect x="24" y="1" width="4" height="4" rx="2" fill={progress >= 1 ? "#E8E7E6" : color}/>
  </svg>
)

export const CircularProgressBar68Negative = ({ progress, color }) => (
  <svg width="68" height="68" viewBox="0 0 68 68" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect x="32" width="4" height="4" rx="2" fill={progress >= 1 ? "#E8E7E6" : color} />
    <rect x="40" y="1" width="4" height="4" rx="2" fill={progress >= 2 ? "#E8E7E6" : color} />
    <rect x="48" y="4" width="4" height="4" rx="2" fill={progress >= 3 ? "#E8E7E6" : color}/>
    <rect x="55" y="9" width="4" height="4" rx="2" fill={progress >= 4 ? "#E8E7E6" : color}/>
    <rect x="60" y="16" width="4" height="4" rx="2" fill={progress >= 5 ? "#E8E7E6" : color}/>
    <rect x="63" y="24" width="4" height="4" rx="2" fill={progress >= 6 ? "#E8E7E6" : color}/>
    <rect x="64" y="32" width="4" height="4" rx="2" fill={progress >= 7 ? "#E8E7E6" : color}/>
    <rect x="63" y="40" width="4" height="4" rx="2" fill={progress >= 8 ? "#E8E7E6" : color}/>
    <rect x="60" y="48" width="4" height="4" rx="2" fill={progress >= 9 ? "#E8E7E6" : color}/>
    <rect x="55" y="55" width="4" height="4" rx="2" fill={progress >= 10 ? "#E8E7E6" : color}/>
    <rect x="48" y="60" width="4" height="4" rx="2" fill={progress >= 11 ? "#E8E7E6" : color}/>
    <rect x="40" y="63" width="4" height="4" rx="2" fill={progress >= 12 ? "#E8E7E6" : color}/>
    <rect x="32" y="64" width="4" height="4" rx="2" fill={progress >= 13 ? "#E8E7E6" : color}/>
    <rect x="24" y="63" width="4" height="4" rx="2" fill={progress >= 14 ? "#E8E7E6" : color}/>
    <rect x="16" y="60" width="4" height="4" rx="2" fill={progress >= 15 ? "#E8E7E6" : color}/>
    <rect x="9" y="55" width="4" height="4" rx="2" fill={progress >= 16 ? "#E8E7E6" : color}/>
    <rect x="4" y="48" width="4" height="4" rx="2" fill={progress >= 17 ? "#E8E7E6" : color}/>
    <rect x="1" y="40" width="4" height="4" rx="2" fill={progress >= 18 ? "#E8E7E6" : color}/>
    <rect y="32" width="4" height="4" rx="2" fill={progress >= 19 ? "#E8E7E6" : color}/>
    <rect x="1" y="24" width="4" height="4" rx="2" fill={progress >= 20 ? "#E8E7E6" : color}/>
    <rect x="4" y="16" width="4" height="4" rx="2" fill={progress >= 21 ? "#E8E7E6" : color}/>
    <rect x="9" y="9" width="4" height="4" rx="2" fill={progress >= 22 ? "#E8E7E6" : color}/>
    <rect x="16" y="4" width="4" height="4" rx="2" fill={progress >= 23 ? "#E8E7E6" : color}/>
    <rect x="24" y="1" width="4" height="4" rx="2" fill={progress >= 24 ? "#E8E7E6" : color}/>
  </svg>
)

export const CircularProgressBar68Full = () => (
  <svg width="68" height="68" viewBox="0 0 68 68" fill="none" xmlns="http://www.w3.org/2000/svg">
    <circle cx="34" cy="34" r="32" stroke="#FFA787" strokeWidth="4" strokeLinecap="round" strokeDasharray="0.1 8"/>
  </svg>

)

export const CircularProgressBar68ThreeQuarter = () => (
  <svg width="68" height="68" viewBox="0 0 68 68" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M2 34C2 51.6731 16.3269 66 34 66C51.6731 66 66 51.6731 66 34C66 25.0988 62.3657 17.0465 56.5 11.2459C50.7195 5.52952 42.7719 2 34 2" stroke="#FFA787" strokeWidth="4" strokeLinecap="round" strokeDasharray="0.1 8"/>
  </svg>

)

export const NoItems80 = () => (
  <svg width="81" height="80" viewBox="0 0 81 80" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M3.69052 21.7142H40.8334L35.5273 73.1428H8.99664L3.69052 21.7142Z" fill="#BAB9B8"/>
    <path d="M43.6905 21.7142L0.833378 21.7142L4.09868 15.9999L40.4252 16L43.6905 21.7142Z" fill="#929191"/>
    <rect x="76.5477" y="20.2858" width="4.28571" height="41.4286" transform="rotate(30 76.5477 20.2858)" fill="#C4C4C4"/>
    <path d="M5.11908 34.5714H39.4048L37.2619 57.4286H7.26194L5.11908 34.5714Z" fill="#D8D7D6"/>
    <path d="M52.2619 73.1428C68.0415 73.1428 80.8334 60.351 80.8334 44.5714H23.6905C23.6905 60.351 36.4824 73.1428 52.2619 73.1428Z" fill="#929191"/>
  </svg>

)

export const NoTakeaway120x90 = () => (
  <svg width="120" height="90" viewBox="0 0 120 90" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M13.9339 0H81.4052V90H12.4671L13.9339 0Z" fill="#D8D7D6"/>
    <path d="M25.6684 90L13.9343 0L0 90H25.6684Z" fill="#BAB9B8"/>
    <path d="M94.6065 90L81.5915 0L68.2047 90H94.6065Z" fill="#D8D7D6"/>
    <path d="M13.9341 0H83.9722L85.0723 9.07563H15.0342L13.9341 0Z" fill="#929191"/>
    <path d="M60.4653 23.8774H110.258V89.9997H59.3828L60.4653 23.8774Z" fill="#BAB9B8"/>
    <path d="M69.1249 89.9997L60.4653 23.8774L50.182 89.9997H69.1249Z" fill="#929191"/>
    <path d="M120 89.9997L110.395 23.8774L100.516 89.9997H120Z" fill="#BAB9B8"/>
    <path d="M60.4608 23.8774L112.163 23.8774L113.231 32.6938H61.5799L60.4608 23.8774Z" fill="#737372"/>
  </svg>

)

export const NoReservation120 = () => (
  <svg width="120" height="120" viewBox="0 0 120 120" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M74.717 120C99.7261 120 120 99.8528 120 75H29.434C29.434 99.8528 49.7079 120 74.717 120Z" fill="#D8D7D6"/>
    <path d="M79.2453 75C92.5835 75 103.396 64.2548 103.396 51H55.0943C55.0943 64.2548 65.9071 75 79.2453 75Z" fill="#E8E7E6"/>
    <path d="M31.2479 0H13.4478L13.4926 25.1679C13.4995 30.6773 11.0251 35.9014 6.74631 39.4112C2.47374 42.9157 0 48.13 0 53.6313V120H44.5283V53.4979C44.5283 48.0405 42.0977 42.8622 37.8881 39.3513C33.6785 35.8403 31.2479 30.6621 31.2479 25.2047V0Z" fill="#BAB9B8"/>
    <path d="M44.4305 86.25H97.3585L97.4563 120H44.4305V86.25Z" fill="#BAB9B8"/>
    <path d="M60.7547 120L44.5283 86.25L28.3019 120H60.7547Z" fill="#929191"/>
    <path d="M113.962 120L97.3585 86.25L81.5095 120H113.962Z" fill="#BAB9B8"/>
  </svg>

)

export const NoTables100 = () => (
  <svg width="100" height="100" viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect opacity="0.9" x="20" y="50" width="10" height="50" fill="#929191"/>
    <rect y="50" width="10" height="50" fill="#BAB9B8"/>
    <rect x="100" y="50" width="10" height="50" transform="rotate(-180 100 50)" fill="#929191"/>
    <rect x="60" y="50" width="10" height="60" transform="rotate(90 60 50)" fill="#BAB9B8"/>
    <rect x="80" y="50" width="10" height="50" transform="rotate(-180 80 50)" fill="#737372"/>
    <rect x="40" y="50" width="10" height="60" transform="rotate(-90 40 50)" fill="#929191"/>
    <rect x="40" width="50" height="40" transform="rotate(90 40 0)" fill="#BAB9B8"/>
    <rect x="60" y="100" width="50" height="40" transform="rotate(-90 60 100)" fill="#929191"/>
    <rect x="50" y="50" width="10" height="50" fill="#BAB9B8"/>
    <rect x="50" y="50" width="10" height="50" transform="rotate(-180 50 50)" fill="#929191"/>
  </svg>

)

export const Play20 = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd" d="M17 10C17 13.866 13.866 17 10 17C6.13401 17 3 13.866 3 10C3 6.13401 6.13401 3 10 3C13.866 3 17 6.13401 17 10ZM9.27735 7.58398C9.12392 7.48169 8.92665 7.47215 8.76407 7.55916C8.60149 7.64617 8.5 7.8156 8.5 8V12C8.5 12.1844 8.60149 12.3538 8.76407 12.4408C8.92665 12.5278 9.12392 12.5183 9.27735 12.416L12.2774 10.416C12.4164 10.3233 12.5 10.1672 12.5 10C12.5 9.83282 12.4164 9.67671 12.2774 9.58398L9.27735 7.58398Z" fill="#BAB9B8"/>
  </svg>

)

export const CloseIcon24 = () => (
  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd" d="M19.8002 12.0002C19.8002 16.308 16.308 19.8002 12.0002 19.8002C7.69237 19.8002 4.2002 16.308 4.2002 12.0002C4.2002 7.69237 7.69237 4.2002 12.0002 4.2002C16.308 4.2002 19.8002 7.69237 19.8002 12.0002ZM14.7532 9.24654C14.9484 9.44181 14.9484 9.75839 14.7532 9.95365L12.7067 12.0001L14.7532 14.0465C14.9484 14.2418 14.9484 14.5584 14.7532 14.7537C14.5579 14.9489 14.2413 14.9489 14.0461 14.7537L11.9996 12.7072L9.95316 14.7537C9.7579 14.9489 9.44132 14.9489 9.24606 14.7537C9.05079 14.5584 9.05079 14.2418 9.24606 14.0465L11.2925 12.0001L9.24606 9.95365C9.05079 9.75839 9.05079 9.44181 9.24606 9.24654C9.44132 9.05128 9.7579 9.05128 9.95316 9.24654L11.9996 11.293L14.0461 9.24654C14.2413 9.05128 14.5579 9.05128 14.7532 9.24654Z" fill="#BAB9B8"/>
  </svg>

)

export const ArrowLeft16 = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd" d="M5.83323 3.76398C7.06659 2.93987 8.51664 2.5 10 2.5C11.9884 2.5023 13.8947 3.29321 15.3008 4.69924C16.7068 6.10526 17.4977 8.01158 17.5 10C17.5 11.4834 17.0601 12.9334 16.236 14.1668C15.4119 15.4001 14.2406 16.3614 12.8701 16.9291C11.4997 17.4967 9.99168 17.6453 8.53682 17.3559C7.08197 17.0665 5.7456 16.3522 4.6967 15.3033C3.64781 14.2544 2.9335 12.918 2.64411 11.4632C2.35472 10.0083 2.50325 8.50032 3.07091 7.12987C3.63856 5.75943 4.59986 4.58809 5.83323 3.76398ZM13.6247 9.96437C13.6247 10.2405 13.4009 10.4644 13.1247 10.4644H8.07963L9.88145 12.2722C10.0764 12.4677 10.0759 12.7843 9.88028 12.9793C9.6847 13.1742 9.36812 13.1737 9.17318 12.9781L6.53206 10.3282C6.43536 10.2371 6.375 10.1078 6.375 9.96437C6.375 9.81602 6.4396 9.68277 6.54221 9.5912L9.17293 6.94807C9.36773 6.75235 9.68432 6.75161 9.88004 6.94641C10.0758 7.14121 10.0765 7.45779 9.8817 7.65351L8.07934 9.46437H13.1247C13.4009 9.46437 13.6247 9.68823 13.6247 9.96437Z" fill="#BAB9B8"/>
  </svg>

)

export const RevenueIcon16 = () => (
  <svg width="16" height="17" viewBox="0 0 16 17" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd" d="M14 8.41797C14 11.7317 11.3137 14.418 8 14.418C4.68629 14.418 2 11.7317 2 8.41797C2 5.10426 4.68629 2.41797 8 2.41797C11.3137 2.41797 14 5.10426 14 8.41797ZM8.49902 5.61816C8.49902 5.34202 8.27517 5.11816 7.99902 5.11816C7.72288 5.11816 7.49902 5.34202 7.49902 5.61816V5.91797H7.39941C7.00159 5.91797 6.62006 6.076 6.33875 6.35731C6.05745 6.63861 5.89941 7.02014 5.89941 7.41797C5.89941 7.81579 6.05745 8.19732 6.33875 8.47863C6.62006 8.75993 7.00159 8.91797 7.39941 8.91797H8.59941C8.73202 8.91797 8.8592 8.97065 8.95297 9.06442C9.04674 9.15818 9.09941 9.28536 9.09941 9.41797C9.09941 9.55058 9.04674 9.67775 8.95297 9.77152C8.8592 9.86529 8.73202 9.91797 8.59941 9.91797H7.99902H6.79941C6.52327 9.91797 6.29941 10.1418 6.29941 10.418C6.29941 10.6941 6.52327 10.918 6.79941 10.918H7.49902V11.218C7.49902 11.4941 7.72288 11.718 7.99902 11.718C8.27517 11.718 8.49902 11.4941 8.49902 11.218V10.918H8.59941C8.99724 10.918 9.37877 10.7599 9.66007 10.4786C9.94138 10.1973 10.0994 9.81579 10.0994 9.41797C10.0994 9.02014 9.94138 8.63861 9.66007 8.35731C9.37877 8.076 8.99724 7.91797 8.59941 7.91797H7.39941C7.26681 7.91797 7.13963 7.86529 7.04586 7.77152C6.95209 7.67775 6.89941 7.55058 6.89941 7.41797C6.89941 7.28536 6.95209 7.15818 7.04586 7.06442C7.13963 6.97065 7.26681 6.91797 7.39941 6.91797H7.9849L7.99902 6.91816L8.01314 6.91797H9.19941C9.47556 6.91797 9.69941 6.69411 9.69941 6.41797C9.69941 6.14183 9.47556 5.91797 9.19941 5.91797H8.49902V5.61816Z" fill="#BAB9B8"/>
  </svg>

)

export const CheckIconGreen16 = () => (
  <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd" d="M14 8C14 11.3137 11.3137 14 8 14C4.68629 14 2 11.3137 2 8C2 4.68629 4.68629 2 8 2C11.3137 2 14 4.68629 14 8ZM11.0952 6.86168C11.295 6.67101 11.3023 6.35451 11.1117 6.15476C10.921 5.95501 10.6045 5.94765 10.4048 6.13832L7.08332 9.30878L5.59524 7.88832C5.39549 7.69765 5.07899 7.70501 4.88832 7.90476C4.69765 8.10451 4.70501 8.42101 4.90476 8.61168L6.73807 10.3617C6.93128 10.5461 7.23534 10.5461 7.42855 10.3617L11.0952 6.86168Z" fill="#4D9985"/>
  </svg>

)

export const WarningCircle16 = () => (
  <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd" d="M14 8C14 11.3137 11.3137 14 8 14C4.68629 14 2 11.3137 2 8C2 4.68629 4.68629 2 8 2C11.3137 2 14 4.68629 14 8ZM8 4.5C8.27614 4.5 8.5 4.72386 8.5 5V8.5C8.5 8.77614 8.27614 9 8 9C7.72386 9 7.5 8.77614 7.5 8.5V5C7.5 4.72386 7.72386 4.5 8 4.5ZM8 11.5C8.41421 11.5 8.75 11.1642 8.75 10.75C8.75 10.3358 8.41421 10 8 10C7.58579 10 7.25 10.3358 7.25 10.75C7.25 11.1642 7.58579 11.5 8 11.5Z" fill="#FF7C5C"/>
  </svg>

)

export const ClockIcon20 = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M9.5 3.50024C8.21443 3.50024 6.95772 3.88146 5.8888 4.59569C4.81988 5.30992 3.98676 6.32508 3.49479 7.5128C3.00282 8.70052 2.87409 10.0075 3.1249 11.2683C3.3757 12.5292 3.99477 13.6874 4.90381 14.5964C5.81285 15.5055 6.97104 16.1245 8.23192 16.3753C9.49279 16.6262 10.7997 16.4974 11.9874 16.0055C13.1752 15.5135 14.1903 14.6804 14.9046 13.6114C15.6188 12.5425 16 11.2858 16 10.0002C15.998 8.27695 15.3126 6.6248 14.094 5.40625C12.8754 4.18769 11.2233 3.50224 9.5 3.50024ZM13 10.8232H9.5C9.43433 10.8232 9.3693 10.8103 9.30862 10.7852C9.24794 10.7601 9.1928 10.7233 9.14637 10.6768C9.09993 10.6304 9.0631 10.5752 9.03799 10.5146C9.01287 10.4539 8.99997 10.3889 9 10.3232V6.50024C9 6.36764 9.05268 6.24046 9.14645 6.14669C9.24022 6.05292 9.3674 6.00024 9.5 6.00024C9.63261 6.00024 9.75979 6.05292 9.85356 6.14669C9.94733 6.24046 10 6.36764 10 6.50024V9.82318H13C13.1326 9.82318 13.2598 9.87586 13.3536 9.96963C13.4473 10.0634 13.5 10.1906 13.5 10.3232C13.5 10.4558 13.4473 10.583 13.3536 10.6767C13.2598 10.7705 13.1326 10.8232 13 10.8232Z" fill="#BAB9B8" />
  </svg>

)

export const ClockIconLight20 = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M9.5 3.50024C8.21443 3.50024 6.95772 3.88146 5.8888 4.59569C4.81988 5.30992 3.98676 6.32508 3.49479 7.5128C3.00282 8.70052 2.87409 10.0075 3.1249 11.2683C3.3757 12.5292 3.99477 13.6874 4.90381 14.5964C5.81285 15.5055 6.97104 16.1245 8.23192 16.3753C9.49279 16.6262 10.7997 16.4974 11.9874 16.0055C13.1752 15.5135 14.1903 14.6804 14.9046 13.6114C15.6188 12.5425 16 11.2858 16 10.0002C15.998 8.27695 15.3126 6.6248 14.094 5.40625C12.8754 4.18769 11.2233 3.50224 9.5 3.50024ZM13 10.8232H9.5C9.43433 10.8232 9.3693 10.8103 9.30862 10.7852C9.24794 10.7601 9.1928 10.7233 9.14637 10.6768C9.09993 10.6304 9.0631 10.5752 9.03799 10.5146C9.01287 10.4539 8.99997 10.3889 9 10.3232V6.50024C9 6.36764 9.05268 6.24046 9.14645 6.14669C9.24022 6.05292 9.3674 6.00024 9.5 6.00024C9.63261 6.00024 9.75979 6.05292 9.85356 6.14669C9.94733 6.24046 10 6.36764 10 6.50024V9.82318H13C13.1326 9.82318 13.2598 9.87586 13.3536 9.96963C13.4473 10.0634 13.5 10.1906 13.5 10.3232C13.5 10.4558 13.4473 10.583 13.3536 10.6767C13.2598 10.7705 13.1326 10.8232 13 10.8232Z" fill="#F9F9F9" fillOpacity="0.8"/>
  </svg>

)

export const RevenueIconLight20 = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M17 10C17 13.866 13.866 17 10 17C6.13401 17 3 13.866 3 10C3 6.13401 6.13401 3 10 3C13.866 3 17 6.13401 17 10Z" fill="#D1D1D1" stroke="#D1D1D1" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M10.0181 6.25V7.32143" stroke="#242423" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M10.0181 12.6785V13.7499" stroke="#242423" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M8.41071 12.6787H10.8214C11.1766 12.6787 11.5173 12.5376 11.7684 12.2864C12.0196 12.0352 12.1607 11.6946 12.1607 11.3394C12.1607 10.9842 12.0196 10.6435 11.7684 10.3924C11.5173 10.1412 11.1766 10.0001 10.8214 10.0001H9.21429C8.85909 10.0001 8.51843 9.859 8.26727 9.60784C8.0161 9.35667 7.875 9.01602 7.875 8.66082C7.875 8.30562 8.0161 7.96497 8.26727 7.7138C8.51843 7.46264 8.85909 7.32153 9.21429 7.32153H11.625" stroke="#242423" strokeLinecap="round" strokeLinejoin="round"/>
  </svg>

)

export const MandatoryChoices24 = () => (
  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M20 8.97065C20 8.54012 19.7495 8.25832 19.1233 7.89041L12.9941 4.32094C12.6184 4.10176 12.3131 4 12 4C11.6791 4 11.3816 4.10176 11.0059 4.32094L4.87671 7.89041C4.25049 8.25832 4 8.54012 4 8.97065C4 9.40117 4.25049 9.68297 4.87671 10.0587L11.0059 13.6204C11.3816 13.8474 11.6791 13.9413 12 13.9413C12.3131 13.9413 12.6184 13.8395 12.9941 13.6204L19.1233 10.0587C19.7495 9.68297 20 9.40117 20 8.97065Z" fill="#BAB9B8"/>
    <path d="M4.42188 12.7079C6.20859 13.7499 7.97547 14.8471 9.77441 15.8796C10.5307 16.3137 10.9089 16.5308 11.3729 16.6285C11.7562 16.7092 12.2347 16.7085 12.6178 16.6267C13.0815 16.5276 13.4646 16.3062 14.2307 15.8635C16.0237 14.8275 17.7916 13.7432 19.5765 12.7002" stroke="#BAB9B8" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M4.42188 16.0077C6.6362 17.2965 8.82008 18.67 11.0723 19.9004C11.654 20.2182 12.3574 20.2134 12.9367 19.891C15.1773 18.6443 17.3638 17.2904 19.5765 16" stroke="#BAB9B8" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round" strokeDasharray="0.01 2"/>
  </svg>

)

export const CurrencyCircleDollar = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M17 10C17 13.866 13.866 17 10 17C6.13401 17 3 13.866 3 10C3 6.13401 6.13401 3 10 3C13.866 3 17 6.13401 17 10Z" fill="#BAB9B8" stroke="#BAB9B8" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M10.0178 6.25V7.32143" stroke="#E8E7E6" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M10.0178 12.6785V13.7499" stroke="#E8E7E6" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M8.41071 12.6787H10.8214C11.1766 12.6787 11.5173 12.5376 11.7684 12.2864C12.0196 12.0352 12.1607 11.6946 12.1607 11.3394C12.1607 10.9842 12.0196 10.6435 11.7684 10.3924C11.5173 10.1412 11.1766 10.0001 10.8214 10.0001H9.21429C8.85909 10.0001 8.51843 9.859 8.26727 9.60784C8.0161 9.35667 7.875 9.01602 7.875 8.66082C7.875 8.30562 8.0161 7.96497 8.26727 7.7138C8.51843 7.46264 8.85909 7.32153 9.21429 7.32153H11.625" stroke="#E8E7E6" strokeLinecap="round" strokeLinejoin="round"/>
  </svg>

)

export const CurrencyCircleDollarTableTaken = () => (
  <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M8 14C11.3137 14 14 11.3137 14 8C14 4.68629 11.3137 2 8 2C4.68629 2 2 4.68629 2 8C2 11.3137 4.68629 14 8 14Z" fill="#7A0F19" fillOpacity="0.5"/>
    <path d="M8.01428 5V5.85714" stroke="#FFD5B8" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M8.01428 10.1431V11.0002" stroke="#FFD5B8" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M6.72862 10.1426H8.65719C8.94135 10.1426 9.21387 10.0298 9.41481 9.82883C9.61574 9.6279 9.72862 9.35538 9.72862 9.07122C9.72862 8.78706 9.61574 8.51454 9.41481 8.3136C9.21387 8.11267 8.94135 7.99979 8.65719 7.99979H7.37148C7.08732 7.99979 6.81479 7.88691 6.61386 7.68598C6.41293 7.48504 6.30005 7.21252 6.30005 6.92836C6.30005 6.6442 6.41293 6.37168 6.61386 6.17075C6.81479 5.96982 7.08732 5.85693 7.37148 5.85693H9.30005" stroke="#FFD5B8" strokeLinecap="round" strokeLinejoin="round"/>
  </svg>

)

export const CurrencyCircleDollarTableActive = () => (
  <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M8 14C11.3137 14 14 11.3137 14 8C14 4.68629 11.3137 2 8 2C4.68629 2 2 4.68629 2 8C2 11.3137 4.68629 14 8 14Z" fill="#F9F9F9" fillOpacity="0.8"/>
    <path d="M8.01428 5V5.85714" stroke="#FF7C5C" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M8.01428 10.1431V11.0002" stroke="#FF7C5C" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M6.72862 10.1426H8.65719C8.94135 10.1426 9.21387 10.0298 9.41481 9.82883C9.61574 9.6279 9.72862 9.35538 9.72862 9.07122C9.72862 8.78706 9.61574 8.51454 9.41481 8.3136C9.21387 8.11267 8.94135 7.99979 8.65719 7.99979H7.37148C7.08732 7.99979 6.81479 7.88691 6.61386 7.68598C6.41293 7.48504 6.30005 7.21252 6.30005 6.92836C6.30005 6.6442 6.41293 6.37168 6.61386 6.17075C6.81479 5.96982 7.08732 5.85693 7.37148 5.85693H9.30005" stroke="#FF7C5C" strokeLinecap="round" strokeLinejoin="round"/>
  </svg>

)

export const UserCircle20 = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd" d="M3 10C3 6.13401 6.13401 3 10 3C13.866 3 17 6.13401 17 10C17 10.1203 16.997 10.2398 16.991 10.3586C16.9011 12.1404 16.145 13.7464 14.9667 14.9327C13.6987 16.2095 11.9417 17 10 17C8.05835 17 6.30134 16.2095 5.03327 14.9327C3.7765 13.6673 3 11.9243 3 10ZM5.66669 14.15C6.75885 15.2901 8.29652 16 10 16C11.7035 16 13.2411 15.2901 14.3333 14.15C13.2411 13.0099 11.7035 12.3 10 12.3C8.29652 12.3 6.75885 13.0099 5.66669 14.15ZM12.4497 8.5C12.4497 9.88071 11.3304 11 9.94973 11C8.56902 11 7.44973 9.88071 7.44973 8.5C7.44973 7.11929 8.56902 6 9.94973 6C11.3304 6 12.4497 7.11929 12.4497 8.5Z" fill="#BAB9B8"/>
  </svg>

)

export const CoffeeIcon20 = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M7.34375 3.50024V5.62524" stroke="#BAB9B8" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M10 3.50024V5.62524" stroke="#BAB9B8" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M12.6562 3.50024V5.62524" stroke="#BAB9B8" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M3.625 15.344H16.375" stroke="#BAB9B8" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M3.09375 8.00024V8.09399C3.09375 8.9357 3.09375 9.35655 3.13592 9.71034C3.46381 12.4614 5.63263 14.6302 8.38365 14.9581C8.73745 15.0002 9.1583 15.0002 10 15.0002C10.8417 15.0002 11.2626 15.0002 11.6163 14.9581C14.3674 14.6302 16.5362 12.4614 16.8641 9.71034C16.9062 9.35655 16.9062 8.9357 16.9062 8.09399V8.00024H3.09375Z" fill="#BAB9B8" stroke="#BAB9B8" strokeLinejoin="round"/>
  </svg>

)

export const CoffeeIconLight20 = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M7.34375 3.50024V5.62524" stroke="#F9F9F9" strokeOpacity="0.8" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M10 3.50024V5.62524" stroke="#F9F9F9" strokeOpacity="0.8" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M12.6562 3.50024V5.62524" stroke="#F9F9F9" strokeOpacity="0.8" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M3.625 15.344H16.375" stroke="#F9F9F9" strokeOpacity="0.8" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M3.09375 8.00024V8.09399C3.09375 8.9357 3.09375 9.35655 3.13592 9.71034C3.46381 12.4614 5.63263 14.6302 8.38365 14.9581C8.73745 15.0002 9.1583 15.0002 10 15.0002C10.8417 15.0002 11.2626 15.0002 11.6163 14.9581C14.3674 14.6302 16.5362 12.4614 16.8641 9.71034C16.9062 9.35655 16.9062 8.9357 16.9062 8.09399V8.00024H3.09375Z" fill="#F9F9F9" stroke="#F9F9F9" strokeLinejoin="round" fillOpacity="0.8" strokeOpacity="0.8"/>
  </svg>

)

export const UserCircleLight20 = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd" d="M3 10C3 6.13401 6.13401 3 10 3C13.866 3 17 6.13401 17 10C17 10.1203 16.997 10.2398 16.991 10.3586C16.9011 12.1404 16.145 13.7464 14.9667 14.9327C13.6987 16.2095 11.9417 17 10 17C8.05835 17 6.30134 16.2095 5.03327 14.9327C3.7765 13.6673 3 11.9243 3 10ZM5.66669 14.15C6.75885 15.2901 8.29652 16 10 16C11.7035 16 13.2411 15.2901 14.3333 14.15C13.2411 13.0099 11.7035 12.3 10 12.3C8.29652 12.3 6.75885 13.0099 5.66669 14.15ZM12.4497 8.5C12.4497 9.88071 11.3304 11 9.94973 11C8.56902 11 7.44973 9.88071 7.44973 8.5C7.44973 7.11929 8.56902 6 9.94973 6C11.3304 6 12.4497 7.11929 12.4497 8.5Z" fill="#F9F9F9" stroke="#F9F9F9" strokeLinejoin="round" fillOpacity="0.8" strokeOpacity="0.8" />
  </svg>

)

export const DurationIcon16Active = () => (
  <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M5.38863 4.09214C6.16146 3.57575 7.07004 3.30011 7.99951 3.30005C9.24564 3.30156 10.4403 3.79726 11.3214 4.67841C12.2027 5.55962 12.6984 6.7544 12.6998 8.00063C12.6997 8.93 12.4241 9.83848 11.9077 10.6112C11.3913 11.3841 10.6572 11.9865 9.79842 12.3423C8.93961 12.698 7.99459 12.7911 7.08288 12.6097C6.17117 12.4284 5.33371 11.9808 4.67641 11.3235C4.0191 10.6661 3.57147 9.82868 3.39012 8.91697C3.20877 8.00526 3.30184 7.06025 3.65757 6.20144C4.01331 5.34262 4.61572 4.60858 5.38863 4.09214Z" stroke="#FAE0DA"/>
    <path d="M12.6731 8.49997C12.5926 9.25277 12.3311 9.97762 11.9077 10.6112C11.3913 11.3841 10.6572 11.9865 9.79842 12.3423C8.93961 12.698 7.99459 12.7911 7.08288 12.6097C6.17117 12.4284 5.33371 11.9808 4.67641 11.3235C4.0191 10.6661 3.57147 9.82868 3.39012 8.91697C3.20877 8.00526 3.30184 7.06025 3.65757 6.20144C4.01331 5.34262 4.61572 4.60858 5.38863 4.09214C6.02221 3.6688 6.74704 3.40725 7.49981 3.32672V7.99927V8.49919L7.99973 8.49927L12.6731 8.49997Z" fill="#FAE0DA" stroke="#FAE0DA"/>
  </svg>

)

export const DurationIcon16Taken = () => (
  <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M5.38863 4.09214C6.16146 3.57575 7.07004 3.30011 7.99951 3.30005C9.24564 3.30156 10.4403 3.79726 11.3214 4.67841C12.2027 5.55962 12.6984 6.7544 12.6998 8.00063C12.6997 8.93 12.4241 9.83848 11.9077 10.6112C11.3913 11.3841 10.6572 11.9865 9.79842 12.3423C8.93961 12.698 7.99459 12.7911 7.08288 12.6097C6.17117 12.4284 5.33371 11.9808 4.67641 11.3235C4.0191 10.6661 3.57147 9.82868 3.39012 8.91697C3.20877 8.00526 3.30184 7.06025 3.65757 6.20144C4.01331 5.34262 4.61572 4.60858 5.38863 4.09214Z" stroke="#BD7269"/>
    <path d="M7.9998 2.80005C5.12792 2.80005 2.7998 5.12817 2.7998 8.00005H7.9998V2.80005Z" fill="#BD7269"/>
  </svg>

)

export const UserCircleTaken = () => (
  <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd" d="M2.40002 8.1199C2.40002 4.96083 4.96096 2.3999 8.12002 2.3999C11.2791 2.3999 13.84 4.96083 13.84 8.1199C13.84 11.279 11.2791 13.8399 8.12002 13.8399C4.96096 13.8399 2.40002 11.279 2.40002 8.1199Z" fill="#BD7269"/>
    <g clipPath="url(#clip0_565_275829)">
      <mask id="mask0_565_275829" style={{ maskType: "alpha" }} maskUnits="userSpaceOnUse" x="3" y="4" width="10" height="16">
        <path fillRule="evenodd" clipRule="evenodd" d="M9.9598 6.7998C9.9598 7.90437 9.06437 8.7998 7.9598 8.7998C6.85523 8.7998 5.9598 7.90437 5.9598 6.7998C5.9598 5.69524 6.85523 4.7998 7.9598 4.7998C9.06437 4.7998 9.9598 5.69524 9.9598 6.7998ZM8.00001 9.83984C5.34904 9.83984 3.20001 11.9889 3.20001 14.6398C3.20001 17.2908 5.34904 19.4398 8.00001 19.4398C10.651 19.4398 12.8 17.2908 12.8 14.6398C12.8 11.9889 10.651 9.83984 8.00001 9.83984Z" fill="#B02525"/>
      </mask>
      <g mask="url(#mask0_565_275829)">
        <path fillRule="evenodd" clipRule="evenodd" d="M2.27936 8.1199C2.27936 4.96083 4.84029 2.3999 7.99936 2.3999C11.1584 2.3999 13.7194 4.96083 13.7194 8.1199C13.7194 11.279 11.1584 13.8399 7.99936 13.8399C4.84029 13.8399 2.27936 11.279 2.27936 8.1199Z" fill="#FFD5B8"/>
      </g>
    </g>
    <path d="M2.90002 8.1199C2.90002 5.23698 5.2371 2.8999 8.12002 2.8999C11.003 2.8999 13.34 5.23698 13.34 8.1199C13.34 11.0028 11.003 13.3399 8.12002 13.3399C5.2371 13.3399 2.90002 11.0028 2.90002 8.1199Z" stroke="#BD7269"/>
    <defs>
      <clipPath id="clip0_565_275829">
        <rect width="9.6" height="8.8" fill="white" transform="translate(3.20001 4.7998)"/>
      </clipPath>
    </defs>
  </svg>


)

export const UserCircleActive = () => (
  <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd" d="M2.40002 8.1199C2.40002 4.96083 4.96096 2.3999 8.12002 2.3999C11.2791 2.3999 13.84 4.96083 13.84 8.1199C13.84 11.279 11.2791 13.8399 8.12002 13.8399C4.96096 13.8399 2.40002 11.279 2.40002 8.1199Z" fill="#FAE0DA"/>
    <g clipPath="url(#clip0_565_275874)">
      <mask id="mask0_565_275874" style={{ maskType: "alpha" }} maskUnits="userSpaceOnUse" x="3" y="4" width="10" height="16">
        <path fillRule="evenodd" clipRule="evenodd" d="M9.9598 6.7998C9.9598 7.90437 9.06437 8.7998 7.9598 8.7998C6.85523 8.7998 5.9598 7.90437 5.9598 6.7998C5.9598 5.69524 6.85523 4.7998 7.9598 4.7998C9.06437 4.7998 9.9598 5.69524 9.9598 6.7998ZM8.00001 9.83984C5.34904 9.83984 3.20001 11.9889 3.20001 14.6398C3.20001 17.2908 5.34904 19.4398 8.00001 19.4398C10.651 19.4398 12.8 17.2908 12.8 14.6398C12.8 11.9889 10.651 9.83984 8.00001 9.83984Z" fill="#B02525"/>
      </mask>
      <g mask="url(#mask0_565_275874)">
        <path fillRule="evenodd" clipRule="evenodd" d="M2.27936 8.1199C2.27936 4.96083 4.84029 2.3999 7.99936 2.3999C11.1584 2.3999 13.7194 4.96083 13.7194 8.1199C13.7194 11.279 11.1584 13.8399 7.99936 13.8399C4.84029 13.8399 2.27936 11.279 2.27936 8.1199Z" fill="#FF7C5C"/>
      </g>
    </g>
    <path d="M2.90002 8.1199C2.90002 5.23698 5.2371 2.8999 8.12002 2.8999C11.003 2.8999 13.34 5.23698 13.34 8.1199C13.34 11.0028 11.003 13.3399 8.12002 13.3399C5.2371 13.3399 2.90002 11.0028 2.90002 8.1199Z" stroke="#FAE0DA"/>
    <defs>
      <clipPath id="clip0_565_275874">
        <rect width="9.6" height="8.8" fill="white" transform="translate(3.20001 4.7998)"/>
      </clipPath>
    </defs>
  </svg>

)

export const CashIcon20 = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd" d="M2.27248 5.36502C2 5.8998 2 6.59987 2 8V12C2 13.4001 2 14.1002 2.27248 14.635C2.51217 15.1054 2.89462 15.4878 3.36502 15.7275C3.8998 16 4.59987 16 6 16H14C15.4001 16 16.1002 16 16.635 15.7275C17.1054 15.4878 17.4878 15.1054 17.7275 14.635C18 14.1002 18 13.4001 18 12V8C18 6.59987 18 5.8998 17.7275 5.36502C17.4878 4.89462 17.1054 4.51217 16.635 4.27248C16.1002 4 15.4001 4 14 4H6C4.59987 4 3.8998 4 3.36502 4.27248C2.89462 4.51217 2.51217 4.89462 2.27248 5.36502ZM10.5146 7C10.5146 6.72386 10.2908 6.5 10.0146 6.5C9.73851 6.5 9.51465 6.72386 9.51465 7V7.35742H9.37123C8.95446 7.35742 8.55477 7.52298 8.26007 7.81768C7.96537 8.11238 7.7998 8.51208 7.7998 8.92885C7.7998 9.34562 7.96537 9.74532 8.26007 10.04C8.55477 10.3347 8.95446 10.5003 9.37123 10.5003H10.6569C10.8085 10.5003 10.9538 10.5605 11.061 10.6676C11.1682 10.7748 11.2284 10.9202 11.2284 11.0717C11.2284 11.2233 11.1682 11.3686 11.061 11.4758C10.9538 11.5829 10.8085 11.6431 10.6569 11.6431H10.0385C10.0306 11.6428 10.0226 11.6426 10.0146 11.6426C10.0067 11.6426 9.99871 11.6428 9.99082 11.6431H8.72838C8.45223 11.6431 8.22838 11.867 8.22838 12.1431C8.22838 12.4193 8.45223 12.6431 8.72838 12.6431H9.51465V12.9997C9.51465 13.2759 9.73851 13.4997 10.0146 13.4997C10.2908 13.4997 10.5146 13.2759 10.5146 12.9997V12.6431H10.6569C11.0737 12.6431 11.4734 12.4776 11.7681 12.1829C12.0628 11.8882 12.2284 11.4885 12.2284 11.0717C12.2284 10.6549 12.0628 10.2552 11.7681 9.96054C11.4734 9.66584 11.0737 9.50028 10.6569 9.50028H9.37123C9.21968 9.50028 9.07434 9.44007 8.96717 9.33291C8.86001 9.22575 8.7998 9.0804 8.7998 8.92885C8.7998 8.7773 8.86001 8.63195 8.96717 8.52479C9.07434 8.41763 9.21968 8.35742 9.37123 8.35742H11.2998C11.5759 8.35742 11.7998 8.13356 11.7998 7.85742C11.7998 7.58128 11.5759 7.35742 11.2998 7.35742H10.5146V7Z" fill="#BAB9B8"/>
  </svg>

)

export const CardIcon20 = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd" d="M2.27248 5.36502C2.08303 5.73684 2.0253 6.18855 2.00771 6.8999H17.9923C17.9747 6.18855 17.917 5.73684 17.7275 5.36502C17.4878 4.89462 17.1054 4.51217 16.635 4.27248C16.1002 4 15.4001 4 14 4H6C4.59987 4 3.8998 4 3.36502 4.27248C2.89462 4.51217 2.51217 4.89462 2.27248 5.36502ZM18 8.0999H2V12C2 13.4001 2 14.1002 2.27248 14.635C2.51217 15.1054 2.89462 15.4878 3.36502 15.7275C3.8998 16 4.59987 16 6 16H14C15.4001 16 16.1002 16 16.635 15.7275C17.1054 15.4878 17.4878 15.1054 17.7275 14.635C18 14.1002 18 13.4001 18 12V8.0999ZM4.5 12.8998C4.5 12.5684 4.76863 12.2998 5.1 12.2998H7.9C8.23137 12.2998 8.5 12.5684 8.5 12.8998C8.5 13.2312 8.23137 13.4998 7.9 13.4998H5.1C4.76863 13.4998 4.5 13.2312 4.5 12.8998Z" fill="#BAB9B8"/>
  </svg>

)

export const ApplePayIcon20 = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M12.8258 6.12795C11.6867 6.12795 10.7773 6.80759 10.1838 6.80759C9.56158 6.80759 8.73835 6.16624 7.75239 6.16624C5.89534 6.16624 4 7.7074 4 10.6174C4 12.4458 4.69879 14.3602 5.56031 15.5951C6.30696 16.6385 6.95788 17.5 7.88641 17.5C8.81493 17.5 9.22655 16.8874 10.3752 16.8874C11.5335 16.8874 11.8015 17.4809 12.8258 17.4809C13.8309 17.4809 14.5105 16.5523 15.1423 15.6334C15.8602 14.5804 16.157 13.5562 16.1666 13.4987C16.1091 13.4796 14.1755 12.6946 14.1755 10.4643C14.1755 8.5402 15.6975 7.67869 15.7837 7.61168C14.7786 6.16624 13.247 6.12795 12.8258 6.12795ZM12.2897 4.89311C12.7492 4.33791 13.0842 3.57211 13.0842 2.79675C13.0842 2.69145 13.0747 2.58615 13.0555 2.5C12.2993 2.52872 11.3899 2.99777 10.8539 3.63912C10.4327 4.12731 10.0306 4.89311 10.0306 5.66847C10.0306 5.78334 10.0498 5.89821 10.0593 5.9365C10.1072 5.94608 10.1838 5.95565 10.2604 5.95565C10.94 5.95565 11.792 5.50574 12.2897 4.89311Z" fill="#BAB9B8"/>
  </svg>

)

export const PaypalIcon20 = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect x="2" y="2" width="16" height="16" fill="url(#pattern0)"/>
    <defs>
      <pattern id="pattern0" patternContentUnits="objectBoundingBox" width="1" height="1">
        <use xlinkHref="#image0_732_144176" transform="scale(0.00195312)"/>
      </pattern>
      <image id="image0_732_144176" width="512" height="512" xlinkHref="data:image/png;base64,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"/>
    </defs>
  </svg>

)

export const UberEatsIcon20 = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect width="20" height="20" rx="6" fill="url(#patternubereasts)"/>
    <defs>
      <pattern id="patternubereasts" patternContentUnits="objectBoundingBox" width="1" height="1">
        <use xlinkHref="#image0_490_219359" transform="translate(-0.501289 -0.25) scale(0.00792003)"/>
      </pattern>
      <image id="image0_490_219359" width="259" height="194" xlinkHref="data:image/png;base64,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"/>
    </defs>
  </svg>

)

export const WoltIcon20 = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect width="20" height="20" rx="6" fill="url(#patternwolt)"/>
    <defs>
      <pattern id="patternwolt" patternContentUnits="objectBoundingBox" width="1" height="1">
        <use xlinkHref="#image0_490_219374" transform="translate(-0.249064) scale(0.********)"/>
      </pattern>
      <image id="image0_490_219374" width="400" height="267" xlinkHref="data:image/png;base64,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********************************************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"/>
    </defs>
  </svg>

)

export const LieferandoIcon20 = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect width="20" height="20" rx="6" fill="url(#patternlieferando)"/>
    <defs>
      <pattern id="patternlieferando" patternContentUnits="objectBoundingBox" width="1" height="1">
        <use xlinkHref="#image0_490_219366" transform="scale(0.00657895)"/>
      </pattern>
      <image id="image0_490_219366" width="152" height="152" xlinkHref="data:image/png;base64,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"/>
    </defs>
  </svg>

)

export const OpenTableIcon20 = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect width="20" height="20" rx="6" fill="url(#patternopentable)"/>
    <defs>
      <pattern id="patternopentable" patternContentUnits="objectBoundingBox" width="1" height="1">
        <use xlinkHref="#image0_565_252083" transform="scale(0.005)"/>
      </pattern>
      <image id="image0_565_252083" width="200" height="200" xlinkHref="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMgAAADICAYAAACtWK6eAAAYGElEQVR4Ae2diZ8U1bXH898kAgMqzA4MMDOCgAJKNA9FjTFKjISYaDT6nnFJfPrUmPCixqdREQUNIWqMUROjydM8NGqCuDDDbMzCLMy+rz3nfc6pe6trOtO3q7urqdtTv/l8erp6qeqqU7/vOefuXzpRXEl4wAbQwPwa+BIMM79hYBfYhTUAQBBBkUEYNABADMZBFEEUASAABBHEoAEAYjAOIggiCAABIIggBg0AEINxEEEQQQAIAEEEMWgAgBiMgwiCCAJAAAgiiEEDAMRgHEQQRBAAAkAQQQwaACAG4yCCIIIAEACCCGLQAAAxGAcRBBEEgAAQRBCDBgCIwTiIIIggAASAIIIYNABADMZBBEEEASAABBHEoAEAYjAOIggiCAABIIggBg0AEINxEEEQQQAIAEEEMWgAgBiMgwiCCAJAAAgiiEEDAMRgHEQQRBAAAkAQQQwaACAG4yCCIIIAEACCCGLQAAAxGAcRBBEEgAAQRBCDBgCIwTiIIIggAASAIIIYNABADMZBBEEEASAABBHEoAEAYjAOIggiCAABIIggBg0AEINxEEEQQQAIAEEEMWgAgBiMgwiCCAJAAAgiiEEDAMRgHEQQRBAAAkAQQQwaACAG4yCCIIIAEACCCGLQAAAxGAcRBBEEgAAQRBCDBgCIwTiIIIggAASAIIIYNABADMbJqwhSUkUnkj0WyjWGcB0AJASjZwweA1BaTXVl58njRFm1AwVfQ+Faql2xhmqXV1DtuRXO84o18r78XkmVux/vz8cRoPLp+kM4VwASgtHTAkQBIYJesZZqlpVTzaJiOn5WEdUsLqHac1ZJilRfsYnqq7ZQw4aLqXHjV+WZX9dVbKQTRZVUe/ZKqlms9ltUTLXLVjrweI9vuy1COD8AEoLRUwKiRVu0jmqWltPxswqpZmkZ1a/ZTM07r6WOO+6l3qeeo6E336axfx6jyaZmmu46TTMDgxQbHaPZ8Ql5nukfoOnO0zTZeJLGPj5KQ6+/Rb1PPksdt99DzTuuobrVG6mmoMyBbVm5gOZGFxvtEsI5AZAQjD4vICoF4rSHYeAIwVGj5evXU/cjT9Dokb/TdE8vBfnHUI28e4S6f/6YgMfnJZGJYfGcz7zna4vdcnweACTHBk4pLi3EFWtEnFyGaPnGDdT3/CGabG6dl4fZmRnSD4rFSB6zs0TJHuo77j4zsXmPy5Gmb98Bat55naRunMpx2UaiCpd/wrZVCL8PQEIwughNgcFAHP9KIdWvvYC67v8ZTXxRO1e8s7MCgwvB3E+ze8VAzcSc4yccaeyTz6jzrvupbtX5TjRzQYlW2wgACQEQ8ciF66Rs0XDeNikXzPT2xSXqQjEbf+9MbMX+FZbpzi7q3vs41a/Z5IBStM6JKCHYLYwIBkDO5I0udappOXWpW7meeh59kmJDw670OQWSSOG+E+JGAiwzPb10+qFfSJpVs6QkMmkXADlDgHDUqD13NdUUlFL7rXfSVHuHq/7Z6Rmn/OC+Y9GGjmbqlLjGrG3PLVJlXLt8zYKPJgAk14CosgZHjYbzt9PwX99z1W81GO5Zqg0GhUFWf0Ov/4nqKy+UthhJGXNtx5COD0ByaXhurS6ulIa99h/eRbGRUZGXVamUVrzfZ11rRkRcbmr7zi1O2US18odRTsjlbwKQHAGiUyru/tH/65dc+Qkc7qv83ZidnnZPvvep/dIyz9cq3V9yZNNcgpDs2AAkBzezrvw8aQHnrh5jn3zqCEk87xmulXIlnKMNviauKiaikb+9T3UrN0iXloWUcgGQgAGpK19PXMvTtP0K6f7B4vF62xxJNdTD6uvjhsaGjV+V7ivsJJJ55Xx6H4AECIjAsbhYWqK5T1QU4NBkaki4O0zTxTultm4hQAJAAgJE0qrFJdR8xXU0OzHhwMHtGhH607VcseFhOnnJVQsCEgASACCcc9csKaWTO66JLBzaD2hIuCdx44X/Jt3z87lMAkCyBaSsWgqmjZsvle7mLJSFUlOlRZ/us4aEG0O5rYQbSGU8S7a2DmF/AJKN0UuqZBQf195wC7PA4WlMS1dYC+n7ukwyfuwzZ2BW0bq8HMEIQLIEhMduDP/FaR3XolhIQs/mWrQ9Bl95Tbqm5GMbCQDJEBAulHM39e5fPCEa0mLIRlALcV+dbnb++AFpcc+3mi0AkgEgUigvKKOWq2+Ia1o1mMXfwJZYQNmFQWnafqWU1/KpPAJA0gWE+xxxPl1aTZMnWxwKuEUZf0ktoKPI+LHPnQJ7Ho1OBCBpAsLRg8dt9z5zQASB1CopF3M+0DVbpx9+xEm1eOqhNG0fxvcBSDo3qbRaOuWd/NrV8Zu/wLpXxS8s4C2dao2PE1eJS9VvHkQSAJImIDxNDnfM4z+dOgQspQV7OG2vwdfelCEA+dCACED8AlJWLa3lbbt/4AgY5Y7MQFaRpPmKXdLj2fYCOwDxCwg3Cp6zisaOerqvZyaRSO+lo8jwO++KwwEgfgVo8/dKVfS44WZEjyDwdqPIddJXy2ZIEEH8gMmAcNnjPZQ9AuFD9XIe/P0b1pdFAEgqQLjm6uyVNKfmKgiVRPkYbo3WBDVuvsTqGi0AkgIQ3e7R9+xBkbTOoaOs7yCuXdux+2ePWd0uAkBMgJRUSqs5jxScOtXu6EJ5vyBEEuljqFrAieO1xPNr2bpWCQAxAaIL59++ydEyqnZzwjQv6cDrnthYWM8/QNT8S5z6eB9i3IBbZnV61f/ib0UYOi3IiUqyPShHNjURNZ+nnCsDbXHE0/bseexX1qZZ+QOIXiZAFpVx1s/ghWW4X5SsabGkVJYdkzEHasK2oPru8Gwd8mdjBPEzny+fd5IlD7LlMqv9lT25bUlWygrYwQVx//MCEPbkskzAWUUy9xKvn9H54wep5/GnZWZ0nlS5bc+t1LD+IpkKkwcxzVm/z5RGJfvM9tqrhOgw3dVNI3/7gAYOv0J9+1+kgZdepdH3P5LZD10R8z4xizqPqegWGxunxk1cm1VhXVnEbkBUOqXnte3bd5CmOjrd+524ERseoeG3/iIL0PD6fbz4S6Z5LUNZs6iIOu/5L/kZnQ4k/mYYr73nwi3SbbtvdpZTW1om58wDudhmvHxb/drNdOp7t9HokQ/dU/Xu774Z1oaKIqe+f7s4N9tGHdoLiAq3PFtI508elDX33HuoZhznG60ficsG8DBPrn2S0M2rwSaLFEne14AM/PZV+VlbRKXPY6qtnVp33ehEzCWlrjPg89YPcQ6Fa53vFJQKKHodEn0c16Yhbejz6P3VfivLIXYCouDglVj7D73s3joZe2EqdOqp+lUaMVnfKGlX7dmr0o8k3Pfq3NU0/ukXzu8rT+eeTAgbWkxjHx2VlZ84SjIMxnRSl93UOJaG6q00caJezl4fL4RLif+kKhuNvHdEeitkGvHTdYB+v28nIFy9uriE+g8edm5kButnzE5Nyb6TJ5sl/ZCJlf0WAtVsJfXrLojn8CYw47c7Z1tazOOffSFtMwwvR0i/N5q/JxF12UpZLcpd/zBs8LUza251nBiP1kwS1cN43zpAJLVZXEwd//ETBw6upcmwXKkh4XIJ96Xy7Z10Af2Sq+yoJtWF2ZFRWQOdu75I5MhASAwJL+Jz8tKvx2u2woRfX9voqKyfYltB3S5A2MOvWCvrgetcObFska6L1p731I23yaTSvgqBuoHQkt67+hp4kU8ugKcbORI9L+9//MsrqPeJfXEnlK5hc/B9Xl3XtgZDqwBhr8htGtw/h/8CGe+tctyxo8dIyiI+0iyJYouKqePO/3TOI8w5dpWHnWrvdMAIYgI2lUJyecRdIzHMKKJrsr57q38nlkH0THQUfl5bBciJkkqZqXCi5oTjnwLOj3nuXC74p0q1XFB/rkANERAdPfr2vxBoLY92AtzlXJyRBdfIDkkWOLVoQgd7AFEj9pouunzOWngOKdn91yI7/cBeXyITQL5SSL1PPRe6eHSKyUudcZV3Krj9eEX+jgak4457Q79G9/48+N++7o/fawzie/YAovP+79zi0BBg9NA3gNs0OIVjcZiMpwHpe/5QuOJRac/s1DQ1bb2Mas9ZHVxLM9t7Wbks1+AYPLz/+v7wsth+7o/p3gX9mTWAuB7tRznI+1X6MPz2//ryws65FNHA4d9ZAcjMwCDVV22V9DOwbuE6Ym/jiK3WGwypHOIC8vgzACQZ4S4gd94XvCg1IDxRQEHqNEWfi12AbMkRIJcRRyjH6BnWpzt7Z/w/DsjTACQZIFz9ymv7nfrurY6hc5FivfR76avEACQ9D5Wfc3Vq3wFLUqzJKVmMJtDJ1mQSvHJqvvzajIUd1I4uII/a1+3dmhSLUweuhuXFL4Pumq1vwOmf+pv2kgFiQKwqpH/7Jl/RzwS+9zOJktwge/s9js5D7A7v3p+HUEg3em65gYXraKK+MdgoonJrvw1RAgi3x+z9pZyHvoFBecx0jqN/u/fp5wNNPwSQRUU08PJr1lxj5533oZrX68USt7UweYQZ/7mFx3QUlfhdlaqNf37c93gDRzzF1HlXDspDieeX6rXqqzTV0hZcDZbuscB9zfoHnDMIqYAuP67ukfR24GEKGfS+TtRSUK/tSbG4ZZTTrOUV1HDetsBaeLUHbv/hXdIBksWf0nhulbMl04yqSobOu+8PtKtJ9yNq8Z8QGwm9/kGmI7VsbLpdgHgasLrufUhsl00U0bUz0pV6KU8KUJUaDgZVd1bkDn02/CnvPtPXT/VVW6Qbvi/Q5+mOwfvVLiunpq073BV5Qx23rq4tPqowwLaeea4/pXNM2Mc6QOQC2IMXlNHgq6/HIUkzBdBwTHd0OaJansZwTo5kK9bICq1WpCCemeRHP/yHAMLnly4k0t2dV5wtqaSJ2joH/RAL587NdaqWp1pPOdeD7u6Vqb0458hF6yTdGnr9LdeHyyIsKUCRlErd9KlTHdR4wdec/lfp5rUq3Rv/vMb5/QCrnd0LSnNDp4sj7x4R+7ATkZ69qSapKK12upbwAKvV59PYPz6RX9bHS/M0gv26ulcj//cByVwCqa4lwcOnGxHS/b6dEYSNoCDhcdXdex+fuxZHLOYOteWb7IVC373hP/+V6tdeIHCk62nZiLwPd5wb/N0f7BGTJ5JM1DVIG4bM6MLpI9vMM9xWrpmdAs8CU8Bj1Yup9do97gR4VsDhuZ7eZ4KtpUsXhGTftxcQDUlJlTTuNW27nLgvFXe7SPbH5ZXR9z+ktj23SJuBzNiXoUdigbH43LKQJQVZvnavuAdf+QO1XHW9OBQehTl3KiRnSC6DMfSnd1yzefd33wxrQ0Xm9pvvkEoUhjyZWMN4325AVDhlsfLkCzzLSH3lFuKerRxV+g8cov4XDlPP/zxDHbffTU3bLpOZPNxerz7GfiQ1urQ0r6TmHdeEJR3z7yakfJNNzTT05p+JPXHPL5+ivn0HZIYXzu3dP05PE/ZzPwtjQ6XLsxMTTios5SOfFSlKG0nvX0Cf5wUgYoTSKkl7uHDKALB3l/RikX4udmYw4YiRbnkjmTFlNdsqio/fDqevkkm7Eg1SlMu4lsqqqKEvSME6/qm9q9/mDyBaxBwVVKGTI4v3EVhPV/VbOs0KvdOiFpTpeZ5ymS94TMfM8Wca2t4nn5XUkO2d64iQ7vHzDxANypl41g2Ge4Ifo5Jj7eXH4VXka77qW9auVwhATKBxtCpcK3NQTXd2OaJLlc7khzTDP0uVXnFtnMyAaboPIX4GQFIYX6dZ/Qd/I6LSaUH4CsvvM9B27H7EvlGE3jQMgKQAhMs7MjTVgnET+Y2E5+x17dXUFDVt2RHsUOJU9zPNzwGIH4MpSEb//rFzl8PunuHRWj5u6ugx9ObbVrZ9IIL4gcL7HR7tuLhEJn8WQdrUlpCXhDjV5S1X32Bl9xIA4hW/323dN+uLWkeSgCQjNHX0kL5X6UwH6/c+Bfw9pFg+DcqFdYkiN/07AMkIDbWTKn+0XLPb+ujBkQSA+AREwq7qfjL60T/lbmtvmI1eorSvtpdMJr7ErpGD3rTKuw1A0gSEexc3X7nL0bV9PU/s5U3XXE1PE8+eKQsbZdiR1CvgXG8DkHQA0VMCnVVE7sq3etI1e6VpxZnpkaHckVJmqLewW8l8sAGQNAGR/l7Sur6Bpto7HPGhwG6EUKdWEzV1VreaA5B0YUjyfSmwLyml1uu/7wgD3U+SA+KxTfNl37S2z9V8cPB7iCBJIEhmMP2+7oLiTi6HVGteSHT04KW6eXhCXbl9PXb1PZ3vGYBkCAhPfCDTFJ2zikY/cFrYdZ49r1Ii+Ka2x9Af3/E1afh8Ag37PQCSKSC8H1f7Lq+Qse/Tav127TEjyMOcS5YJNohkRV22k/TYzWaEZzb3KYt9AUgWxmPvJuWRpeXUtP1Kmh2fcEQS8b5a2knwOpMN5293lr7Lgyrd+aIVAMkSEIGk3Gllb/nG7viquBGFRMMRGx+XlXRlaqI8qdIFIAHAMJ8RHUjWSyG0Ta+MmzD7yJz8Y4G+0HDMTkzKylW8nEW+FcoT7y8iSIDQ8CRuMv/UrhvdSRJ0Lr5AmXAvSxfIYyMjMl8X91uTSe0CtG+ieM/EawAS8A3UkDRfcR3NDA6JgLR4XDUtsA19fTzNK3cjcSLHeusmYMgEKAASMCB8EwSSJaXUuPlS4jHX/Cfph6fRbEEwwtejylpjRz+VuYy5r1q+p1VekABIDgARSHiyu7OdNdmH3vDML2zRDI3ZQKrLG3yM/kMvE89iyUvEca2eV2D5vg1AcgSICEPV/3PK0XXvT9313/M6mqj5txiM2OgYtd92t5S73OvNpT1DODYAybXR1UR33M2i6eKdNPbxUddx+5mt3v1y2Bs8O+P0jHsWPMN846ZL4oui5mEjoJ/oBkByDYg6PuflnHLxQqU8Iba77ojt5ZMEMKa7TsvCn9y+sRBTqkRoAMgZAsRNQUqqpL2kvvJC6nvuRZqdnHS9sqReak1C982wNjypFJ9CbHSUep7YJ+uLcFW2dB/J09bxRAhMrwHImQRERxMuwK9YI6DwAj/9Bw9LPu+yoMV5pmu99CTXHkhnBgepb99B6TLCA51kpskFVhAHICFAYDK6fFaiZqs/d7WAwn2Weh59kqaaW11OZEPDwoOyggaGj6ePn3BsXor79MOPUn31VplYmjtlSg3VAi1rJLtfiCBhw6NBWV4hoHDqwuufDL76Bk1398yFhV8pLy/pGLdBaHBY4KYHf28mvjLXfLDx/MO8SFHrrhtlZSquWHDXQowYGBoYABI2IPr3FSiyZNqSUqk6ra/YRG27f0B9+1+g8WOfU2xk9F+ByeKd2NAwcQMfD/piKOpWbZBaqZqCUmnLiGLE0GDoZwCiBWrTs1r/hPN9vVgQ1xg1bLiYWr/1PTr9wF4aOPQy8eRr3FLPnp/FzgV+7vbhPiYmKTY0RNwFhFe1HXnvfZlsouu+h6n1m3tkPXqeXUQWImIoeKw9ly8iUPjWAKR6BiA2gZF4LqoNRXtyLgewd2dBi6iXljmiXr1RxM4F/qaLdkp/KO4TxV1dGqq3Sc2TrMxVUBbfl6tp1dLYLhQRTaNMkACQRFHa/NoDjCtqPt/CtSJ2jjIcEdwHv16+Rj4XEXhW5pIoASBSdosBIDYD4ffcWOipHn6Phe/NgQaAQBBzBGFKN6L4GQABIADEoAEAYjBOFD0mrrlyjsMAIABkjiAACACBIOAUfGsAEQRi8S2WKEYXAAJAAIhBAwDEYJwoekxcM8og8JhwCr41gAgCsfgWSxSjCwABIADEoAEAYjBOFD0mrhllEHhMOAXfGkAEgVh8iyWK0QWAABAAYtAAADEYJ4oeE9eMMgg8JpyCbw0ggkAsvsUSxegCQAAIADFoAIAYjBNFj4lrRhkEHhNOwbcGEEEgFt9iiWJ0ASAABIAYNABADMaJosfENaMMAo8Jp+BbA4ggEItvsUQxugAQAAJADBoAIAbjRNFj4ppRBoHHhFPwrQFEEIjFt1iiGF0ACAABIAYNABCDcaLoMXHNKIPAY8Ip+NYAIgjE4lssUYwuAASAABCDBgCIwThR9Ji4ZpRB4DHhFHxrABEEYvEtlihGFwACQACIQQMAxGCcKHpMXDPKIPCYcAq+NYAIArH4FksUowsAASAAxKABAGIwThQ9Jq4ZZRB4TDgF3xpABIFYfIslitEFgAAQAGLQAAAxGCeKHhPXjDIIPCacgm8NIIJALL7FEsXoAkAACAAxaOD/AcSZZw+YR8rMAAAAAElFTkSuQmCC"/>
    </defs>
  </svg>

)

export const QuandooIcon20 = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect width="20" height="20" rx="6" fill="url(#patternquandoo)"/>
    <defs>
      <pattern id="patternquandoo" patternContentUnits="objectBoundingBox" width="1" height="1">
        <use xlinkHref="#image0_565_252090" transform="scale(0.00444444)"/>
      </pattern>
      <image id="image0_565_252090" width="225" height="225" xlinkHref="data:image/png;base64,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"/>
    </defs>
  </svg>

)

export const GoogleIcon20 = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect width="20" height="20" rx="6" fill="white"/>
    <rect x="4" y="4" width="12" height="12" fill="url(#patternGoogle)"/>
    <defs>
      <pattern id="patternGoogle" patternContentUnits="objectBoundingBox" width="1" height="1">
        <use xlinkHref="#image0_34_2" transform="translate(-0.557895 -0.031343) scale(0.00111294 0.00107887)"/>
      </pattern>
      <image id="image0_34_2" width="1920" height="985" xlinkHref="data:image/png;base64,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**********************************************************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"/>
    </defs>
  </svg>

)

export const PlaceholderIcon20 = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M10 3C8.61553 3 7.26215 3.41054 6.11101 4.17971C4.95987 4.94888 4.06266 6.04213 3.53285 7.32122C3.00303 8.6003 2.86441 10.0078 3.13451 11.3656C3.4046 12.7235 4.07129 13.9708 5.05026 14.9497C6.02922 15.9287 7.2765 16.5954 8.63437 16.8655C9.99224 17.1356 11.3997 16.997 12.6788 16.4672C13.9579 15.9373 15.0511 15.0401 15.8203 13.889C16.5895 12.7378 17 11.3845 17 10C16.9979 8.14414 16.2597 6.36491 14.9474 5.05262C13.6351 3.74033 11.8559 3.00214 10 3Z" fill="#BAB9B8"/>
  </svg>

)

export const DeleteIcon24 = () => (
  <svg width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd" d="M15.0227 5.40104C12.9815 5.40158 10.9406 5.40212 8.89934 5.40116C8.03092 5.40075 7.21906 5.83374 6.73645 6.55649C6.02842 7.61685 5.32157 8.67809 4.61496 9.73899L4.61369 9.74089L4.0692 10.5583C3.48736 11.4318 3.48738 12.5693 4.0691 13.4427L4.61317 14.2597L4.61666 14.265L4.62075 14.2711L4.62091 14.2714C5.3256 15.3296 6.03058 16.3884 6.73709 17.4459C7.21929 18.1678 8.03008 18.6004 8.89755 18.6004L19.1621 18.6004C20.598 18.6004 21.7621 17.4363 21.7618 16.0002L21.7612 14.1779V14.1775V14.1772C21.7606 12.1188 21.7599 10.0607 21.761 8.00259C21.7617 6.5664 20.5983 5.40051 19.1613 5.40041C17.7815 5.40031 16.402 5.40067 15.0227 5.40104L15.0227 5.40104ZM16.8536 9.39693C17.0489 9.5922 17.0489 9.90878 16.8536 10.104L14.9572 12.0005L16.8536 13.8969C17.0489 14.0922 17.0489 14.4088 16.8536 14.604C16.6583 14.7993 16.3418 14.7993 16.1465 14.604L14.2501 12.7076L12.3536 14.604C12.1583 14.7993 11.8418 14.7993 11.6465 14.604C11.4512 14.4088 11.4512 14.0922 11.6465 13.8969L13.5429 12.0005L11.6465 10.104C11.4512 9.90878 11.4512 9.5922 11.6465 9.39693C11.8418 9.20167 12.1583 9.20167 12.3536 9.39693L14.2501 11.2934L16.1465 9.39693C16.3418 9.20167 16.6583 9.20167 16.8536 9.39693Z" fill="#BAB9B8"/>
  </svg>

)


export const SwitchOnIcon = () => (
  <svg width="36" height="24" viewBox="0 0 36 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <g clipPath="url(#clip0_136_25311)">
      <path d="M0 12C0 5.37258 5.37258 0 12 0H24C30.6274 0 36 5.37258 36 12V12C36 18.6274 30.6274 24 24 24H12C5.37258 24 0 18.6274 0 12V12Z" fill="#73AF9F"/>
      <g filter="url(#filter0_ddd_136_25311)">
        <circle cx="24" cy="12" r="10" fill="#F9F9F9"/>
      </g>
    </g>
    <defs>
      <filter id="filter0_ddd_136_25311" x="6" y="-2" width="36" height="36" filterUnits="userSpaceOnUse" colorInterpolationFilters="sRGB">
        <feFlood floodOpacity="0" result="BackgroundImageFix"/>
        <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
        <feOffset/>
        <feGaussianBlur stdDeviation="0.5"/>
        <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.02 0"/>
        <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_136_25311"/>
        <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
        <feOffset/>
        <feGaussianBlur stdDeviation="1"/>
        <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.04 0"/>
        <feBlend mode="normal" in2="effect1_dropShadow_136_25311" result="effect2_dropShadow_136_25311"/>
        <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
        <feOffset dy="4"/>
        <feGaussianBlur stdDeviation="4"/>
        <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.02 0"/>
        <feBlend mode="normal" in2="effect2_dropShadow_136_25311" result="effect3_dropShadow_136_25311"/>
        <feBlend mode="normal" in="SourceGraphic" in2="effect3_dropShadow_136_25311" result="shape"/>
      </filter>
      <clipPath id="clip0_136_25311">
        <path d="M0 12C0 5.37258 5.37258 0 12 0H24C30.6274 0 36 5.37258 36 12V12C36 18.6274 30.6274 24 24 24H12C5.37258 24 0 18.6274 0 12V12Z" fill="white"/>
      </clipPath>
    </defs>
  </svg>

)

export const SwitchOffIcon = () => (
  <svg width="36" height="24" viewBox="0 0 36 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <g clipPath="url(#clip0_136_25310)">
      <path d="M0 12C0 5.37258 5.37258 0 12 0H24C30.6274 0 36 5.37258 36 12V12C36 18.6274 30.6274 24 24 24H12C5.37258 24 0 18.6274 0 12V12Z" fill="#E8E7E6"/>
      <g filter="url(#filter0_ddd_136_25310)">
        <circle cx="12" cy="12" r="10" fill="#F9F9F9"/>
      </g>
    </g>
    <defs>
      <filter id="filter0_ddd_136_25310" x="-6" y="-2" width="36" height="36" filterUnits="userSpaceOnUse" colorInterpolationFilters="sRGB">
        <feFlood floodOpacity="0" result="BackgroundImageFix"/>
        <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
        <feOffset/>
        <feGaussianBlur stdDeviation="0.5"/>
        <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.02 0"/>
        <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_136_25310"/>
        <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
        <feOffset/>
        <feGaussianBlur stdDeviation="1"/>
        <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.04 0"/>
        <feBlend mode="normal" in2="effect1_dropShadow_136_25310" result="effect2_dropShadow_136_25310"/>
        <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
        <feOffset dy="4"/>
        <feGaussianBlur stdDeviation="4"/>
        <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.02 0"/>
        <feBlend mode="normal" in2="effect2_dropShadow_136_25310" result="effect3_dropShadow_136_25310"/>
        <feBlend mode="normal" in="SourceGraphic" in2="effect3_dropShadow_136_25310" result="shape"/>
      </filter>
      <clipPath id="clip0_136_25310">
        <path d="M0 12C0 5.37258 5.37258 0 12 0H24C30.6274 0 36 5.37258 36 12V12C36 18.6274 30.6274 24 24 24H12C5.37258 24 0 18.6274 0 12V12Z" fill="white"/>
      </clipPath>
    </defs>
  </svg>

)

export const CheckboxOnIcon = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect x="2" y="2" width="16" height="16" rx="6" fill="#FF7C5C"/>
    <path d="M13.1429 8L8.95236 12L6.85715 10" stroke="#F9F9F9" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
  </svg>

)

export const CheckboxOffIcon = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect x="2.5" y="2.5" width="15" height="15" rx="5.5" stroke="#D8D7D6"/>
  </svg>

)

export const DeliveryIcon20 = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd" d="M6.3837 5.25569C6.50709 4.25316 7.35863 3.5 8.36872 3.5H16.2385C17.44 3.5 18.3703 4.55183 18.2236 5.74431L17.1159 14.7443C16.9925 15.7468 16.1409 16.5 15.1308 16.5H7.26103C6.05955 16.5 5.12924 15.4482 5.27601 14.2557L6.3837 5.25569ZM9.79959 6.2998C10.0757 6.2998 10.2996 6.52366 10.2996 6.7998C10.2996 7.73869 11.0607 8.4998 11.9996 8.4998C12.9385 8.4998 13.6996 7.73869 13.6996 6.7998C13.6996 6.52366 13.9234 6.2998 14.1996 6.2998C14.4757 6.2998 14.6996 6.52366 14.6996 6.7998C14.6996 8.29097 13.4908 9.4998 11.9996 9.4998C10.5084 9.4998 9.29959 8.29097 9.29959 6.7998C9.29959 6.52366 9.52345 6.2998 9.79959 6.2998ZM0.900391 5.9999C0.900391 5.66853 1.16902 5.3999 1.50039 5.3999H4.50039C4.83176 5.3999 5.10039 5.66853 5.10039 5.9999C5.10039 6.33127 4.83176 6.5999 4.50039 6.5999H1.50039C1.16902 6.5999 0.900391 6.33127 0.900391 5.9999ZM0.900391 8.4999C0.900391 8.16853 1.16902 7.8999 1.50039 7.8999H4.00039C4.33176 7.8999 4.60039 8.16853 4.60039 8.4999C4.60039 8.83127 4.33176 9.0999 4.00039 9.0999H1.50039C1.16902 9.0999 0.900391 8.83127 0.900391 8.4999ZM1.50039 10.3999C1.16902 10.3999 0.900391 10.6685 0.900391 10.9999C0.900391 11.3313 1.16902 11.5999 1.50039 11.5999H3.50039C3.83176 11.5999 4.10039 11.3313 4.10039 10.9999C4.10039 10.6685 3.83176 10.3999 3.50039 10.3999H1.50039ZM0.900391 13.4999C0.900391 13.1685 1.16902 12.8999 1.50039 12.8999H3.00039C3.33176 12.8999 3.60039 13.1685 3.60039 13.4999C3.60039 13.8313 3.33176 14.0999 3.00039 14.0999H1.50039C1.16902 14.0999 0.900391 13.8313 0.900391 13.4999Z" fill="#BAB9B8"/>
  </svg>
)

export const ExpandIcon20 = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd" d="M6.11101 4.17971C7.26216 3.41054 8.61553 3 10 3C11.8559 3.00214 13.6351 3.74033 14.9474 5.05262C16.2597 6.36491 16.9979 8.14414 17 10C17 11.3845 16.5895 12.7378 15.8203 13.889C15.0511 15.0401 13.9579 15.9373 12.6788 16.4672C11.3997 16.997 9.99224 17.1356 8.63437 16.8655C7.2765 16.5954 6.02922 15.9287 5.05026 14.9497C4.07129 13.9708 3.4046 12.7235 3.13451 11.3656C2.86441 10.0078 3.00303 8.6003 3.53285 7.32122C4.06266 6.04213 4.95987 4.94888 6.11101 4.17971ZM7.54105 8.84664C7.34579 8.65138 7.02921 8.65138 6.83395 8.84664C6.63868 9.0419 6.63868 9.35849 6.83395 9.55375L9.64645 12.3662C9.84171 12.5615 10.1583 12.5615 10.3536 12.3662L13.1661 9.55375C13.3613 9.35849 13.3613 9.0419 13.1661 8.84664C12.9708 8.65138 12.6542 8.65138 12.4589 8.84664L10 11.3056L7.54105 8.84664Z" fill="#929191"/>
  </svg>

)

export const CollapseIcon20 = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd" d="M6.11101 4.17971C7.26215 3.41054 8.61553 3 10 3C11.8559 3.00215 13.6351 3.74033 14.9474 5.05262C16.2597 6.36491 16.9979 8.14414 17 10C17 11.3845 16.5895 12.7378 15.8203 13.889C15.0511 15.0401 13.9579 15.9373 12.6788 16.4672C11.3997 16.997 9.99224 17.1356 8.63437 16.8655C7.2765 16.5954 6.02922 15.9287 5.05026 14.9497C4.07129 13.9708 3.4046 12.7235 3.13451 11.3656C2.86441 10.0078 3.00303 8.6003 3.53285 7.32122C4.06266 6.04213 4.95987 4.94888 6.11101 4.17971ZM12.4589 11.3658C12.6542 11.561 12.9708 11.561 13.1661 11.3658C13.3613 11.1705 13.3613 10.8539 13.1661 10.6587L10.3536 7.84615C10.1583 7.65089 9.84171 7.65089 9.64645 7.84615L6.83395 10.6587C6.63868 10.8539 6.63868 11.1705 6.83395 11.3658C7.02921 11.561 7.34579 11.561 7.54105 11.3658L10 8.90681L12.4589 11.3658Z" fill="#929191"/>
  </svg>
)

export const ClockIcon16 = () => (
  <svg width="16" height="17" viewBox="0 0 16 17" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd" d="M4.66658 3.42915C5.65328 2.76986 6.81331 2.41797 8 2.41797C9.59074 2.41981 11.1158 3.05254 12.2406 4.17736C13.3654 5.30218 13.9982 6.82723 14 8.41797C14 9.60466 13.6481 10.7647 12.9888 11.7514C12.3295 12.7381 11.3925 13.5071 10.2961 13.9612C9.19975 14.4154 7.99335 14.5342 6.82946 14.3027C5.66557 14.0712 4.59648 13.4997 3.75736 12.6606C2.91825 11.8215 2.3468 10.7524 2.11529 9.58851C1.88378 8.42462 2.0026 7.21822 2.45673 6.12187C2.91085 5.02551 3.67989 4.08844 4.66658 3.42915ZM8.2998 5.61816C8.2998 5.34202 8.07595 5.11816 7.7998 5.11816C7.52366 5.11816 7.2998 5.34202 7.2998 5.61816V8.61816C7.2998 8.89431 7.52366 9.11816 7.7998 9.11816H10.7998C11.0759 9.11816 11.2998 8.89431 11.2998 8.61816C11.2998 8.34202 11.0759 8.11816 10.7998 8.11816H8.2998V5.61816Z" fill="#BAB9B8"/>
  </svg>

)

export const ClockIcon16Active = () => (
  <svg width="16" height="17" viewBox="0 0 16 17" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd" d="M4.66658 3.42915C5.65328 2.76986 6.81331 2.41797 8 2.41797C9.59074 2.41981 11.1158 3.05254 12.2406 4.17736C13.3654 5.30218 13.9982 6.82723 14 8.41797C14 9.60466 13.6481 10.7647 12.9888 11.7514C12.3295 12.7381 11.3925 13.5071 10.2961 13.9612C9.19975 14.4154 7.99335 14.5342 6.82946 14.3027C5.66557 14.0712 4.59648 13.4997 3.75736 12.6606C2.91825 11.8215 2.3468 10.7524 2.11529 9.58851C1.88378 8.42462 2.0026 7.21822 2.45673 6.12187C2.91085 5.02551 3.67989 4.08844 4.66658 3.42915ZM8.2998 5.61816C8.2998 5.34202 8.07595 5.11816 7.7998 5.11816C7.52366 5.11816 7.2998 5.34202 7.2998 5.61816V8.61816C7.2998 8.89431 7.52366 9.11816 7.7998 9.11816H10.7998C11.0759 9.11816 11.2998 8.89431 11.2998 8.61816C11.2998 8.34202 11.0759 8.11816 10.7998 8.11816H8.2998V5.61816Z" fill="#FAE0DA"/>
  </svg>

)

export const ChairIcon16 = () => (
  <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd" d="M5.69922 2.2002C4.87079 2.2002 4.19922 2.87177 4.19922 3.70019V5.5002C4.19922 6.22616 4.71494 6.83167 5.4 6.97035V8.3999H5.09961C4.27118 8.3999 3.59961 9.07148 3.59961 9.8999V11.5999H3.6002V13.9999C3.6002 14.3313 3.86882 14.5999 4.2002 14.5999C4.53157 14.5999 4.8002 14.3313 4.8002 13.9999V11.5999H11.1998V13.9999C11.1998 14.3313 11.4684 14.5999 11.7998 14.5999C12.1312 14.5999 12.3998 14.3313 12.3998 13.9999V10.3999L12.3996 10.3844V9.8999C12.3996 9.07148 11.728 8.3999 10.8996 8.3999H10.6V6.97003C11.2843 6.83075 11.7992 6.22561 11.7992 5.5002V3.7002C11.7992 2.87177 11.1276 2.2002 10.2992 2.2002H5.69922ZM6.6 8.3999H9.4V7.0002H6.6V8.3999Z" fill="#BAB9B8"/>
  </svg>

)

export const KeyboardIcon20 = () => (
  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M19.8002 9.81865L19.8001 9.20105C19.7998 8.08037 19.7997 7.52004 19.5816 7.09226C19.3898 6.7159 19.0839 6.41013 18.7074 6.21842C18.2796 6.00052 17.7193 6.00063 16.5986 6.00086C13.5321 6.00148 10.4656 6.00154 7.39913 6.00091C6.27925 6.00068 5.71931 6.00057 5.29138 6.21855C4.9151 6.41021 4.60885 6.71642 4.41714 7.09267C4.19911 7.52058 4.19915 8.0801 4.19924 9.19914C4.19939 11.0652 4.20026 12.9313 4.2003 14.7973C4.20033 15.9187 4.20034 16.4794 4.41828 16.9072C4.61006 17.2836 4.91571 17.5893 5.29211 17.7812C5.71984 17.9992 6.28011 17.9993 7.40063 17.9995C10.4681 18.0001 13.5355 18.0002 16.6029 17.9995C17.7216 17.9993 18.281 17.9992 18.7087 17.7812C19.0847 17.5896 19.3908 17.2834 19.5825 16.9074C19.8004 16.4797 19.8005 15.9205 19.8007 14.8023C19.8009 13.1411 19.8006 11.4799 19.8002 9.81865Z" fill="#BAB9B8" stroke="#BAB9B8" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M6.59766 12.002H17.3977" stroke="#F9F9F9" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M6.59766 9.36133H17.3977" stroke="#F9F9F9" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M6.59766 14.6426H7.19766" stroke="#F9F9F9" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M9.59766 14.6426H14.3977" stroke="#F9F9F9" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M16.7969 14.6426H17.3969" stroke="#F9F9F9" strokeLinecap="round" strokeLinejoin="round"/>
  </svg>
)

export const KeyboardIconLight20 = () => (
  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M19.8002 9.81865L19.8001 9.20105C19.7998 8.08037 19.7997 7.52004 19.5816 7.09226C19.3898 6.7159 19.0839 6.41013 18.7074 6.21842C18.2796 6.00052 17.7193 6.00063 16.5986 6.00086C13.5321 6.00148 10.4656 6.00154 7.39913 6.00091C6.27925 6.00068 5.71931 6.00057 5.29138 6.21855C4.9151 6.41021 4.60885 6.71642 4.41714 7.09267C4.19911 7.52058 4.19915 8.0801 4.19924 9.19914C4.19939 11.0652 4.20026 12.9313 4.2003 14.7973C4.20033 15.9187 4.20034 16.4794 4.41828 16.9072C4.61006 17.2836 4.91571 17.5893 5.29211 17.7812C5.71984 17.9992 6.28011 17.9993 7.40063 17.9995C10.4681 18.0001 13.5355 18.0002 16.6029 17.9995C17.7216 17.9993 18.281 17.9992 18.7087 17.7812C19.0847 17.5896 19.3908 17.2834 19.5825 16.9074C19.8004 16.4797 19.8005 15.9205 19.8007 14.8023C19.8009 13.1411 19.8006 11.4799 19.8002 9.81865Z" fill="none" stroke="rgba(249, 249, 249, 0.8)" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M6.59766 12.002H17.3977" stroke="#F9F9F9" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M6.59766 9.36133H17.3977" stroke="#F9F9F9" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M6.59766 14.6426H7.19766" stroke="#F9F9F9" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M9.59766 14.6426H14.3977" stroke="#F9F9F9" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M16.7969 14.6426H17.3969" stroke="#F9F9F9" strokeLinecap="round" strokeLinejoin="round"/>
  </svg>
)

export const EditIcon20 = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd" d="M12.3549 3.75C12.063 3.75 11.783 3.86596 11.5766 4.07237L4.07237 11.5766C3.97016 11.6788 3.88909 11.8002 3.83378 11.9337C3.77847 12.0672 3.75 12.2104 3.75 12.3549V15.1494C3.75 15.4413 3.86596 15.7212 4.07237 15.9276C4.27877 16.134 4.55872 16.25 4.85063 16.25H7.85233C8.01155 16.25 8.16425 16.1867 8.27684 16.0742L15.9276 8.42337C16.134 8.21696 16.25 7.93701 16.25 7.64511C16.25 7.3532 16.134 7.07326 15.9276 6.86685L13.1332 4.07237C12.9267 3.86596 12.6468 3.75 12.3549 3.75ZM12.3545 4.99175L15.0074 7.64473L13.8549 8.79725L11.2022 6.14405L12.3545 4.99175Z" fill="#BAB9B8" />
  </svg>
)

export const EditIcon20Light = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd" d="M12.3549 3.75C12.063 3.75 11.783 3.86596 11.5766 4.07237L4.07237 11.5766C3.97016 11.6788 3.88909 11.8002 3.83378 11.9337C3.77847 12.0672 3.75 12.2104 3.75 12.3549V15.1494C3.75 15.4413 3.86596 15.7212 4.07237 15.9276C4.27877 16.134 4.55872 16.25 4.85063 16.25H7.85233C8.01155 16.25 8.16425 16.1867 8.27684 16.0742L15.9276 8.42337C16.134 8.21696 16.25 7.93701 16.25 7.64511C16.25 7.3532 16.134 7.07326 15.9276 6.86685L13.1332 4.07237C12.9267 3.86596 12.6468 3.75 12.3549 3.75ZM12.3545 4.99175L15.0074 7.64473L13.8549 8.79725L11.2022 6.14405L12.3545 4.99175Z" fill="#F9F9F9" fillOpacity="0.8"/>
  </svg>
)

export const CopyIcon20 = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M12.75 13.4999H13.3C14.4201 13.4999 14.9802 13.4999 15.408 13.2819C15.7843 13.0902 16.0903 12.7842 16.282 12.4079C16.5 11.9801 16.5 11.42 16.5 10.2999V6.69991C16.5 5.5798 16.5 5.01975 16.282 4.59193C16.0903 4.2156 15.7843 3.90964 15.408 3.7179C14.9802 3.49991 14.4201 3.49991 13.3 3.49991H9.7C8.57989 3.49991 8.01984 3.49991 7.59202 3.7179C7.21569 3.90964 6.90973 4.2156 6.71799 4.59193C6.5 5.01975 6.5 5.5798 6.5 6.69991V7.24991" stroke="#BAB9B8" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M10.3 6.49991H6.7C5.5799 6.49991 5.01984 6.49991 4.59202 6.7179C4.21569 6.90964 3.90973 7.2156 3.71799 7.59193C3.5 8.01975 3.5 8.5798 3.5 9.69991V13.2999C3.5 14.42 3.5 14.9801 3.71799 15.4079C3.90973 15.7842 4.21569 16.0902 4.59202 16.2819C5.01984 16.4999 5.5799 16.4999 6.7 16.4999H10.3C11.4201 16.4999 11.9802 16.4999 12.408 16.2819C12.7843 16.0902 13.0903 15.7842 13.282 15.4079C13.5 14.9801 13.5 14.42 13.5 13.2999V9.69991C13.5 8.5798 13.5 8.01975 13.282 7.59193C13.0903 7.2156 12.7843 6.90964 12.408 6.7179C11.9802 6.49991 11.4201 6.49991 10.3 6.49991Z" fill="#BAB9B8" stroke="#BAB9B8" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
  </svg>

)

export const UserIcon16 = () => (
  <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd" d="M2 8C2 4.68629 4.68629 2 8 2C11.3137 2 14 4.68629 14 8C14 9.64935 13.3345 11.1433 12.2574 12.2279C11.1704 13.3223 9.66436 14 8 14C6.33581 14 4.82986 13.3225 3.74296 12.2282C2.66564 11.1436 2 9.64952 2 8ZM4.38614 11.4554C5.29645 12.4072 6.579 13 8 13C9.42115 13 10.7038 12.4071 11.6141 11.4552C10.6853 10.5377 9.40893 9.9714 8.00028 9.9714C6.59148 9.9714 5.31499 10.5379 4.38614 11.4554ZM10.1 6.71422C10.1 7.89768 9.14064 8.85707 7.95717 8.85707C6.7737 8.85707 5.81431 7.89768 5.81431 6.71422C5.81431 5.53075 6.7737 4.57136 7.95717 4.57136C9.14064 4.57136 10.1 5.53075 10.1 6.71422Z" fill="#BAB9B8"/>
  </svg>

)

export const TableIcon16 = () => (
  <svg width="16px" height="16px" viewBox="0 0 16 16" version="1.1">
    <g id="surface1dd">
      <path style={{ stroke: "none", fillRule: "evenodd", fill: "rgb(72.941176%,72.54902%,72.156863%)", fillOpacity: 1 }} d="M 5.726562 2.519531 C 5.460938 2.519531 5.246094 2.734375 5.246094 3 C 5.246094 3.265625 5.460938 3.480469 5.726562 3.480469 L 10.273438 3.480469 C 10.539062 3.480469 10.753906 3.265625 10.753906 3 C 10.753906 2.734375 10.539062 2.519531 10.273438 2.519531 Z M 3.480469 5.726562 C 3.480469 5.460938 3.265625 5.246094 3 5.246094 C 2.734375 5.246094 2.519531 5.460938 2.519531 5.726562 L 2.519531 10.273438 C 2.519531 10.539062 2.734375 10.753906 3 10.753906 C 3.265625 10.753906 3.480469 10.539062 3.480469 10.273438 Z M 13.480469 5.726562 C 13.480469 5.460938 13.265625 5.246094 13 5.246094 C 12.734375 5.246094 12.519531 5.460938 12.519531 5.726562 L 12.519531 10.273438 C 12.519531 10.539062 12.734375 10.753906 13 10.753906 C 13.265625 10.753906 13.480469 10.539062 13.480469 10.273438 Z M 5.726562 12.519531 C 5.460938 12.519531 5.246094 12.734375 5.246094 13 C 5.246094 13.265625 5.460938 13.480469 5.726562 13.480469 L 10.273438 13.480469 C 10.539062 13.480469 10.753906 13.265625 10.753906 13 C 10.753906 12.734375 10.539062 12.519531 10.273438 12.519531 Z M 10.101562 4.417969 C 9.882812 4.398438 9.617188 4.398438 9.296875 4.398438 L 6.703125 4.398438 C 6.382812 4.398438 6.117188 4.398438 5.898438 4.417969 C 5.675781 4.4375 5.46875 4.476562 5.273438 4.574219 C 4.972656 4.726562 4.726562 4.972656 4.574219 5.273438 C 4.476562 5.46875 4.4375 5.675781 4.417969 5.898438 C 4.398438 6.117188 4.398438 6.382812 4.398438 6.703125 L 4.398438 9.296875 C 4.398438 9.617188 4.398438 9.882812 4.417969 10.101562 C 4.4375 10.324219 4.476562 10.53125 4.574219 10.726562 C 4.726562 11.027344 4.972656 11.273438 5.273438 11.425781 C 5.46875 11.523438 5.675781 11.5625 5.898438 11.582031 C 6.117188 11.601562 6.382812 11.601562 6.703125 11.601562 L 9.296875 11.601562 C 9.617188 11.601562 9.882812 11.601562 10.101562 11.582031 C 10.324219 11.5625 10.53125 11.523438 10.726562 11.425781 C 11.027344 11.273438 11.273438 11.027344 11.425781 10.726562 C 11.523438 10.53125 11.5625 10.324219 11.582031 10.101562 C 11.601562 9.882812 11.601562 9.617188 11.601562 9.296875 L 11.601562 6.703125 C 11.601562 6.382812 11.601562 6.117188 11.582031 5.898438 C 11.5625 5.675781 11.523438 5.46875 11.425781 5.273438 C 11.273438 4.972656 11.027344 4.726562 10.726562 4.574219 C 10.53125 4.476562 10.324219 4.4375 10.101562 4.417969 Z M 10.101562 4.417969 "/>
    </g>
  </svg>
)


export const PhoneIcon20Gray = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M7.06012 3.52725C7.27499 3.50125 7.4925 3.54578 7.67986 3.65414C7.86696 3.76234 8.01388 3.92834 8.09856 4.12717L9.35396 7.05643C9.41892 7.20799 9.44538 7.37327 9.43101 7.53754C9.41663 7.70182 9.36185 7.85998 9.27155 7.99796L8.23177 9.58669C8.70057 10.5419 9.47512 11.3131 10.4324 11.7775L11.9972 10.7341C12.1356 10.6418 12.2949 10.5855 12.4605 10.5703C12.6262 10.5551 12.793 10.5814 12.9459 10.647L15.8718 11.9009C16.0707 11.9856 16.2374 12.1328 16.3457 12.3199C16.454 12.5073 16.4986 12.7248 16.4726 12.9397L16.4722 12.943C16.3642 13.7877 15.9521 14.564 15.313 15.1267C14.6738 15.6894 13.8515 15.9998 13 15.9998C10.6131 15.9998 8.32387 15.0516 6.63604 13.3638C4.94821 11.676 4 9.38677 4 6.99982C4.00004 6.1483 4.31047 5.32597 4.87314 4.68685C5.43581 4.04772 6.21216 3.63561 7.0568 3.52766L7.06012 3.52725Z" fill="#BAB9B8"/>
  </svg>
)

export const EmailIcon20Gray = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd" d="M3.15504 5.34526C2.99204 5.64373 2.89937 5.98612 2.89931 6.35014L2.89894 8.12772L2.89894 8.12775C2.89852 9.96589 2.89809 11.8043 2.89887 13.6426C2.89936 14.8019 3.83949 15.7411 4.99855 15.7411H14.9984C16.1582 15.7411 17.0984 14.8009 17.0984 13.6411V6.35024C17.0984 5.98599 17.0056 5.6434 16.8425 5.34479L10.3861 10.8084C10.1624 10.9977 9.83462 10.9977 9.61091 10.8084L3.15504 5.34526ZM4.01053 4.49719L9.99849 9.56434L15.9868 4.49692C15.6922 4.33949 15.3557 4.25024 14.9984 4.25024H4.99944C4.64189 4.25024 4.30522 4.3396 4.01053 4.49719Z" fill="#BAB9B8"/>
  </svg>

)

export const EnvelopeIcon20 = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd" d="M3.51708 4.8643C3.13645 5.24439 2.90096 5.76978 2.90087 6.35014L2.9005 8.12772L2.9005 8.12775C2.90007 9.96589 2.89965 11.8043 2.90043 13.6426C2.90091 14.8019 3.84105 15.7411 5.00011 15.7411H14.9999C16.1597 15.7411 17.0999 14.8009 17.0999 13.6411V6.35024C17.0999 5.76963 16.8643 5.24406 16.4835 4.86392L10.0001 10.3503L3.51708 4.8643ZM3.53119 4.85034H16.4697C16.091 4.47913 15.5722 4.25024 14.9999 4.25024H5.001C4.42874 4.25024 3.90997 4.47913 3.53119 4.85034Z" fill="#BAB9B8"/>
  </svg>

)

export const CloseIcon20Gray = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd" d="M17 10C17 13.866 13.866 17 10 17C6.13401 17 3 13.866 3 10C3 6.13401 6.13401 3 10 3C13.866 3 17 6.13401 17 10ZM12.3536 7.64645C12.5488 7.84171 12.5488 8.15829 12.3536 8.35355L10.7071 10L12.3536 11.6464C12.5488 11.8417 12.5488 12.1583 12.3536 12.3536C12.1583 12.5488 11.8417 12.5488 11.6464 12.3536L10 10.7071L8.35355 12.3536C8.15829 12.5488 7.84171 12.5488 7.64645 12.3536C7.45118 12.1583 7.45118 11.8417 7.64645 11.6464L9.29289 10L7.64645 8.35355C7.45118 8.15829 7.45118 7.84171 7.64645 7.64645C7.84171 7.45118 8.15829 7.45118 8.35355 7.64645L10 9.29289L11.6464 7.64645C11.8417 7.45118 12.1583 7.45118 12.3536 7.64645Z" fill="#BAB9B8"/>
  </svg>
)

export const CheckIcon20Green = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M10 17C13.866 17 17 13.866 17 10C17 6.13401 13.866 3 10 3C6.13401 3 3 6.13401 3 10C3 13.866 6.13401 17 10 17Z" fill="#4D9985"/>
    <path d="M12.75 8.25L9.08331 11.75L7.25 10" stroke="#F9F9F9" strokeLinecap="round" strokeLinejoin="round"/>
  </svg>
)

export const WarningIcon20Red = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M10 17C13.866 17 17 13.866 17 10C17 6.13401 13.866 3 10 3C6.13401 3 3 6.13401 3 10C3 13.866 6.13401 17 10 17Z" fill="#F06060"/>
    <path d="M10 6.5V10.5" stroke="#F9F9F9" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M10 14C10.4142 14 10.75 13.6642 10.75 13.25C10.75 12.8358 10.4142 12.5 10 12.5C9.58579 12.5 9.25 12.8358 9.25 13.25C9.25 13.6642 9.58579 14 10 14Z" fill="#F9F9F9"/>
  </svg>
)

export const AlloAvatar32 = () => (
  <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect y="-0.00012207" width="32" height="32" rx="16" fill="#FF7C5C"/>
    <path d="M13.1227 20.1667L14.4661 20.1667L14.4661 11.8348L13.1227 11.8348L13.1227 20.1667Z" fill="#F9F9F9"/>
    <path fillRule="evenodd" clipRule="evenodd" d="M22.3405 20.3296C24.7306 20.3296 26.6682 18.392 26.6682 16.0018C26.6682 13.6117 24.7306 11.6741 22.3405 11.6741C19.9503 11.6741 18.0127 13.6117 18.0127 16.0018C18.0127 18.392 19.9503 20.3296 22.3405 20.3296ZM22.3403 18.9863C23.9885 18.9863 25.3247 17.6502 25.3247 16.002C25.3247 14.3538 23.9885 13.0176 22.3403 13.0176C20.6921 13.0176 19.356 14.3538 19.356 16.002C19.356 17.6502 20.6921 18.9863 22.3403 18.9863Z" fill="#F9F9F9"/>
    <path d="M15.6494 20.1543H16.9928V11.8348C16.2509 11.8348 15.6494 12.4363 15.6494 13.1783V20.1543Z" fill="#F9F9F9"/>
    <path fillRule="evenodd" clipRule="evenodd" d="M10.5999 14.4536C10.0825 14.0204 9.44175 13.7216 8.63742 13.7216C6.8128 13.7216 5.33365 15.2007 5.33365 17.0253C5.33365 18.8499 6.8128 20.3291 8.63742 20.3291C9.44155 20.3291 10.0826 20.0328 10.5999 19.5997L10.6392 20.1676H11.9412L11.9412 13.8834L10.6396 13.8834L10.5999 14.4536ZM10.5997 17.0255C10.5997 18.1093 9.7211 18.9878 8.63731 18.9878C7.55351 18.9878 6.67492 18.1093 6.67492 17.0255C6.67492 15.9417 7.55351 15.0631 8.63731 15.0631C9.7211 15.0631 10.5997 15.9417 10.5997 17.0255Z" fill="#F9F9F9"/>
  </svg>

)

export const AlloNavLogo54x22 = () => (
  <svg width="54" height="22" viewBox="0 0 54 22" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M19.7169 21.5893H23.1173L23.1173 0.41181L19.7169 0.41181L19.7169 21.5893Z" fill="white"/>
    <path fillRule="evenodd" clipRule="evenodd" d="M43.0454 22C49.0955 22 54 17.0751 54 11C54 4.92487 49.0955 0 43.0454 0C36.9954 0 32.0909 4.92487 32.0909 11C32.0909 17.0751 36.9954 22 43.0454 22ZM43.0451 18.5858C47.2171 18.5858 50.5992 15.1897 50.5992 11.0004C50.5992 6.81105 47.2171 3.41494 43.0451 3.41494C38.873 3.41494 35.491 6.81105 35.491 11.0004C35.491 15.1897 38.873 18.5858 43.0451 18.5858Z" fill="white"/>
    <path d="M26.1082 21.5578H29.5087V0.41181C27.6306 0.41181 26.1082 1.94059 26.1082 3.82643V21.5578Z" fill="white"/>
    <path fillRule="evenodd" clipRule="evenodd" d="M13.3301 7.06598C12.0206 5.96505 10.3986 5.2054 8.36262 5.2054C3.74407 5.2054 0 8.965 0 13.6027C0 18.2404 3.74407 22 8.36262 22C10.3981 22 12.0206 21.247 13.3301 20.1461L13.4295 21.5895H16.7254L16.7254 5.61686L13.4307 5.61686L13.3301 7.06598ZM13.3296 13.603C13.3296 16.3577 11.1057 18.5909 8.36234 18.5909C5.619 18.5909 3.39508 16.3577 3.39508 13.603C3.39508 10.8483 5.619 8.61516 8.36234 8.61516C11.1057 8.61516 13.3296 10.8483 13.3296 13.603Z" fill="white"/>
  </svg>

)

export const AlloNavLogo54x22Black = () => (
  <svg width="54" height="22" viewBox="0 0 54 22" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M19.7169 21.5893H23.1173L23.1173 0.41181L19.7169 0.41181L19.7169 21.5893Z" fill="#333332"/>
    <path fillRule="evenodd" clipRule="evenodd" d="M43.0454 22C49.0955 22 54 17.0751 54 11C54 4.92487 49.0955 0 43.0454 0C36.9954 0 32.0909 4.92487 32.0909 11C32.0909 17.0751 36.9954 22 43.0454 22ZM43.0451 18.5858C47.2171 18.5858 50.5992 15.1897 50.5992 11.0004C50.5992 6.81105 47.2171 3.41494 43.0451 3.41494C38.873 3.41494 35.491 6.81105 35.491 11.0004C35.491 15.1897 38.873 18.5858 43.0451 18.5858Z" fill="#333332"/>
    <path d="M26.1082 21.5578H29.5087V0.41181C27.6306 0.41181 26.1082 1.94059 26.1082 3.82643V21.5578Z" fill="#333332"/>
    <path fillRule="evenodd" clipRule="evenodd" d="M13.3301 7.06598C12.0206 5.96505 10.3986 5.2054 8.36262 5.2054C3.74407 5.2054 0 8.965 0 13.6027C0 18.2404 3.74407 22 8.36262 22C10.3981 22 12.0206 21.247 13.3301 20.1461L13.4295 21.5895H16.7254L16.7254 5.61686L13.4307 5.61686L13.3301 7.06598ZM13.3296 13.603C13.3296 16.3577 11.1057 18.5909 8.36234 18.5909C5.619 18.5909 3.39508 16.3577 3.39508 13.603C3.39508 10.8483 5.619 8.61516 8.36234 8.61516C11.1057 8.61516 13.3296 10.8483 13.3296 13.603Z" fill="#333332"/>
  </svg>

)

export const AlloNavLogo50x20Black = () => (
  <svg width="50" height="20" viewBox="0 0 50 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M17.9975 19.6248L21.1016 19.6248L21.1016 0.373047L17.9975 0.373047L17.9975 19.6248Z" fill="#333332"/>
    <path fillRule="evenodd" clipRule="evenodd" d="M39.2946 19.9995C44.8173 19.9995 49.2944 15.5225 49.2944 9.99976C49.2944 4.47705 44.8173 0 39.2946 0C33.7719 0 29.2949 4.47705 29.2949 9.99976C29.2949 15.5225 33.7719 19.9995 39.2946 19.9995ZM39.2943 16.8958C43.1027 16.8958 46.19 13.8085 46.19 10.0001C46.19 6.19172 43.1027 3.10442 39.2943 3.10442C35.4859 3.10442 32.3986 6.19172 32.3986 10.0001C32.3986 13.8085 35.4859 16.8958 39.2943 16.8958Z" fill="#333332"/>
    <path d="M23.8342 19.5962H26.9384V0.373047C25.224 0.373047 23.8342 1.76281 23.8342 3.47717V19.5962Z" fill="#333332"/>
    <path fillRule="evenodd" clipRule="evenodd" d="M12.1682 6.42406C10.9729 5.42324 9.49222 4.73267 7.63373 4.73267C3.41774 4.73267 0 8.1504 0 12.3664C0 16.5824 3.41774 20.0001 7.63373 20.0001C9.49176 20.0001 10.9729 19.3156 12.1682 18.3148L12.259 19.627H15.2676L15.2676 5.10671L12.26 5.10671L12.1682 6.42406ZM12.1678 12.3667C12.1678 14.8709 10.1377 16.901 7.63347 16.901C5.12924 16.901 3.09916 14.8709 3.09916 12.3667C3.09916 9.86246 5.12924 7.83238 7.63347 7.83238C10.1377 7.83238 12.1678 9.86246 12.1678 12.3667Z" fill="#333332"/>
  </svg>

)

export const MinusQuantity20Black = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M4.5 10H15.5" stroke="#333332" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
  </svg>
)

export const PlusQuantity20Black = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd" d="M10.5984 4.4999C10.5984 4.16853 10.3298 3.8999 9.99844 3.8999C9.66707 3.8999 9.39844 4.16853 9.39844 4.4999V9.3999H4.49844C4.16707 9.3999 3.89844 9.66853 3.89844 9.9999C3.89844 10.3313 4.16707 10.5999 4.49844 10.5999H9.39844V15.4999C9.39844 15.8313 9.66707 16.0999 9.99844 16.0999C10.3298 16.0999 10.5984 15.8313 10.5984 15.4999V10.5999H15.4984C15.8298 10.5999 16.0984 10.3313 16.0984 9.9999C16.0984 9.66853 15.8298 9.3999 15.4984 9.3999H10.5984V4.4999Z" fill="#333332"/>
  </svg>

)

export const PaymentIllustration160 = () => (
  <svg width="160" height="187" viewBox="0 0 160 187" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect x="3.48965" y="71.549" width="104" height="64" transform="rotate(-15 3.48965 71.549)" fill="#C0DAD3"/>
    <rect x="6.85428" y="84.106" width="104" height="15" transform="rotate(-15 6.85428 84.106)" fill="#2B574B"/>
    <path d="M90.2244 45.0984C91.7083 39.5604 97.4117 36.2769 102.963 37.7645L148.756 50.0346C154.307 51.5221 157.605 57.2174 156.121 62.7553L147.022 96.7131C146.162 99.923 144.105 102.682 141.275 104.422L137.87 106.516C134.604 108.524 132.231 111.707 131.238 115.411L115.357 174.68C113.873 180.218 108.17 183.501 102.618 182.014L66.8779 172.437C61.3263 170.949 58.0288 165.254 59.5127 159.716L90.2244 45.0984Z" fill="#A9A7A5"/>
    <path d="M73.5358 40.6267C75.0197 35.0888 80.7231 31.8053 86.2747 33.2928L132.067 45.5629C137.619 47.0504 140.916 52.7457 139.433 58.2837L108.721 172.902C107.237 178.439 101.534 181.723 95.9819 180.235L50.1893 167.965C44.6377 166.478 41.3402 160.782 42.8241 155.245L73.5358 40.6267Z" fill="#E8E7E6"/>
    <mask id="mask0_1071_81294" style={{ 'mask-type': 'alpha' }} maskUnits="userSpaceOnUse" x="45" y="36" width="92" height="142">
      <path d="M76.3832 42.2854C77.5373 37.9781 81.9733 35.4242 86.2912 36.5812L130.408 48.4024C134.726 49.5594 137.291 53.9891 136.137 58.2963L105.761 171.661C104.607 175.968 100.171 178.522 95.853 177.365L51.7357 165.544C47.4178 164.387 44.8531 159.957 46.0072 155.65L76.3832 42.2854Z" fill="#C4C4C4"/>
    </mask>
    <g mask="url(#mask0_1071_81294)">
      <rect x="75.0337" y="54.1387" width="58.3333" height="97.6667" transform="rotate(15 75.0337 54.1387)" fill="white"/>
      <ellipse cx="104.662" cy="56.9011" rx="2" ry="2" transform="rotate(15 104.662 56.9011)" fill="#4B4A49"/>
      <path d="M78.4073 56.9814C78.084 56.8947 77.7941 57.0552 77.7126 57.3594L77.712 57.3614C77.6358 57.6459 77.78 57.9093 78.0662 57.986C78.2707 58.0408 78.429 57.9708 78.5154 57.8671L78.5361 57.8727C78.533 57.8841 78.5289 57.8952 78.5259 57.9067C78.4377 58.1902 78.2752 58.3959 78.0386 58.3325C77.9075 58.2973 77.8339 58.2042 77.8227 58.0888L77.8224 58.0776L77.56 58.0073L77.559 58.0193C77.5451 58.2614 77.7059 58.4792 77.9797 58.5526C78.3547 58.6531 78.661 58.4147 78.7962 57.9101L78.7968 57.908C78.9414 57.3682 78.7255 57.0666 78.4073 56.9814ZM78.1874 57.7981C78.0179 57.7528 77.9284 57.5952 77.9749 57.4219L77.9754 57.4198C78.0202 57.2526 78.1867 57.1559 78.351 57.1999C78.5163 57.2442 78.6095 57.4127 78.5636 57.5841L78.563 57.5861C78.5177 57.7554 78.3557 57.8433 78.1874 57.7981Z" fill="white"/>
      <path d="M79.2777 57.82C79.3727 57.8454 79.4614 57.7913 79.4859 57.6999C79.5107 57.6075 79.4606 57.5174 79.3656 57.4919C79.2716 57.4667 79.1822 57.5195 79.1574 57.6119C79.1329 57.7033 79.1837 57.7948 79.2777 57.82ZM79.0713 58.5903C79.1663 58.6158 79.2547 58.5627 79.2795 58.4703C79.3042 58.3779 79.2542 58.2877 79.1592 58.2622C79.0652 58.2371 78.9757 58.2899 78.951 58.3823C78.9262 58.4747 78.9773 58.5651 79.0713 58.5903Z" fill="white"/>
      <path d="M80.1675 59.0988L80.4226 59.1672L80.4997 58.8796L80.7001 58.9333L80.7593 58.7122L80.5589 58.6585L80.824 57.6691L80.447 57.568C80.1616 57.8232 79.8575 58.1111 79.5739 58.3968L79.5152 58.6158L80.2445 58.8113L80.1675 59.0988ZM79.8207 58.4674L79.8249 58.4518C80.0377 58.2362 80.2845 58.0052 80.5002 57.8115L80.5157 57.8157L80.3062 58.5974L79.8207 58.4674Z" fill="white"/>
      <path d="M81.1981 59.3749L81.4647 59.4463L81.8661 57.9482L81.6006 57.8771L81.1374 58.0478L81.0698 58.3001L81.5113 58.1358L81.5289 58.1405L81.1981 59.3749Z" fill="white"/>
      <path fillRule="evenodd" clipRule="evenodd" d="M119.661 67.8764L119.51 67.8359C119.427 67.8135 119.341 67.8634 119.318 67.9473L118.966 69.2635C118.943 69.3474 118.993 69.4335 119.076 69.4559L119.227 69.4964C119.311 69.5188 119.397 69.4689 119.419 69.385L119.772 68.0687C119.794 67.9849 119.745 67.8988 119.661 67.8764ZM118.71 68.0013L118.861 68.0418C118.945 68.0642 118.994 68.1503 118.972 68.2342L118.714 69.196C118.692 69.2799 118.606 69.3298 118.522 69.3074L118.371 69.2669C118.288 69.2445 118.238 69.1585 118.261 69.0746L118.518 68.1127C118.541 68.0288 118.627 67.9789 118.71 68.0013ZM118.061 68.2072L117.91 68.1667C117.827 68.1444 117.741 68.1943 117.718 68.2781L117.555 68.8856C117.533 68.9695 117.582 69.0556 117.666 69.078L117.817 69.1185C117.9 69.1408 117.986 69.091 118.009 69.0071L118.172 68.3996C118.194 68.3157 118.145 68.2296 118.061 68.2072ZM117.274 68.322L117.123 68.2815C117.04 68.2592 116.954 68.309 116.932 68.3929L116.85 68.6967C116.828 68.7805 116.877 68.8667 116.961 68.889L117.112 68.9295C117.195 68.9519 117.281 68.902 117.303 68.8181L117.385 68.5144C117.407 68.4305 117.358 68.3444 117.274 68.322Z" fill="white"/>
      <path fillRule="evenodd" clipRule="evenodd" d="M121.636 68.7518C121.971 68.8415 122.258 69.0571 122.439 69.354C122.452 69.3768 122.481 69.3844 122.504 69.3711L122.726 69.2407C122.738 69.2339 122.746 69.2228 122.75 69.2098C122.753 69.1967 122.751 69.1829 122.745 69.1713C122.272 68.3872 121.267 68.1179 120.466 68.5607C120.454 68.5674 120.445 68.5784 120.442 68.5914C120.438 68.6044 120.44 68.6182 120.447 68.6299L120.574 68.8539C120.587 68.8769 120.616 68.8849 120.639 68.8719C120.944 68.705 121.301 68.6621 121.636 68.7518ZM121.481 69.3282C121.665 69.3775 121.824 69.4938 121.927 69.6544C121.941 69.677 121.97 69.6844 121.993 69.6711L122.215 69.5407C122.227 69.5339 122.235 69.5226 122.239 69.5095C122.242 69.4964 122.24 69.4824 122.233 69.4708C121.922 68.9713 121.279 68.7989 120.759 69.0759C120.748 69.0825 120.739 69.0936 120.735 69.1067C120.732 69.1197 120.733 69.1337 120.74 69.1455L120.867 69.3694C120.88 69.3923 120.909 69.4004 120.932 69.3878C121.102 69.3003 121.297 69.279 121.481 69.3282ZM121.727 69.8097C121.724 69.823 121.715 69.8343 121.703 69.8409L121.32 70.0662C121.308 70.0728 121.295 70.0747 121.282 70.0713C121.27 70.0679 121.259 70.0596 121.252 70.0482L121.033 69.6612C121.026 69.6495 121.024 69.6355 121.028 69.6224C121.032 69.6094 121.041 69.5984 121.053 69.5922C121.289 69.48 121.572 69.5557 121.721 69.7712C121.728 69.7826 121.73 69.7965 121.727 69.8097Z" fill="white"/>
      <path opacity="0.4" fillRule="evenodd" clipRule="evenodd" d="M124.007 69.1494L126.526 69.8242C126.665 69.8615 126.747 70.005 126.71 70.1448L126.466 71.0561C126.428 71.1959 126.285 71.279 126.146 71.2417L123.628 70.5669C123.488 70.5296 123.406 70.3861 123.443 70.2463L123.688 69.335C123.725 69.1952 123.868 69.1121 124.007 69.1494ZM123.537 69.2945C123.596 69.0709 123.825 68.9379 124.048 68.9975L126.567 69.6723C126.789 69.732 126.921 69.9616 126.861 70.1853L126.617 71.0966C126.557 71.3202 126.328 71.4532 126.105 71.3936L123.587 70.7188C123.364 70.6591 123.232 70.4294 123.292 70.2058L123.537 69.2945ZM127.091 70.7352C127.055 70.8678 126.944 70.9663 126.809 70.9852L126.971 70.3777C127.079 70.4617 127.126 70.6026 127.091 70.7352Z" fill="white"/>
      <path d="M126.384 69.949L124.067 69.3282C123.956 69.2983 123.841 69.3648 123.811 69.4767L123.621 70.1854C123.591 70.2973 123.657 70.4121 123.769 70.4419L126.086 71.0628C126.197 71.0926 126.311 71.0261 126.341 70.9143L126.531 70.2055C126.561 70.0937 126.495 69.9788 126.384 69.949Z" fill="white"/>
      <rect x="99.0741" y="42.9806" width="17.6667" height="1.66667" rx="0.833333" transform="rotate(15 99.0741 42.9806)" fill="#D9D9D9"/>
      <rect x="78.1725" y="59.811" width="22.6667" height="22.6667" transform="rotate(15 78.1725 59.811)" fill="#4D9985"/>
      <rect x="104.092" y="66.756" width="22.6667" height="22.6667" rx="11.3333" transform="rotate(15 104.092 66.756)" fill="#E8E7E6"/>
      <rect x="71.2705" y="85.569" width="22.6667" height="49.3333" rx="11.3333" transform="rotate(15 71.2705 85.569)" fill="#FFD36E"/>
      <rect x="97.0285" y="92.4708" width="22.6667" height="22.6667" rx="11.3333" transform="rotate(15 97.0285 92.4708)" fill="#8977B5"/>
      <rect x="90.1267" y="118.229" width="22.6667" height="22.6667" rx="11.3333" transform="rotate(15 90.1267 118.229)" fill="#FF8769"/>
    </g>
  </svg>

)

export const ArrowDownIconWhiteInCircle20 = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M10 3.5C8.71442 3.5 7.45772 3.88122 6.3888 4.59545C5.31988 5.30968 4.48676 6.32484 3.99479 7.51256C3.50282 8.70028 3.37409 10.0072 3.6249 11.2681C3.8757 12.529 4.49477 13.6872 5.40381 14.5962C6.31285 15.5052 7.47104 16.1243 8.73192 16.3751C9.99279 16.6259 11.2997 16.4972 12.4874 16.0052C13.6752 15.5132 14.6903 14.6801 15.4046 13.6112C16.1188 12.5423 16.5 11.2856 16.5 10C16.498 8.2767 15.8126 6.62456 14.594 5.40601C13.3754 4.18745 11.7233 3.50199 10 3.5ZM12.4746 10.7324L10.3541 12.8529C10.2601 12.9467 10.1328 12.9993 10 12.9993C9.86723 12.9993 9.73988 12.9467 9.64588 12.8529L7.52539 10.7324C7.4318 10.6386 7.37927 10.5115 7.37934 10.379C7.37941 10.2465 7.43208 10.1195 7.52577 10.0258C7.61946 9.93207 7.74652 9.87941 7.87902 9.87934C8.01152 9.87927 8.13863 9.9318 8.23242 10.0254L9.5 11.293V7.5C9.5 7.36739 9.55268 7.24021 9.64645 7.14645C9.74022 7.05268 9.86739 7 10 7C10.1326 7 10.2598 7.05268 10.3536 7.14645C10.4473 7.24021 10.5 7.36739 10.5 7.5V11.293L11.7676 10.0254C11.8614 9.9318 11.9885 9.87927 12.121 9.87934C12.2535 9.87941 12.3805 9.93207 12.4742 10.0258C12.5679 10.1195 12.6206 10.2465 12.6207 10.379C12.6207 10.5115 12.5682 10.6386 12.4746 10.7324Z" fill="#F2F2F2"/>
  </svg>

)
