import React from 'react';
import moment from 'moment';
import isEmpty from './isEmpty';

const calculateOpeningHours = (arr = []) => {
  if (isEmpty(arr)) {
    return '';
  }
  return arr.map(({ opens, closes }, index) => {
    if (!opens && !closes) {
      return '-';
    }

    const openingTime = moment.utc(opens, 'HH:mm:ss').format('HH:mm') || '-';
    const closingTime = moment.utc(closes, 'HH:mm:ss').format('HH:mm') || '';

    return `${index > 0 ? ` & ` : ''}${openingTime}–${closingTime}`;
  });
};

export default calculateOpeningHours;
