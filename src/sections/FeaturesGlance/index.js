import React from 'react';
import { Grid } from '@material-ui/core';
import Container from '@material-ui/core/Container';
import { Header, Paragraph } from '../../components/Text';
import useStyles from './styles';

const FeaturesGlance = () => {
  const classes = useStyles();

  return (
    <div>
      <section className={classes.section}>
        <Container maxWidth="lg">
          <Grid container spacing={10}>
            <Grid item md={12}>
              <div className={classes.flex}>
                <div className={classes.item}>
                  <img src="/icons/features/all-in-one.png" alt="bar" />
                  <Header>
                    It’s all happening at one place
                  </Header>
                  <Paragraph>
                    Manage POS, Pickup, Delivery, Scan-to-order, reservation, coupon, tax, etc at one place
                  </Paragraph>
                </div>
                <div className={classes.item}>
                  <img src="/icons/features/connected-with-customers.png" alt="bar" />
                  <Header>
                    Stay connected with your customers
                  </Header>
                  <Paragraph>
                    Engage your customer early on and make them back by staying connected with them
                  </Paragraph>
                </div>
                <div className={classes.item}>
                  <img src="/icons/features/anywhere-anytime.png" alt="bar" />
                  <Header>
                    Keep track of your business anytime
                  </Header>
                  <Paragraph>
                    Monitor your business on any device, anytime and anywhere with just few clicks
                  </Paragraph>
                </div>
              </div>
            </Grid>
          </Grid>
        </Container>
      </section>
    </div>
  );
};

export default FeaturesGlance;
