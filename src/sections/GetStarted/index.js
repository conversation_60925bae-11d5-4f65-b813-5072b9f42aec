import React from 'react';
import { Grid } from '@material-ui/core';
import Container from '@material-ui/core/Container';
import { Header, Paragraph, SectionTitle } from '../../components/Text';
import { CheckIconRound } from '../../components/Icons';
import TextField from '../../components/TextField';
import { withTranslation } from '../../../i18n';
import useStyles from './styles';
import { MainButton } from '../../components/Buttons';

const GetStarted = ({ t }) => {
  const classes = useStyles();

  return (
    <div>
      <section className={classes.section}>
        <Container maxWidth="lg">
          <Grid container>
            <Grid item md={6} sm={12}>
              <div className={classes.widthLimit}>
                <Grid container spacing={5}>
                  <Grid item sm={12}>
                    <SectionTitle>
                      Choose the future of gastronomy and get started with <PERSON><PERSON>
                    </SectionTitle>
                  </Grid>
                  <Grid item sm={12}>
                    <Paragraph>
                      One of our restaurant expert will get back to you as soon as possible.
                    </Paragraph>
                  </Grid>
                  <Grid item sm={12}>
                    <div className={classes.listItem}>
                      <CheckIconRound />
                      <Paragraph>
                        Simply run your restaurant
                      </Paragraph>
                    </div>
                    <div className={classes.listItem}>
                      <CheckIconRound />
                      <Paragraph>
                        Make your customer happy
                      </Paragraph>
                    </div>
                    <div className={classes.listItem}>
                      <CheckIconRound />
                      <Paragraph>
                        Make more money
                      </Paragraph>
                    </div>
                  </Grid>
                  <Grid item sm={12}>
                    <div className={classes.illustration} />
                  </Grid>
                </Grid>
              </div>
            </Grid>
            <Grid item md={6} sm={12}>
              <form className={classes.form}>
                <Grid container spacing={5}>
                  <Grid item xs={12}>
                    <Header>
                      Contact
                    </Header>
                  </Grid>
                  <Grid item xs={12}>
                    <TextField
                      label={`${t('welcome-details-firstName-field-label')}*`}
                      name="firstName"
                      variant="filled"
                      id="leviee-welcome-firstName-input"
                      autoComplete="off"
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <TextField
                      label={`${t('welcome-details-lastName-field-label')}*`}
                      name="lastName"
                      variant="filled"
                      id="leviee-welcome-lastName-input"
                      autoComplete="off"
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <TextField
                      label={t('welcome-details-street-field-label')}
                      name="street"
                      variant="filled"
                      id="leviee-welcome-street-input"
                      autoComplete="off"
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <TextField
                      label={t('welcome-details-number-field-label')}
                      variant="filled"
                      name="number"
                      id="leviee-welcome-number-input"
                      autoComplete="off"
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <TextField
                      label={t('welcome-details-zipCode-field-label')}
                      variant="filled"
                      name="zipCode"
                      id="leviee-welcome-zipCode-input"
                      autoComplete="off"
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <Paragraph>
                      By clicking submit below, you consent to allow Leviee to store and process
                      the personal information submitted above to provide you the content requested.
                    </Paragraph>
                  </Grid>
                  <Grid item xs={12}>
                    <MainButton>
                      Submit
                    </MainButton>
                  </Grid>
                </Grid>
              </form>
            </Grid>
          </Grid>
        </Container>
      </section>
    </div>
  );
};

export default withTranslation('common')(GetStarted);
