import { makeStyles } from '@material-ui/core/styles';

const useStyles = makeStyles(() => ({
  section: {
    paddingTop: 100,
    paddingBottom: 100,
  },
  widthLimit: {
    maxWidth: 486
  },
  listItem: {
    display: 'flex',
    alignItems: 'center',
    minHeight: 25,
    '& p': {
      marginLeft: 12
    },
    '& + &': {
      marginTop: 15
    }
  },
  illustration: {
    backgroundImage: 'url("/assets/illustrations/client-icons.png")',
    height: 178,
    width: '100%',
    backgroundSize: 'cover',
    backgroundPosition: 'center',
    marginLeft: 'auto'
  },
  form: {
    borderRadius: 8,
    boxShadow: '0px 2px 9px rgba(26, 35, 59, 0.1)',
    marginLeft: 'auto',
    width: '100%',
    padding: '48px 42px'
  }
}));

export default useStyles;
