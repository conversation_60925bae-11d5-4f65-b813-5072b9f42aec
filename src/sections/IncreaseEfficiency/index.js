import React from 'react';
import { Grid } from '@material-ui/core';
import Container from '@material-ui/core/Container';
import { Paragraph, SectionTitle } from '../../components/Text';
import { MainButton } from '../../components/Buttons';
import useStyles from './styles';

const IncreaseEfficiency = () => {
  const classes = useStyles();

  return (
    <div>
      <section className={classes.section}>
        <Container maxWidth="lg">
          <Grid container>
            <Grid item md={6} sm={12}>
              <div className={classes.illustration} />
            </Grid>
            <Grid item md={6} sm={12}>
              <div className={classes.widthLimit}>
                <Grid container spacing={5}>
                  <Grid item sm={12}>
                    <SectionTitle>
                      Efficiently serve your business customers during peak hours
                    </SectionTitle>
                  </Grid>
                  <Grid item sm={12}>
                    <Paragraph>
                      80% revenues of restaurants are generated in peak hours. With the pre-order of
                      Leviee RS, you will significantly increase the efficiency and
                      serve more business customers during peak hours (e.g. lunchtime).
                      Increase your sales and reduce waiting times for your customers.
                      Your customers will be thankful!
                    </Paragraph>
                  </Grid>
                  <Grid item sm={12}>
                    <div className={classes.flex}>
                      <div className={classes.item}>
                        <SectionTitle>
                          +10%
                        </SectionTitle>
                        <Paragraph>
                          Revenue during Peak Hours
                        </Paragraph>
                      </div>
                      <div className={classes.item}>
                        <SectionTitle>
                          -60%
                        </SectionTitle>
                        <Paragraph>
                          Waiting Time in Lunch Time
                        </Paragraph>
                      </div>
                    </div>
                  </Grid>
                  <Grid item sm={12}>
                    <MainButton>Try Leviee for Free</MainButton>
                  </Grid>
                </Grid>
              </div>
            </Grid>
          </Grid>
        </Container>
      </section>
    </div>
  );
};

export default IncreaseEfficiency;
