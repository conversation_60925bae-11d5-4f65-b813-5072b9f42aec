import React from 'react';
import { Grid } from '@material-ui/core';
import Container from '@material-ui/core/Container';
import { Header, Paragraph, SectionTitle } from '../../components/Text';
import { Button } from '../../components/Buttons';
import useStyles from './styles';

const CustomerBenefits = () => {
  const classes = useStyles();

  return (
    <div>
      <section className={classes.section}>
        <Container maxWidth="lg">
          <Grid container spacing={10}>
            <Grid item container md={12} spacing={5}>
              <Grid item md={12}>
                <div className={classes.centered}>
                  <SectionTitle>
                    Making your customers happier than ever before
                  </SectionTitle>
                </div>
              </Grid>
              <Grid item md={12}>
                <div className={classes.centered}>
                  <Paragraph>
                    With Leviee RS, you can stay closely connected with your customers and clearly understand what they love.
                  </Paragraph>
                </div>
              </Grid>
              <Grid item md={12}>
                <div className={classes.centered}>
                  <Button normal>
                    Try Leviee for Free
                  </Button>
                </div>
              </Grid>
            </Grid>
            <Grid item container md={12} spacing={10}>
              <Grid item container sm={12} spacing={10}>
                <Grid item md={6}>
                  <div className={classes.card}>
                    <div className={classes.illustration} />
                  </div>
                </Grid>
                <Grid item md={6}>
                  <Header>
                    Engage your customers
                  </Header>
                  <Paragraph>
                    Your customers can simply make reservation, order a
                    takeaway/deliver or purchase coupons online before they arrive at your restaurant
                  </Paragraph>
                </Grid>
              </Grid>
              <Grid item container sm={12} spacing={10} direction="row-reverse">
                <Grid item md={6}>
                  <div className={classes.card}>
                    <div className={classes.illustration} />
                  </div>
                </Grid>
                <Grid item md={6}>
                  <Header>
                    Engage your customers
                  </Header>
                  <Paragraph>
                    Your customers can simply make reservation, order a
                    takeaway/deliver or purchase coupons online before they arrive at your restaurant
                  </Paragraph>
                </Grid>
              </Grid>
              <Grid item container sm={12} spacing={10}>
                <Grid item md={6}>
                  <div className={classes.card}>
                    <div className={classes.illustration} />
                  </div>
                </Grid>
                <Grid item md={6}>
                  <Header>
                    Engage your customers
                  </Header>
                  <Paragraph>
                    Your customers can simply make reservation, order a
                    takeaway/deliver or purchase coupons online before they arrive at your restaurant
                  </Paragraph>
                </Grid>
              </Grid>
              <Grid item container sm={12} spacing={10} direction="row-reverse">
                <Grid item md={6}>
                  <div className={classes.card}>
                    <div className={classes.illustration} />
                  </div>
                </Grid>
                <Grid item md={6}>
                  <Header>
                    Engage your customers
                  </Header>
                  <Paragraph>
                    Your customers can simply make reservation, order a
                    takeaway/deliver or purchase coupons online before they arrive at your restaurant
                  </Paragraph>
                </Grid>
              </Grid>
              <Grid item container sm={12} spacing={10}>
                <Grid item md={6}>
                  <div className={classes.card}>
                    <div className={classes.illustration} />
                  </div>
                </Grid>
                <Grid item md={6}>
                  <Header>
                    Engage your customers
                  </Header>
                  <Paragraph>
                    Your customers can simply make reservation, order a
                    takeaway/deliver or purchase coupons online before they arrive at your restaurant
                  </Paragraph>
                </Grid>
              </Grid>
            </Grid>
          </Grid>
        </Container>
      </section>
    </div>
  );
};

export default CustomerBenefits;
