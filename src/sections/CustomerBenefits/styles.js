import { makeStyles } from '@material-ui/core/styles';

const useStyles = makeStyles(() => ({
  section: {
    paddingTop: 100,
    paddingBottom: 100,
    background: '#FEFAF4'
  },
  centered: {
    textAlign: 'center',
    maxWidth: 900,
    margin: '0 auto'
  },
  card: {
    width: '100%',
    height: 365,
    background: '#fff',
    boxShadow: '0px 2px 9px rgba(26, 35, 59, 0.1)',
    borderRadius: 8
  },
  illustration: {
    width: '100%',
    height: 30,
    background: '#00365F',
    borderTopRightRadius: 8,
    borderTopLeftRadius: 8
  }
}));

export default useStyles;
