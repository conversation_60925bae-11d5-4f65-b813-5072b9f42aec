import { makeStyles } from '@material-ui/core/styles';
import { fade } from '@material-ui/core';

const useStyles = makeStyles(() => ({
  section: {
    paddingTop: 250,
    paddingBottom: 150,
    background: '#FFFFFF'
  },
  centered: {
    textAlign: 'center',
    maxWidth: 900,
    margin: '0 auto'
  },
  actions: {
    maxWidth: 900,
    margin: '0 auto',
    textAlign: 'center',
    '& > button': {
      marginLeft: 10,
      marginRight: 10
    }
  },
  illustration: {
    width: '100%',
    height: 490,
    background: fade('#576487', 0.16),
    borderRadius: 8
  }
}));

export default useStyles;
