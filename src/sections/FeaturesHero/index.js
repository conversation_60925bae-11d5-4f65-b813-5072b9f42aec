import React from 'react';
import { Grid } from '@material-ui/core';
import Container from '@material-ui/core/Container';
import { PageTitle, Paragraph } from '../../components/Text';
import { Button } from '../../components/Buttons';
import useStyles from './styles';

const FeaturesHero = () => {
  const classes = useStyles();

  return (
    <div>
      <section className={classes.section}>
        <Container maxWidth="lg">
          <Grid container spacing={10}>
            <Grid item container md={12} spacing={5}>
              <Grid item md={12}>
                <div className={classes.centered}>
                  <PageTitle>
                    Make restaurant business sexy
                  </PageTitle>
                </div>
              </Grid>
              <Grid item md={12}>
                <div className={classes.centered}>
                  <Paragraph>
                    All features of <PERSON><PERSON> are closely connected together, so are you and your customers
                  </Paragraph>
                </div>
              </Grid>
              <Grid item md={12}>
                <div className={classes.actions}>
                  <Button normal>
                    Try <PERSON><PERSON> for Free
                  </Button>
                  <Button variant="outlined" normal>
                    See all functions
                  </Button>
                </div>
              </Grid>
            </Grid>
            <Grid item md={12}>
              <div className={classes.illustration} />
            </Grid>
          </Grid>
        </Container>
      </section>
    </div>
  );
};

export default FeaturesHero;
