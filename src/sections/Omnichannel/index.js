import React from 'react';
import { Grid } from '@material-ui/core';
import Container from '@material-ui/core/Container';
import { Paragraph, SectionTitle } from '../../components/Text';
import { Button } from '../../components/Buttons';
import useStyles from './styles';

const Omnichannel = () => {
  const classes = useStyles();

  return (
    <div>
      <section className={classes.section}>
        <Container maxWidth="lg">
          <Grid container spacing={10}>
            <Grid item container md={12} spacing={5}>
              <Grid item md={12}>
                <div className={classes.centered}>
                  <SectionTitle>
                    Anyone, anywhere, anytime.
                    One platform, every channel, all devices.
                  </SectionTitle>
                </div>
              </Grid>
              <Grid item md={12}>
                <div className={classes.centered}>
                  <Paragraph>
                    Restaurant owners can use Leviee RS anywhere, anytime or any smart devices.
                    Everything is kept at one place. Make your customers happy both online and offline.
                  </Paragraph>
                </div>
              </Grid>
              <Grid item md={12}>
                <div className={classes.centered}>
                  <Button normal>
                    Try Leviee for Free
                  </Button>
                </div>
              </Grid>
            </Grid>
            <Grid item md={12}>
              <div className={classes.illustration} />
            </Grid>
          </Grid>
        </Container>
      </section>
    </div>
  );
};

export default Omnichannel;
