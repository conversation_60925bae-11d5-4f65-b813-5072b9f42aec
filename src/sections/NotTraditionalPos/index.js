import React from 'react';
import { Grid } from '@material-ui/core';
import Container from '@material-ui/core/Container';
import { Paragraph, SectionTitle } from '../../components/Text';
import { Button } from '../../components/Buttons';
import useStyles from './styles';

const NotTraditionalPos = () => {
  const classes = useStyles();

  return (
    <div>
      <section className={classes.section}>
        <Container maxWidth="lg">
          <Grid container spacing={10}>
            <Grid item container md={12} spacing={5}>
              <Grid item md={12}>
                <div className={classes.centered}>
                  <SectionTitle>
                    Your next POS is much more than documenting transactions
                  </SectionTitle>
                </div>
              </Grid>
              <Grid item md={12}>
                <div className={classes.centered}>
                  <Paragraph>
                    Traditional POS is often hard to use and makes you document transactions manually & repeatedly
                    so that you can print receipt for your customers and do tax declaration.
                    Let <PERSON><PERSON> make it 100x simpler and bring you 100x more value via
                    its modern cloud-based solution
                  </Paragraph>
                </div>
              </Grid>
              <Grid item md={12}>
                <div className={classes.centered}>
                  <Button normal>
                    Get a Free Demo
                  </Button>
                </div>
              </Grid>
            </Grid>
            <Grid item md={12}>
              <div className={classes.illustration} />
            </Grid>
          </Grid>
        </Container>
      </section>
    </div>
  );
};

export default NotTraditionalPos;
