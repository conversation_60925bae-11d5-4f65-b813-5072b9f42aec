import React from 'react';
import { Grid } from '@material-ui/core';
import Container from '@material-ui/core/Container';
import { Header, Paragraph, SectionTitle } from '../../components/Text';
import { Button } from '../../components/Buttons';
import useStyles from './styles';

const RestaurantModel = () => {
  const classes = useStyles();

  return (
    <div>
      <section className={classes.section}>
        <Container maxWidth="lg">
          <Grid container spacing={10}>
            <Grid item md={12}>
              <Grid item container md={12} spacing={5}>
                <Grid item md={12}>
                  <div className={classes.centered}>
                    <SectionTitle>
                      Whatever restaurant you have, you can manage it with <PERSON><PERSON> RS
                    </SectionTitle>
                  </div>
                </Grid>
                <Grid item md={12}>
                  <div className={classes.centered}>
                    <Paragraph>
                      Leviee RS is suitable for all restaurant models: from family owned single restaurant to small or large chains,
                      from Doner kebab kiosk to fine dining restaurant, from sushi buffet to beer garden, you name it.
                    </Paragraph>
                  </div>
                </Grid>
                <Grid item md={12}>
                  <div className={classes.centered}>
                    <Button normal>
                      Get Started
                    </Button>
                  </div>
                </Grid>
              </Grid>
            </Grid>
            <Grid item container md={12} spacing={4}>
              <Grid item md={4}>
                <div className={classes.card}>
                  <div className={classes.illustration} />
                  <div className={classes.centered}>
                    <Header>
                      Full Service
                    </Header>
                    <Paragraph>
                      Customers pay after dining
                    </Paragraph>
                  </div>
                </div>
              </Grid>
              <Grid item md={4}>
                <div className={classes.card}>
                  <div className={classes.illustration} />
                  <div className={classes.centered}>
                    <Header>
                      Full Service
                    </Header>
                    <Paragraph>
                      Customers pay after dining
                    </Paragraph>
                  </div>
                </div>
              </Grid>
              <Grid item md={4}>
                <div className={classes.card}>
                  <div className={classes.illustration} />
                  <div className={classes.centered}>
                    <Header>
                      Full Service
                    </Header>
                    <Paragraph>
                      Customers pay after dining
                    </Paragraph>
                  </div>
                </div>
              </Grid>
            </Grid>
          </Grid>
        </Container>
      </section>
    </div>
  );
};

export default RestaurantModel;
