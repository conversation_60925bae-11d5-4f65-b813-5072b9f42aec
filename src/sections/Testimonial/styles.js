import { makeStyles } from '@material-ui/core/styles';

const useStyles = makeStyles(() => ({
  section: {
    paddingTop: 100,
    paddingBottom: 100
  },
  centered: {
    textAlign: 'center',
    maxWidth: 900,
    margin: '0 auto'
  },
  card: {
    width: '100%',
    height: 520,
    background: '#fff',
    borderRadius: 8,
    boxShadow: '0px 2px 9px rgba(26, 35, 59, 0.1)',
  },
  img: {
    backgroundImage: 'linear-gradient(180deg, rgba(28, 35, 57, 0) 0%, #1C2339 100%), url("/assets/hero/indian-food-on-restaurant-table.jpg")',
    height: 520,
    width: '100%',
    maxWidth: 300,
    backgroundSize: 'cover',
    backgroundPosition: 'center',
    borderTopLeftRadius: 8,
    borderBottomLeftRadius: 8
  },
  flex: {
    display: 'flex',
    alignItems: 'center'
  },
  text: {
    marginRight: 60,
    marginLeft: 60
  },
  border: {
    border: '2px solid #E3E8ED',
    padding: '20px 30px',
    borderRadius: 6
  }
}));

export default useStyles;
