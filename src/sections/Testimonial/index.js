import React from 'react';
import Link from 'next/link';
import { Grid } from '@material-ui/core';
import Container from '@material-ui/core/Container';
import { Paragraph, SectionTitle } from '../../components/Text';
import useStyles from './styles';

const Testimonial = () => {
  const classes = useStyles();

  return (
    <div>
      <section className={classes.section}>
        <Container maxWidth="lg">
          <Grid container spacing={10}>
            <Grid item md={12}>
              <div className={classes.centered}>
                <SectionTitle>
                  Successful customers trust Leviee
                </SectionTitle>
              </div>
            </Grid>
            <Grid item md={12}>
              <div className={classes.card}>
                <div className={classes.flex}>
                  <div className={classes.img} />
                  <div className={classes.text}>
                    <Grid container spacing={5}>
                      <Grid item>
                        <Paragraph>
                          Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor
                          incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam,
                          quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.
                        </Paragraph>
                      </Grid>
                      <Grid item>
                        <div className={classes.border}>
                          <div className={classes.flex}>
                            <div>
                              <Paragraph>100%</Paragraph>
                              <Paragraph>Satisfaction</Paragraph>
                            </div>
                            <hr />
                            <div>
                              <Paragraph>23%+</Paragraph>
                              <Paragraph>Sales Growth</Paragraph>
                            </div>
                          </div>
                        </div>
                      </Grid>
                      <Grid item md={12}>
                        <Link href="#" as="#">
                          <a>
                            Learn More
                          </a>
                        </Link>
                      </Grid>
                    </Grid>
                  </div>
                </div>
              </div>
            </Grid>
          </Grid>
        </Container>
      </section>
    </div>
  );
};

export default Testimonial;
