import React from 'react';
import { Grid } from '@material-ui/core';
import Container from '@material-ui/core/Container';
import { Header, PageTitle } from '../../components/Text';
import useStyles from './styles';

const PricingHero = () => {
  const classes = useStyles();

  return (
    <div>
      <section className={classes.section}>
        <Container maxWidth="lg">
          <Grid container spacing={10}>
            <Grid item container md={12} spacing={5}>
              <Grid item md={12}>
                <div className={classes.centered}>
                  <PageTitle>
                    Only pay for what you need: modular pricing from 29€/month
                  </PageTitle>
                </div>
              </Grid>
              <Grid item md={12}>
                <div className={classes.flex}>
                  <div className={classes.item}>
                    <img src="/icons/features/one-month-cancellation-period.png" alt="one-month-cancellation-period" />
                    <Header>
                      One Month Cancellation Period
                    </Header>
                  </div>
                  <div className={classes.item}>
                    <img src="/icons/features/one-month-free-trial.png" alt="one-month-free-trial" />
                    <Header>
                      One Month
                      <br />
                      Free Trial
                    </Header>
                  </div>
                  <div className={classes.item}>
                    <img src="/icons/features/no-minimum-contract-period.png" alt="no-minimum-contract-period" />
                    <Header>
                      No Minimum Contract Period
                    </Header>
                  </div>
                </div>
              </Grid>
            </Grid>
          </Grid>
        </Container>
      </section>
    </div>
  );
};

export default PricingHero;
