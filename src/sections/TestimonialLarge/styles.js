import { makeStyles } from '@material-ui/core/styles';
import { colors } from '../../../styles/theme';

const useStyles = makeStyles(() => ({
  section: {
    paddingTop: 100,
    paddingBottom: 100
  },
  centered: {
    textAlign: 'center',
    maxWidth: 900,
    margin: '0 auto'
  },
  card: {
    width: '100%',
    height: 520,
    background: colors.leviee.main.green,
    borderRadius: 8
  },
  img: {
    backgroundImage: 'url("/assets/hero/large-testimonial-example.png")',
    height: 520,
    width: '100%',
    backgroundSize: 'cover',
    backgroundPosition: 'center',
    borderTopRightRadius: 8,
    borderBottomRightRadius: 8
  },
  flex: {
    display: 'flex',
    alignItems: 'center'
  },
  text: {
    marginRight: 60,
    marginLeft: 60,
    color: '#fff'
  },
  border: {
    border: '2px solid #E3E8ED',
    padding: '20px 30px',
    borderRadius: 6
  }
}));

export default useStyles;
