import React from 'react';
import { Grid } from '@material-ui/core';
import Container from '@material-ui/core/Container';
import { Header, Paragraph } from '../../components/Text';
import useStyles from './styles';

const TestimonialLarge = () => {
  const classes = useStyles();

  return (
    <div>
      <section className={classes.section}>
        <Container maxWidth="lg">
          <Grid container spacing={10}>
            <Grid item md={12}>
              <div className={classes.card}>
                <div className={classes.flex}>
                  <div className={classes.text}>
                    <Grid container spacing={5}>
                      <Grid item>
                        <Header>
                          “Leviee RS is very intuitive. It took me less than 15min to learn and use it.”
                        </Header>
                      </Grid>
                      <Grid item>
                        <Paragraph>
                          <PERSON>, Waiter at Seen Restaurant
                        </Paragraph>
                      </Grid>
                    </Grid>
                  </div>
                  <div className={classes.img} />
                </div>
              </div>
            </Grid>
          </Grid>
        </Container>
      </section>
    </div>
  );
};

export default TestimonialLarge;
