import React from 'react';
import { Grid } from '@material-ui/core';
import Container from '@material-ui/core/Container';
import { Paragraph, SectionTitle } from '../../components/Text';
import { MainButton } from '../../components/Buttons';
import useStyles from './styles';

const LoyaltyProgram = () => {
  const classes = useStyles();

  return (
    <div>
      <section className={classes.section}>
        <Container maxWidth="lg">
          <Grid container>
            <Grid item md={6} sm={12}>
              <div className={classes.widthLimit}>
                <Grid container spacing={5}>
                  <Grid item sm={12}>
                    <SectionTitle>
                      Make your customers come back and fall in love with you
                    </SectionTitle>
                  </Grid>
                  <Grid item sm={12}>
                    <Paragraph>
                      You can design and customize a loyalty program for your
                      recurring & loyal customers to ensure the stability and
                      up-side potential of your revenue. (Available from April.)
                    </Paragraph>
                  </Grid>
                  <Grid item sm={12}>
                    <MainButton>Try <PERSON><PERSON> for free</MainButton>
                  </Grid>
                </Grid>
              </div>
            </Grid>
            <Grid item md={6} sm={12}>
              <div className={classes.illustration} />
            </Grid>
          </Grid>
        </Container>
      </section>
    </div>
  );
};

export default LoyaltyProgram;
