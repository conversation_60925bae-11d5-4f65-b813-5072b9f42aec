import React from 'react';
import { Grid } from '@material-ui/core';
import Container from '@material-ui/core/Container';
import { Paragraph, SectionTitle } from '../../components/Text';
import { MainButton } from '../../components/Buttons';
import useStyles from './styles';

const TryOnline = () => {
  const classes = useStyles();

  return (
    <div>
      <section className={classes.section}>
        <Container maxWidth="lg">
          <Grid container>
            <Grid item md={6} sm={12}>
              <div className={classes.widthLimit}>
                <Grid container spacing={5}>
                  <Grid item sm={12}>
                    <SectionTitle>
                      Try our product online in 30 seconds  and get it installed within an hour
                    </SectionTitle>
                  </Grid>
                  <Grid item sm={12}>
                    <Paragraph>
                      You can directly test our product out online in 30s.
                      Once you decide to use our product, besides a working WiFi
                      at your restaurant, you do not have to do anything else and we will take care of the rest
                    </Paragraph>
                  </Grid>
                  <Grid item sm={12}>
                    <MainButton>Try <PERSON><PERSON> for free</MainButton>
                  </Grid>
                </Grid>
              </div>
            </Grid>
            <Grid item md={6} sm={12}>
              <div className={classes.illustration} />
            </Grid>
          </Grid>
        </Container>
      </section>
    </div>
  );
};

export default TryOnline;
