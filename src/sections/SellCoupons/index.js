import React from 'react';
import { Grid } from '@material-ui/core';
import Container from '@material-ui/core/Container';
import { Paragraph, SectionTitle } from '../../components/Text';
import { MainButton } from '../../components/Buttons';
import useStyles from './styles';

const SellCoupons = () => {
  const classes = useStyles();

  return (
    <div>
      <section className={classes.section}>
        <Container maxWidth="lg">
          <Grid container>
            <Grid item md={6} sm={12}>
              <div className={classes.illustration} />
            </Grid>
            <Grid item md={6} sm={12}>
              <div className={classes.widthLimit}>
                <Grid container spacing={5}>
                  <Grid item sm={12}>
                    <SectionTitle>
                      You customers can buy coupons online like shopping on Amazon
                    </SectionTitle>
                  </Grid>
                  <Grid item sm={12}>
                    <Paragraph>
                      With Leviee RS, your customers can buy coupons online
                      or at your restaurant. (Available from March.)
                    </Paragraph>
                  </Grid>
                  <Grid item sm={12}>
                    <MainButton>Try <PERSON>ee for free</MainButton>
                  </Grid>
                </Grid>
              </div>
            </Grid>
          </Grid>
        </Container>
      </section>
    </div>
  );
};

export default SellCoupons;
