import React from 'react';
import { Grid } from '@material-ui/core';
import Container from '@material-ui/core/Container';
import { Paragraph, SectionTitle } from '../../components/Text';
import { MainButton } from '../../components/Buttons';
import useStyles from './styles';

const DigitalMenu = () => {
  const classes = useStyles();

  return (
    <div>
      <section className={classes.section}>
        <Container maxWidth="lg">
          <Grid container>
            <Grid item md={6} sm={12}>
              <div className={classes.illustration} />
            </Grid>
            <Grid item md={6} sm={12}>
              <div className={classes.widthLimit}>
                <Grid container spacing={5}>
                  <Grid item sm={12}>
                    <SectionTitle>
                      Turn your PDF menu into a simple & pretty digital menu that sells
                    </SectionTitle>
                  </Grid>
                  <Grid item sm={12}>
                    <Paragraph>
                      <PERSON><PERSON>’s digital menu can be viewed by anyone anywhere on any device. It has the modern design
                      that will improve your branding and be loved by your customers.
                      According to Google statistics, a great digital menu on your
                      google page could increase your revenue by up to 10%!
                    </Paragraph>
                  </Grid>
                  <Grid item sm={12}>
                    <MainButton>Get a digital menu for free</MainButton>
                  </Grid>
                </Grid>
              </div>
            </Grid>
          </Grid>
        </Container>
      </section>
    </div>
  );
};

export default DigitalMenu;
