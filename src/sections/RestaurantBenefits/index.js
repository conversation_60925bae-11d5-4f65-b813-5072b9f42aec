import React from 'react';
import { Grid } from '@material-ui/core';
import Container from '@material-ui/core/Container';
import { Paragraph, SectionTitle } from '../../components/Text';
import Accordion from '../../components/Accordion';
import useStyles from './styles';

const RestaurantBenefits = () => {
  const classes = useStyles();

  return (
    <div>
      <section className={classes.section}>
        <Container maxWidth="lg">
          <Grid container spacing={10}>
            <Grid item container md={12} spacing={5}>
              <Grid item md={12}>
                <div className={classes.centered}>
                  <SectionTitle>
                    You make it delicious.
                    We take care of the rest
                  </SectionTitle>
                </div>
              </Grid>
              <Grid item md={12}>
                <div className={classes.centered}>
                  <Paragraph>
                    Say goodbye to operation chaos and paperwork and have no more hustle and expensive
                    fee to use and manage different softwares. Let <PERSON><PERSON> make it simple for you.
                  </Paragraph>
                </div>
              </Grid>
            </Grid>
            <Grid item container md={12} spacing={10}>
              <Grid item md={6} sm={12}>
                <div className={classes.widthLimit}>
                  <Accordion />
                </div>
              </Grid>
              <Grid item md={6} sm={12}>
                <div className={classes.illustration} />
              </Grid>
            </Grid>
          </Grid>
        </Container>
      </section>
    </div>
  );
};

export default RestaurantBenefits;
