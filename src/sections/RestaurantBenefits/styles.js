import { makeStyles } from '@material-ui/core/styles';
import { fade } from '@material-ui/core';

const useStyles = makeStyles(() => ({
  section: {
    paddingTop: 100,
    paddingBottom: 100,
    background: fade('#EEF1F9', 0.46)
  },
  centered: {
    textAlign: 'center',
    maxWidth: 900,
    margin: '0 auto'
  },
  widthLimit: {
    maxWidth: 450
  },
  illustration: {
    backgroundImage: 'url("/assets/illustrations/restaurant-benefits.png")',
    height: '100%',
    minHeight: 556,
    width: '100%',
    backgroundSize: 'cover',
    backgroundPosition: 'center',
    marginLeft: 'auto'
  }
}));

export default useStyles;
