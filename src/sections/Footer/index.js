import React from 'react';
import Container from '@material-ui/core/Container';
import Link from 'next/link';
import { Header, Paragraph } from '../../components/Text';
import { withTranslation } from '../../../i18n';
import useStyles from './styles';

const Footer = () => {
  const classes = useStyles();

  return (
    <div>
      <section className={classes.section}>
        <Container maxWidth="lg">
          <div className={classes.flex}>
            <div>
              <Header>Leviee</Header>
            </div>
            <div>
              <Paragraph>Why Leviee</Paragraph>
              <div>
                <Link href="#" as="#">
                  <a>
                    Leviee RS vs. POS
                  </a>
                </Link>
              </div>
              <div>
                <Link href="#" as="#">
                  <a>
                    Customer-centric
                  </a>
                </Link>
              </div>
              <div>
                <Link href="#" as="#">
                  <a>
                    Boom Revenue
                  </a>
                </Link>
              </div>
            </div>
            <div>
              <Paragraph>Features</Paragraph>
            </div>
            <div>
              <Paragraph>Pricing</Paragraph>
            </div>
            <div>
              <Paragraph>Resources</Paragraph>
            </div>
            <div>
              <Paragraph>About us</Paragraph>
            </div>
          </div>
        </Container>
      </section>
    </div>
  );
};

export default withTranslation('common')(Footer);
