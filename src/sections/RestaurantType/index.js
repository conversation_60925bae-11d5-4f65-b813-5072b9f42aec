import React from 'react';
import { Grid } from '@material-ui/core';
import Container from '@material-ui/core/Container';
import { Paragraph, SectionTitle } from '../../components/Text';
import useStyles from './styles';

const RestaurantType = () => {
  const classes = useStyles();

  return (
    <div>
      <section className={classes.section}>
        <Container maxWidth="lg">
          <Grid container spacing={10}>
            <Grid item md={12}>
              <div className={classes.centered}>
                <SectionTitle>
                  Leviee RS fits the individual needs of different gastronomy concepts
                </SectionTitle>
              </div>
            </Grid>
            <Grid item xs={12}>
              <div className={classes.flex}>
                <div className={classes.item}>
                  <img src="/icons/gt/gastro-type-restaurant.png" alt="bar" />
                  <Paragraph>Restaurant</Paragraph>
                </div>
                <div className={classes.item}>
                  <img src="/icons/gt/gastro-type-cafee.png" alt="bar" />
                  <Paragraph>Café</Paragraph>
                </div>
                <div className={classes.item}>
                  <img src="/icons/gt/gastro-type-bar.png" alt="bar" />
                  <Paragraph>Bar</Paragraph>
                </div>
                <div className={classes.item}>
                  <img src="/icons/gt/gastro-type-food-truck.png" alt="bar" />
                  <Paragraph>Food Truck</Paragraph>
                </div>
                <div className={classes.item}>
                  <img src="/icons/gt/gastro-type-sushi.png" alt="bar" />
                  <Paragraph>Sushi</Paragraph>
                </div>
              </div>
              <div className={classes.flex}>
                <div className={classes.item}>
                  <img src="/icons/gt/gastro-type-bubble-tea.png" alt="bar" />
                  <Paragraph>Bubble Tea</Paragraph>
                </div>
                <div className={classes.item}>
                  <img src="/icons/gt/gastro-type-burger.png" alt="bar" />
                  <Paragraph>Burger</Paragraph>
                </div>
                <div className={classes.item}>
                  <img src="/icons/gt/gastro-type-pizza.png" alt="bar" />
                  <Paragraph>Pizza</Paragraph>
                </div>
                <div className={classes.item}>
                  <img src="/icons/gt/gastro-type-buffey.png" alt="bar" />
                  <Paragraph>Buffet</Paragraph>
                </div>
                <div className={classes.item}>
                  <img src="/icons/gt/gastro-type-beer-garten.png" alt="bar" />
                  <Paragraph>Beer Garden</Paragraph>
                </div>
              </div>
            </Grid>
          </Grid>
        </Container>
      </section>
    </div>
  );
};

export default RestaurantType;
