import { makeStyles } from '@material-ui/core/styles';
import { fade } from '@material-ui/core';

const useStyles = makeStyles(() => ({
  section: {
    paddingTop: 100,
    paddingBottom: 100,
    background: fade('#00848A', 0.04)
  },
  centered: {
    textAlign: 'center',
    maxWidth: 900,
    margin: '0 auto'
  },
  card: {
    width: '100%',
    height: 365,
    background: '#fff',
    boxShadow: '0px 2px 9px rgba(26, 35, 59, 0.1)',
    borderRadius: 8
  },
  illustration: {
    width: '100%',
    height: 225,
    background: '#F6F6F6'
  },
  flex: {
    width: '100%',
    display: 'flex',
    alignItems: 'baseline',
    justifyContent: 'space-between',
    verticalAlign: 'middle',
    '& + &': {
      marginTop: 80
    }
  },
  item: {
    textAlign: 'center',
    margin: '0 auto',
    '& p': {
      marginTop: 20
    }
  }
}));

export default useStyles;
