import { makeStyles } from '@material-ui/core/styles';
import { fade } from '@material-ui/core';

const useStyles = makeStyles(() => ({
  section: {
    paddingTop: 100,
    paddingBottom: 100,
    background: fade('#EEF1F9', 0.46)
  },
  centered: {
    textAlign: 'center',
    maxWidth: 900,
    margin: '0 auto'
  },
  flex: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between'
  },
  item: {
    maxWidth: 290,
    '& > img': {
      marginBottom: 16
    },
    '& > h4': {
      marginBottom: 16
    }
  },
  illustration: {
    width: '100%',
    height: 225,
    background: '#F6F6F6'
  }
}));

export default useStyles;
