import React from 'react';
import { Grid } from '@material-ui/core';
import Container from '@material-ui/core/Container';
import { Header, Paragraph, SectionTitle } from '../../components/Text';
import useStyles from './styles';

const SuccessGuaranteed = () => {
  const classes = useStyles();

  return (
    <div>
      <section className={classes.section}>
        <Container maxWidth="lg">
          <Grid container spacing={10}>
            <Grid item md={12}>
              <div className={classes.centered}>
                <SectionTitle>
                  We guarantee your success:
                  our support team accompanies you in every step from day one
                </SectionTitle>
              </div>
            </Grid>
            <Grid item md={12}>
              <div className={classes.flex}>
                <div className={classes.item}>
                  <Header>
                    Onboarding Manager
                  </Header>
                  <Paragraph>
                    Our onboarding manager will be with you from day one,
                    understand your needs and gives you expert advice
                  </Paragraph>
                </div>
                <div className={classes.item}>
                  <Header>
                    Customer Support
                  </Header>
                  <Paragraph>
                    Whenever you want to understand how to use our product better or
                    encounter any problems, our customer support team will be right there with you.
                  </Paragraph>
                </div>
                <div className={classes.item}>
                  <Header>
                    Knowledge Base
                  </Header>
                  <Paragraph>
                    We regularly update our knowledge base with videos and articles so that
                    you can explore the power of Leviee RS further.
                  </Paragraph>
                </div>
              </div>
            </Grid>
          </Grid>
        </Container>
      </section>
    </div>
  );
};

export default SuccessGuaranteed;
