import React from 'react';
import { Grid } from '@material-ui/core';
import Container from '@material-ui/core/Container';
import { Paragraph, SectionTitle } from '../../components/Text';
import { MainButton } from '../../components/Buttons';
import useStyles from './styles';

const IncreaseTakeawayRevenue = () => {
  const classes = useStyles();

  return (
    <div>
      <section className={classes.section}>
        <Container maxWidth="lg">
          <Grid container>
            <Grid item md={6} sm={12}>
              <div className={classes.widthLimit}>
                <Grid container spacing={5}>
                  <Grid item sm={12}>
                    <SectionTitle>
                      Increase your takeaway revenue and reduce cost to delivery platforms
                    </SectionTitle>
                  </Grid>
                  <Grid item sm={12}>
                    <Paragraph>
                      Allow your customers to order and pay directly from
                      your website or Levi<PERSON>’s platform. Save the expensive commission
                      fees paid to Lieferando or Wolt. You can manage and keep track
                      of all takeaway order in Leviee RS anytime
                    </Paragraph>
                  </Grid>
                  <Grid item sm={12}>
                    <div className={classes.flex}>
                      <div className={classes.item}>
                        <SectionTitle>
                          -80%
                        </SectionTitle>
                        <Paragraph>
                          Cost on Pickup Orders
                        </Paragraph>
                      </div>
                      <div className={classes.item}>
                        <SectionTitle>
                          +10%
                        </SectionTitle>
                        <Paragraph>
                          Customers for Pickup
                        </Paragraph>
                      </div>
                    </div>
                  </Grid>
                  <Grid item sm={12}>
                    <MainButton>Try Leviee for Free</MainButton>
                  </Grid>
                </Grid>
              </div>
            </Grid>
            <Grid item md={6} sm={12}>
              <div className={classes.illustration} />
            </Grid>
          </Grid>
        </Container>
      </section>
    </div>
  );
};

export default IncreaseTakeawayRevenue;
