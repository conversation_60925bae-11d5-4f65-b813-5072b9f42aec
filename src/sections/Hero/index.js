import React from 'react';
import { Grid } from '@material-ui/core';
import Container from '@material-ui/core/Container';
import { PageTitle, Paragraph } from '../../components/Text';
import { MainButton } from '../../components/Buttons';
import useStyles from './styles';

const Hero = () => {
  const classes = useStyles();

  return (
    <div>
      <section className={classes.section}>
        <Container maxWidth="lg">
          <Grid container>
            <Grid item md={6} sm={12}>
              <div className={classes.widthLimit}>
                <Grid container spacing={5}>
                  <Grid item sm={12}>
                    <PageTitle>
                      Running a
                      resturant has
                      never been this sexy
                    </PageTitle>
                  </Grid>
                  <Grid item sm={12}>
                    <Paragraph>
                      Leviee Restaurant System (Leviee RS) empowers you to manage restaurant anywhere, anytime with ease and fun.
                    </Paragraph>
                  </Grid>
                  <Grid item sm={12}>
                    <MainButton>Try Leviee for Free</MainButton>
                  </Grid>
                  <Grid item sm={12}>
                    <Paragraph>
                      Does your company use Leviee?
                      {' '}
                      <a href="https://restaurant.leviee.de">
                        Sign in here
                      </a>
                    </Paragraph>
                  </Grid>
                </Grid>
              </div>
            </Grid>
            <Grid item md={6} sm={12}>
              <div className={classes.illustration} />
            </Grid>
          </Grid>
        </Container>
      </section>
    </div>
  );
};

export default Hero;
