import { makeStyles } from '@material-ui/core/styles';
import { fade } from '@material-ui/core';

const useStyles = makeStyles(() => ({
  section: {
    paddingTop: 100,
    paddingBottom: 100,
    background: fade('#00848A', 0.04)
  },
  centered: {
    textAlign: 'center',
    maxWidth: 900,
    margin: '0 auto'
  },
  flex: {
    width: '100%',
    display: 'flex',
    alignItems: 'baseline',
    justifyContent: 'space-between',
    verticalAlign: 'middle',
    '& + &': {
      marginTop: 80
    }
  },
  item: {
    flex: 1,
    textAlign: 'center',
    margin: '0 auto',
    '& p': {
      marginTop: 20
    }
  },
  illustration: {
    width: '100%',
    height: 490,
    background: '#fff',
    borderRadius: 8
  }
}));

export default useStyles;
