import React from 'react';
import { Grid } from '@material-ui/core';
import Container from '@material-ui/core/Container';
import { Paragraph, SectionTitle } from '../../components/Text';
import { Button } from '../../components/Buttons';
import useStyles from './styles';

const NotTraditionalPosExtended = () => {
  const classes = useStyles();

  return (
    <div>
      <section className={classes.section}>
        <Container maxWidth="lg">
          <Grid container spacing={10}>
            <Grid item container md={12} xs={12} spacing={5}>
              <Grid item md={12}>
                <div className={classes.centered}>
                  <SectionTitle>
                    Your next POS is much more than documenting transactions
                  </SectionTitle>
                </div>
              </Grid>
              <Grid item md={12}>
                <div className={classes.centered}>
                  <Paragraph>
                    Traditional POS is often hard to use and makes you document transactions manually & repeatedly
                    so that you can print receipt for your customers and do tax declaration.
                    Let <PERSON><PERSON> make it 100x simpler and bring you 100x more value via
                    its modern cloud-based solution
                  </Paragraph>
                </div>
              </Grid>
              <Grid item md={12}>
                <div className={classes.centered}>
                  <Button normal>
                    Get a Free Demo
                  </Button>
                </div>
              </Grid>
            </Grid>
            <Grid item md={12}>
              <div className={classes.illustration} />
            </Grid>
            <Grid item xs={12}>
              <div className={classes.flex}>
                <div className={classes.item}>
                  <img src="/icons/features/tax-compliant.png" alt="tax-compliant" />
                  <Paragraph>
                    <span>100% Tax</span>
                    <br />
                    <span>Compliant</span>
                  </Paragraph>
                </div>
                <div className={classes.item}>
                  <img src="/icons/features/checkout-terminal.png" alt="checkout-terminal" />
                  <Paragraph>
                    <span>Checkout</span>
                    <br />
                    <span>Terminal</span>
                  </Paragraph>
                </div>
                <div className={classes.item}>
                  <img src="/icons/features/menu-editor.png" alt="menu-editor" />
                  <Paragraph>
                    <span>Menu</span>
                    <br />
                    <span>Editor</span>
                  </Paragraph>
                </div>
                <div className={classes.item}>
                  <img src="/icons/features/floor-plan.png" alt="floor-plan" />
                  <Paragraph>
                    <span>Floor</span>
                    <br />
                    <span>Plan</span>
                  </Paragraph>
                </div>
                <div className={classes.item}>
                  <img src="/icons/features/customer-management.png" alt="customer-management" />
                  <Paragraph>
                    <span>Customer</span>
                    <br />
                    <span>Management</span>
                  </Paragraph>
                </div>
              </div>
              <div className={classes.flex}>
                <div className={classes.item}>
                  <img src="/icons/features/payment-integration.png" alt="payment-integration" />
                  <Paragraph>
                    <span>Payment</span>
                    <br />
                    <span>Integration</span>
                  </Paragraph>
                </div>
                <div className={classes.item}>
                  <img src="/icons/features/customer-management.png" alt="customer-management" />
                  <Paragraph>
                    <span>Customer</span>
                    <br />
                    <span>Management</span>
                  </Paragraph>
                </div>
                <div className={classes.item}>
                  <img src="/icons/features/team-configuration.png" alt="team-configuration" />
                  <Paragraph>
                    <span>Team</span>
                    <br />
                    <span>Configuration</span>
                  </Paragraph>
                </div>
                <div className={classes.item}>
                  <img src="/icons/features/tax-declaration.png" alt="tax-declaration" />
                  <Paragraph>
                    <span>Tax</span>
                    <br />
                    <span>Declaration</span>
                  </Paragraph>
                </div>
                <div className={classes.item}>
                  <img src="/icons/features/business-insights.png" alt="business-insights" />
                  <Paragraph>
                    <span>Business</span>
                    <br />
                    <span>Insights</span>
                  </Paragraph>
                </div>
              </div>
            </Grid>
          </Grid>
        </Container>
      </section>
    </div>
  );
};

export default NotTraditionalPosExtended;
