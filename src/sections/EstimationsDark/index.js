import React from 'react';
import { Grid } from '@material-ui/core';
import Container from '@material-ui/core/Container';
import { Paragraph, SectionTitle } from '../../components/Text';
import useStyles from './styles';

const EstimationsDark = () => {
  const classes = useStyles();

  return (
    <div>
      <section className={classes.section}>
        <Container maxWidth="lg">
          <Grid container>
            <Grid item md={4} sm={12}>
              <div className={classes.centered}>
                <SectionTitle>
                  +40%
                </SectionTitle>
                <Paragraph>
                  Revenue per Waiter
                </Paragraph>
              </div>
            </Grid>
            <Grid item md={4} sm={12}>
              <div className={classes.centered}>
                <SectionTitle>
                  +30%
                </SectionTitle>
                <Paragraph>
                  Additional Drink Orders
                </Paragraph>
              </div>
            </Grid>
            <Grid item md={4} sm={12}>
              <div className={classes.centered}>
                <SectionTitle>
                  -50%
                </SectionTitle>
                <Paragraph>
                  Customer Waiting Time
                </Paragraph>
              </div>
            </Grid>
          </Grid>
        </Container>
      </section>
    </div>
  );
};

export default EstimationsDark;
