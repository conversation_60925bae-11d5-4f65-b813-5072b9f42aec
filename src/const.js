export const noop = () => {}

export const excludeInitRoutes = {
	PAY: '/pay/[code]',
	SCREEN: '/screen/[code]',
	GIFT_CARDS: '/restaurant/[slug]/gift-cards'
}
export const excludeInitRouteOptions = Object.values(excludeInitRoutes)

export const excludeFooterRoutes = {
	SCREEN: '/screen/[code]'
}
export const excludeFooterRouteOptions = Object.values(excludeFooterRoutes)

const flags = {
	de: {
		url: "https://storage.googleapis.com/leviee_public/allO/flag-icons/flag-de-80x48.png",
	},
	cn: {
		url: "https://storage.googleapis.com/leviee_public/allO/flag-icons/flag-cn-80x53.png",
	},
	us: {
		url: "https://storage.googleapis.com/leviee_public/allO/flag-icons/flag-us-80x42.png",
	}
};

export const languages = {
	de: {
		value: "de",
		i18nKey: "german",
		iconSrc: flags.de.url,
	},
	zh: {
		value: "zh",
		i18nKey: "chinese",
		iconSrc: flags.cn.url,
	},
	en: {
		value: "en",
		i18nKey: "english",
		iconSrc: flags.us.url,
	}
}

export const languageOptions = Object.values(languages)
