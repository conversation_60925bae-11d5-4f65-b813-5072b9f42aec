import React, {useEffect, useState} from 'react';
import useStyles from './styles';
import {useRouter} from "next/router";
import isEmpty from "../../utilities/isEmpty";
import {getPaymentLinkData, submitPaymentLinkData} from "../../../redux/api";
import {withTranslation} from "../../../i18n";
import {ButtonBase, Typography, useMediaQuery} from "@material-ui/core";
import typography from "../../../styles/typography";
import palette from "../../../styles/palette";
import {ArrowDownIconWhiteInCircle20, PaymentIllustration160, ReceiptDarkIcon20} from "../../utilities/icons";
import FlexDiv from "../../components/_div/FlexDiv";
import Notification from "../../components/Notification";
import shadows from "../../../styles/shadows";
import formatNumber from "../../utilities/formatNumber";
import {noop} from "../../const";


const Payment = ({ t }) => {
  const classes = useStyles();
  const isMobile = useMediaQuery('(max-width:600px)');
  
  const router = useRouter();
  const { query = {} } = router;
  
  const [paymentLinkData, setPaymentLinkData] = useState(null);
  const { amountSuggestions = [], checkoutRequest = {}, restaurant = {}, order = {}, paymentRequest = {} } = (paymentLinkData || {});
  const { name: restaurantName } = (restaurant || {});
  const { paymentOption, selectedOrderItems = [], tipAmount: checkoutRequestTipAmount } = (checkoutRequest || {})
  const { items: orderItems = [], total: orderTotal, totalDue: orderTotalDue, type } = (order || {})
  const { amount: paymentRequestAmount } = (paymentRequest || {})
  
  const isExpress = type === "EXPRESS";
  const isPayByAmount = paymentOption === "CUSTOM";
  const isPartialPayment = (isPayByAmount || (paymentRequestAmount < orderTotalDue)) && !isExpress
  const tipIsPreSelected = !!checkoutRequestTipAmount
  const selectedOrderItemIds = !isEmpty(selectedOrderItems) ? selectedOrderItems.map(sItm => sItm.id) : []
  const resolvedOrderItems = isPayByAmount || isExpress ? orderItems : orderItems.filter(oItm => selectedOrderItemIds.indexOf(oItm.id) > -1);
  
  useEffect(() => {
    if (isEmpty(query) || !query.code) {
      router.replace('/').then(() => {}).catch(() => {});
    } else {
      getPaymentLinkData(query.code).then(({ data }) => setPaymentLinkData(data)).catch(() => {})
    }
  }, [JSON.stringify(query)])
  
  const [submitted, setSubmitted] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  
  const [selectedAmountSuggestionIndex, setSelectedAmountSuggestionIndex] = useState(0);
  
  const submitPayment = () => {
    setSubmitting(true);
    submitPaymentLinkData(query.code, tipIsPreSelected ? 0 : amountSuggestions[selectedAmountSuggestionIndex].tipAmount).then(({ data }) => {
      const { url } = (data || {});
      if (url) {
        router.push(url).then(noop).catch(noop);
      }
    }).catch(() => {
      setSubmitting(false);
    })
  }
  
  if (isEmpty(paymentLinkData)) {
    return (
      <div>
        <main className={classes.main}>
          <div style={{ maxWidth: "calc(100% - 24px)", margin: "48px auto", width: 460 }}>
          </div>
        </main>
      </div>
    );
  }
  
  if (submitted) {
    return (
      <div>
        <main className={classes.main}>
          <div style={{ maxWidth: "calc(100% - 24px)", margin: "48px auto", width: 460 }}>
            <FlexDiv style={{ flexDirection: "column", justifyContent: "center", width: 320, maxWidth: "calc(100% - 24px)", }}>
              <FlexDiv style={{ marginBottom: 32, justifyContent: "center" }}>
                <PaymentIllustration160 />
              </FlexDiv>
              <Typography style={{ ...typography.large.semiBold, marginBottom: 10 }}>
                {t('receipt-generated')}
              </Typography>
              <Typography style={{ ...typography.body.regular, marginBottom: 32, textAlign: "center" }}>
                {t('please-check-your-email-for-the-pdf-receipt')}
              </Typography>
            </FlexDiv>
          </div>
        </main>
      </div>
    )
  }

  return (
    <div style={{ backgroundColor: "#f0edea" }}>
      <main className={classes.main}>
        <div style={{ maxWidth: "calc(100% - 24px)", margin: "48px auto", width: 600, background: palette.grayscale["100"], padding: isMobile ? 16 : 32, borderRadius: isMobile ? 12 : 16 }}>
          <div style={{ margin: "0 auto 32px" }}>
            {/*<AllOProviderLogo20 />*/}
            <Typography style={{ ...typography.large.semiBold, marginBottom: 16, fontSize: 28, fontWeight: 600, lineHeight: '36px' }}>{t("checkout")}</Typography>
            <Typography style={{ ...typography.body.regular, fontSize: 18, fontWeight: 400, lineHeight: '26px' }}>{t("thank-you-for-ordering-at-restaurant", { name: restaurantName })} {t('below-you-can-find-your-receipt-details')}</Typography>
          </div>
          <div>
            <div style={{ borderRadius: 12, padding: '16px 12px', background: palette.grayscale["250"] }}>
              <div style={{ display: "flex", alignItems: "center", justifyContent: "space-between", marginBottom: 14, paddingRight: 4 }}>
                <div style={{ display: "flex", alignItems: "center" }}>
                  <ReceiptDarkIcon20 />
                  <Typography style={{ ...typography.body.medium, marginLeft: 4 }}>{t('order-summary')}</Typography>
                </div>
                <div>
                  <Typography style={{ ...typography.body.regular }}>{formatNumber(orderTotal)}€</Typography>
                </div>
              </div>
              <div style={{ paddingLeft: 4, paddingRight: 4 }}>
                <div style={{ borderBottom: `1px dashed ${palette.grayscale["400"]}`, marginBottom: 12, opacity: 0.9 }} />
              </div>
              {resolvedOrderItems.map((orderItem = {}, index) => {
                const { id, qtd, name, total } = orderItem;
                const first = index === 0;
                return (
                  <div key={id} style={{ borderRadius: 12, padding: 12, background: palette.grayscale["100"], display: "flex", alignItems: "center", justifyContent: "space-between", width: "100%", ...shadows.email_base, marginTop: first ? 0 : 8 }}>
                    <div style={{ display: "flex", alignItems: "center" }}>
                      <Typography style={{ ...typography.extraSmall.medium, minWidth: 16, display: "flex", justifyContent: "center", borderRadius: "100%", marginRight: 8, background: palette.grayscale["300"] }}>{qtd}</Typography>
                      <Typography style={{ ...typography.body.medium }}>{name}</Typography>
                    </div>
                    <Typography style={{ ...typography.body.regular }}>{formatNumber(total)}€</Typography>
                  </div>
                )
              })}
              <div style={{ paddingLeft: 4, paddingRight: 4 }}>
                <div style={{ paddingTop: 16, display: "flex", alignItems: "center", justifyContent: "space-between" }}>
                  <Typography style={{ ...typography.body.regular }}>{t('subtotal')}</Typography>
                  <Typography style={{ ...typography.body.regular }}>{formatNumber(orderTotal)}€</Typography>
                </div>
                <div style={{ marginTop: 8, display: "flex", alignItems: "center", justifyContent: "space-between" }}>
                  <Typography style={{ ...typography.body.regular }}>{t('discounts')}</Typography>
                  <Typography style={{ ...typography.body.regular }}>{formatNumber(0)}€</Typography>
                </div>
                <div style={{ marginTop: 8, display: "flex", alignItems: "center", justifyContent: "space-between" }}>
                  <Typography style={{ ...typography.body.medium }}>{t('total')}</Typography>
                  <Typography style={{ ...typography.body.medium }}>{formatNumber(orderTotal)}€</Typography>
                </div>
              </div>
            </div>
            <div style={{ paddingLeft: 4, paddingRight: 4 }}>
              <div style={{ borderBottom: `1px dashed ${palette.grayscale["300"]}`, paddingTop: 32, marginBottom: 32 }} />
            </div>
            <Typography style={{ ...typography.body.regular, fontSize: 18, fontWeight: 400, lineHeight: '26px' }}>
              {isPartialPayment ? t("you-have-requested-to-pay-a-partial-amount-of-this-order") : t("you-have-requested-to-pay-full-amount-of-this-order")}
            </Typography>
            {!tipIsPreSelected && (
              <Typography style={{ ...typography.body.regular, fontSize: 18, fontWeight: 400, lineHeight: '26px', marginTop: 16 }}>
                {t("please-select-the-payment-amount")}
              </Typography>
            )}
            {!tipIsPreSelected && (
              <div style={{ display: "flex", flexDirection: "row", justifyContent: "space-around", width: "100%", marginTop: 32, borderRadius: 12, background: palette.grayscale["250"], padding: 2 }}>
                {!isEmpty(amountSuggestions) && amountSuggestions.map((suggestion, index) => {
                  const selectedAmountSuggestion = selectedAmountSuggestionIndex === index;
                  const selectedButtonStyle = { padding: 12, backgroundColor: palette.grayscale["100"], ...shadows.email_base, borderRadius: 10, width: "100%" }
                  const inactiveButtonStyle = { padding: 12, backgroundColor: 'transparent', borderRadius: 10, width: "100%" }
                  return (
                    <ButtonBase key={`sug-${suggestion.amount}`} style={selectedAmountSuggestion ? selectedButtonStyle : inactiveButtonStyle} disableRipple disableTouchRipple onClick={() => setSelectedAmountSuggestionIndex(index)}>
                      <Typography style={{ ...typography.body.medium }}>
                        {formatNumber(suggestion.amount)}€
                      </Typography>
                    </ButtonBase>
                  )
                })}
              </div>
            )}
          </div>
          <div style={{ marginTop: 32 }}>
            <ButtonBase style={{ padding: '12px', width: "100%", background: submitting ? palette.grayscale["350"] : palette.primary["500"], borderRadius: 12 }} onClick={submitPayment} disabled={submitting}>
              <div style={{ display: "flex", alignItems: "center", justifyContent: "space-between", width: "100%" }}>
                <div style={{ "display": "flex", alignItems: "center" }}>
                  <div style={{ display: "flex", transform: "rotate(-90deg)" }}>
                    <ArrowDownIconWhiteInCircle20 />
                  </div>
                  <Typography style={{ ...typography.body.medium, color: palette.grayscale["100"], marginLeft: 2 }}>
                    {t('common-continue')}
                  </Typography>
                </div>
                <Typography style={{ ...typography.body.medium, color: palette.grayscale["100"] }}>
                  {formatNumber(amountSuggestions[selectedAmountSuggestionIndex].amount)}€
                </Typography>
              </div>
            </ButtonBase>
          </div>
        </div>
      </main>
      <Notification />
    </div>
  );
};

export default withTranslation('common')(Payment);
