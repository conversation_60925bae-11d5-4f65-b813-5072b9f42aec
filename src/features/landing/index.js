import React, {useEffect} from 'react';
import { SplashScreen } from "../splash";
import ReturnToOrderBtn from "../../components/ReturnToOrderBtn";
import useStyles from './styles';
import {useDispatch} from "react-redux";
import {orderActions} from "../../../redux/actions";

const Landing = () => {
  const classes = useStyles();
  const dispatch = useDispatch();
  
  useEffect(() => {
    dispatch(orderActions.getActiveOrder())
  }, [])

  return (
    <div>
      <main className={classes.main}>
        <SplashScreen />
        <ReturnToOrderBtn />
      </main>
    </div>
  );
};

export default Landing;
