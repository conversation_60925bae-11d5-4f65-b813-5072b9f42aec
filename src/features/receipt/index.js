import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/router';
import { ButtonBase, Typography } from '@material-ui/core';
import { useDispatch } from 'react-redux';
import useStyles from './styles';
import isEmpty from '../../utilities/isEmpty';
import { generateDigitalReceipt, getDigitalReceiptsInfo } from '../../../redux/api';
import Field from '../../components/_input/Field';
import { withTranslation } from '../../../i18n';
import typography from '../../../styles/typography';
import isValidEmail from '../../utilities/isValidEmail';
import palette from '../../../styles/palette';
import { appActions } from '../../../redux/actions';
import { PaymentIllustration160, WarningCircle16 } from '../../utilities/icons';
import FlexDiv from '../../components/_div/FlexDiv';
import Notification from '../../components/Notification';
import Switch from '../../components/_toggles/Switch';
import shadows from '../../../styles/shadows';

const data = {
  receiptPurpose: null,
  receiptParticipants: null,
  customer: {
    firstName: null,
    lastName: null,
    email: null,
    hideEmailOnReceipt: false,
    company: null,
    vatId: null,
    address: {
      country: 'DE',
      city: null,
      street: null,
      number: null,
      zipCode: null
    }
  }
};

const Receipt = ({ t }) => {
  const classes = useStyles();
  const dispatch = useDispatch();

  const [form, setForm] = useState({ ...data });
  console.log(form)

  const [errorMessagePdfReceipt, setErrorMessagePdfReceipt] = useState('');
  const onChange = (e) => {
    setErrorMessagePdfReceipt('');
    setForm({ ...form, [e.target.name]: e.target.value });
  };
  const onCustomerChange = (e) => {
    setErrorMessagePdfReceipt('');
    const updatedCustomer = { ...form.customer, [e.target.name]: e.target.value };
    setForm({ ...form, customer: updatedCustomer });
  };

  const onToggleUpdate = (stateKey, value) => {
    const updatedCustomer = { ...form.customer, [stateKey]: value };
    setForm({ ...form, customer: updatedCustomer });
  };

  const onAddressChange = (e) => {
    setErrorMessagePdfReceipt('');
    const updatedAddress = { ...form.customer.address, [e.target.name]: e.target.value };
    const updatedCustomer = { ...form.customer, address: updatedAddress };
    setForm({ ...form, customer: updatedCustomer });
  };

  const router = useRouter();
  const { query = {} } = router;

  useEffect(() => {
    if (isEmpty(query) || !query.code) {
      router.replace('/').then(() => {}).catch(() => {});
    }
  }, [JSON.stringify(query)]);

  useEffect(() => {
    getDigitalReceiptsInfo(query.code)
      .then(({ data }) => {
        const { customer = {} } = data;
        if (isEmpty(data)) {

        } else {
          setForm({ ...form,
            customer: {
              firstName: customer.firstName ?? null,
              lastName: customer.lastName ?? null,
              email: customer.email ?? null,
              hideEmailOnReceipt: customer.hideEmailOnReceipt ?? false,
              company: customer.company ?? null,
              vatId: customer.vatId ?? null,
              address: {
                country: customer.address?.country ?? 'DE',
                city: customer.address?.city ?? null,
                street: customer.address?.street ?? null,
                number: customer.address?.number ?? null,
                zipCode: customer.address?.zipCode ?? null
              }
            } });
        }
      }).catch(() => {});
  }, []);

  const [submitted, setSubmitted] = useState(false);
  const [submitting, setSubmitting] = useState(false);

  const submit = () => {
    const { code } = query;
    setSubmitting(true);
    dispatch(appActions.showNotification('generating-receipt'));

    const { customer } = form;

    if (customer.email) {
      customer.email = (customer.email || '').trim();
    }

    generateDigitalReceipt(code, customer, form.receiptPurpose, form.receiptParticipants)
      .then(({ data }) => {
        setSubmitting(false);
        setSubmitted(true);
        dispatch(appActions.showNotification('receipt-sent-via-email'));
      })
      .catch(({ response }) => {
        const { data } = response;
        const { title } = data;
        setErrorMessagePdfReceipt(title);
        setSubmitting(false);
        dispatch(appActions.showNotification('could-not-generate-receipt', 'error'));
      });
  };

  const canSendEmail = form && form.customer && isValidEmail(form.customer.email);

  if (submitted) {
    return (
      <div>
        <main className={classes.main}>
          <div style={{ maxWidth: 'calc(100% - 24px)', margin: '48px auto', width: 460 }}>
            <FlexDiv style={{ flexDirection: 'column', justifyContent: 'center', width: 320, maxWidth: 'calc(100% - 24px)', }}>
              <FlexDiv style={{ marginBottom: 32, justifyContent: 'center' }}>
                <PaymentIllustration160 />
              </FlexDiv>
              <Typography style={{ ...typography.large.semiBold, marginBottom: 10 }}>
                {t('receipt-generated')}
              </Typography>
              <Typography style={{ ...typography.body.regular, marginBottom: 32, textAlign: 'center' }}>
                {t('please-check-your-email-for-the-pdf-receipt')}
              </Typography>
            </FlexDiv>
          </div>
        </main>
      </div>
    );
  }

  return (
    <div>
      <main className={classes.main}>
        <div style={{ maxWidth: 'calc(100% - 24px)', margin: '48px auto', width: 460 }}>
          <div style={{ margin: '0 auto 32px' }}>
            <Typography style={{ ...typography.large.semiBold, marginBottom: 10 }}>{t('your-receipt')}</Typography>
            <Typography style={{ ...typography.body.regular }}>{t('fill-in-your-data-below-to-receive-a-digital-receipt')}</Typography>
          </div>
          <form>
            <div style={{ display: 'flex' }}>
              <div style={{ flex: 1, marginRight: 4 }}>
                <Field
                  skipNavigatorCheck
                  label={t('welcome-details-firstName-field-label')}
                  disabled={submitting}
                  onChange={onCustomerChange}
                  name="firstName"
                  value={form.customer.firstName}
                  placeholder="Max"
                />
              </div>
              <div style={{ flex: 1, marginLeft: 4 }}>
                <Field
                  skipNavigatorCheck
                  label={t('welcome-details-lastName-field-label')}
                  disabled={submitting}
                  onChange={onCustomerChange}
                  name="lastName"
                  value={form.customer.lastName}
                  placeholder="Musterman"
                />
              </div>
            </div>
            <div style={{ marginTop: 16 }}>
              <Field
                skipNavigatorCheck
                label={t('welcome-details-email-field-label')}
                required
                disabled={submitting}
                onChange={onCustomerChange}
                name="email"
                value={form.customer.email}
                placeholder="<EMAIL>"
                error={form.customer.email && !isValidEmail(form.customer.email)}
              />
              {!isEmpty(form.customer.email) && !isValidEmail(form.customer.email) && (
                <Typography style={{
                  ...typography.body.regular,
                  color: palette.primary['500'],
                  marginTop: 8
                }}
                >
                  {t('invalid-email')}
                </Typography>
              )}
              <div style={{
                marginTop: 12,
                padding: 12,
                borderRadius: 12,
                background: palette.grayscale['100'],
                ...shadows.base
              }}
              >
                <div style={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'space-between'
                }}
                >
                  <div style={{ marginRight: 8 }}>
                    <Typography style={{
                      ...typography.body.medium
                    }}
                    >
                      {t('hide-this-email-from-receipt-toggle-header')}
                    </Typography>
                    <Typography style={{
                      ...typography.body.regular,
                      color: palette.grayscale['600']
                    }}
                    >
                      {t('hide-this-email-from-receipt-description')}
                    </Typography>
                  </div>
                  <Switch checked={form.customer.hideEmailOnReceipt ?? false} onClick={() => onToggleUpdate('hideEmailOnReceipt', !form.customer.hideEmailOnReceipt)} />
                </div>
              </div>
            </div>
            <div style={{ marginTop: 16 }}>
              <Field
                skipNavigatorCheck
                label={t('company')}
                disabled={submitting}
                onChange={onCustomerChange}
                name="company"
                value={form.customer.company}
                placeholder="Company"
              />
            </div>
            <div style={{ marginTop: 16, maxWidth: 200 }}>
              <Field
                skipNavigatorCheck
                label={t('vatId')}
                disabled={submitting}
                onChange={onCustomerChange}
                name="vatId"
                value={form.customer.vatId}
                placeholder="DE123456789"
              />
            </div>
            <div style={{ marginTop: 16 }}>
              <Field
                skipNavigatorCheck
                label={t('receipt-purpose')}
                disabled={submitting}
                onChange={onChange}
                name="receiptPurpose"
                value={form.receiptPurpose}
              />
            </div>
            <div style={{ marginTop: 16 }}>
              <Field
                skipNavigatorCheck
                label={t('receipt-participants')}
                disabled={submitting}
                onChange={onChange}
                name="receiptParticipants"
                value={form.receiptParticipants}
                multiline
                style={{ minHeight: 120 }}
              />
            </div>
            <div style={{ marginTop: 16, display: 'flex' }}>
              <div style={{ flex: 1, marginRight: 4 }}>
                <Field
                  skipNavigatorCheck
                  label={t('welcome-details-street-field-label')}
                  name="street"
                  value={form.customer.address.street}
                  onChange={onAddressChange}
                  disabled={submitting}
                  placeholder="Rathenower str."
                />
              </div>
              <div style={{ flex: 1, marginLeft: 4, maxWidth: 100 }}>
                <Field
                  skipNavigatorCheck
                  label={t('welcome-details-number-field-label')}
                  name="number"
                  value={form.customer.address.number}
                  onChange={onAddressChange}
                  disabled={submitting}
                  placeholder="1"
                />
              </div>
            </div>
            <div style={{ marginTop: 16, display: 'flex' }}>
              <div style={{ flex: 1, marginRight: 4 }}>
                <Field
                  skipNavigatorCheck
                  label={t('welcome-details-zipCode-field-label')}
                  name="zipCode"
                  value={form.customer.address.zipCode}
                  onChange={onAddressChange}
                  disabled={submitting}
                  placeholder="12345"
                />
              </div>
              <div style={{ flex: 1, marginLeft: 4 }}>
                <Field
                  skipNavigatorCheck
                  label={t('city')}
                  name="city"
                  value={form.customer.address.city}
                  onChange={onAddressChange}
                  disabled={submitting}
                  placeholder="Berlin"
                />
              </div>
            </div>
            {!isEmpty(errorMessagePdfReceipt) && (
              <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'flex-start', marginTop: 8, gap: 6 }}>
                <WarningCircle16 />
                <Typography style={{ ...typography.body.regular, color: palette.primary['500'] }}>{errorMessagePdfReceipt}</Typography>
              </div>
            )}
            <ButtonBase
              disabled={!canSendEmail}
              style={{ padding: '12px 24px', marginTop: 24, background: !canSendEmail ? palette.grayscale['350'] : palette.primary['500'], borderRadius: 12 }}
              disableRipple
              disableTouchRipple
              onClick={submit}
            >
              <Typography style={{ ...typography.body.medium, color: palette.grayscale['100'] }}>
                {t('get-receipt')}
              </Typography>
            </ButtonBase>

          </form>
        </div>
      </main>
      <Notification />
    </div>
  );
};

export default withTranslation('common')(Receipt);
