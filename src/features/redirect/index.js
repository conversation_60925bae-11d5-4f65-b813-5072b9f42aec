import React, {useEffect} from 'react';
import { SplashScreen } from "../splash";
import useStyles from './styles';
import {useRouter} from "next/router";
import isEmpty from "../../utilities/isEmpty";
import {getDynamicCode} from "../../../redux/api";

const Redirect = () => {
  const classes = useStyles();
  
  const router = useRouter();
  const { query = {} } = router;
  
  useEffect(() => {
    if (!isEmpty(query)) {
      const { code } = query;
      if (code) {
        getDynamicCode(code).then(({ data }) => {
          const { targetUrl } = data;
          // const { slug = 'sushi-banana', tableId = 'some-table-id', orderType = 'express' } = {};
          router
            // .replace(`/restaurant/${slug}/${orderType}?tableId=${tableId}`, undefined, { shallow: true })
            .replace(targetUrl)
            .then(() => {})
            .catch(() => {
              router.replace('/', undefined, { shallow: false })
                .then(() => {})
                .catch(() => {})
            })
        }).catch(() => {
          router.replace('/').then(() => {}).catch(() => {});
        })
      }
    }
  }, [JSON.stringify(query)])

  return (
    <div>
      <main className={classes.main}>
        <SplashScreen />
      </main>
    </div>
  );
};

export default Redirect;
