import React, {useEffect, useState} from 'react';
import {useRouter} from "next/router";
import {useDispatch, useSelector} from "react-redux";
import {
  createCardCart,
  getCurrentCardCart,
  getRestaurantConfiguration,
  payCardCart,
  submitPaymentLinkData
} from "../../../redux/api";
import {withTranslation} from "../../../i18n";
import {ButtonBase, Typography, useMediaQuery} from "@material-ui/core";
import typography from "../../../styles/typography";
import palette from "../../../styles/palette";
import {ArrowDownIconWhiteInCircle20, PaymentIllustration160} from "../../utilities/icons";
import FlexDiv from "../../components/_div/FlexDiv";
import Notification from "../../components/Notification";
import shadows from "../../../styles/shadows";
import formatNumber from "../../utilities/formatNumber";
import {restaurantsActions} from "../../../redux/actions";
import {restaurantsSelectors} from "../../../redux/selectors";
import Field from "../../components/_input/Field";
import isEmpty from "../../utilities/isEmpty";
import {noop} from "../../const";
import useStyles from './styles';
import isValidEmail from "../../utilities/isValidEmail";


const Cards = ({ t }) => {
  const classes = useStyles();
  const dispatch = useDispatch();
  const isMobile = useMediaQuery('(max-width:400px)');
  
  const router = useRouter();
  const { slug, result } = router.query;
  
  const [cardCart, setCardCart] = useState(null);
  const { customer } = (cardCart || {});
  const { email: cartEmail } = (customer || {})
  
  const { restaurant = {} } = useSelector(restaurantsSelectors.getRestaurant);
  const { id: restaurantId, name: restaurantName } = restaurant;

  const [configuration, setConfiguration] = useState(null);
  
  useEffect(() => {
    if (isEmpty(router.query) || !slug) {
      router.replace('/').then(() => {}).catch(() => {});
    } else {
      dispatch(restaurantsActions.getRestaurantBySlug(slug))
    }
  }, [])
  
  useEffect(() => {
    if (restaurantId) {
      getCurrentCardCart(restaurantId)
        .then(({ data }) => setCardCart(data))
        .catch(() => {})
    }
  }, [restaurantId])

  useEffect(() => {
    if (restaurantId) {
      getRestaurantConfiguration(restaurantId)
        .then(({ data }) => setConfiguration(data))
        .catch(() => {});
    }
  }, [restaurantId])
  
  const success = cardCart && cardCart.status === "COMPLETED" && result && result === "success"
  const [submitting, setSubmitting] = useState(false);

  const suggestedAmounts = configuration?.giftCardSettings?.suggestedAmounts || [
    {
      "type": "FIXED",
      "value": 10
    },
    {
      "type": "FIXED",
      "value": 20
    },
    {
      "type": "FIXED",
      "value": 50
    },
    {
      "type": "CUSTOM"
    }
  ];
  
  const [selectedAmountId, setSelectedAmountId] = useState();
  const [amount, setAmount] = useState();

  useEffect(() => {
    if (!isEmpty(suggestedAmounts)) {
      setSelectedAmountId(suggestedAmounts[0]?.value);
      setAmount(suggestedAmounts[0]?.value);
    }
  }, [JSON.stringify(suggestedAmounts)]);

  const updateCustomAmount = (e) => {
    setAmount(e.target.value)
  }
  
  useEffect(() => {
    if (selectedAmountId) {
      setAmount(selectedAmountId)
    } else {
      setAmount('')
    }
  }, [selectedAmountId])

  const customAmountIdSelected = !selectedAmountId;
  
  const [email, setEmail] = useState('');
  const updateEmail = (e) => {
    setEmail(e.target.value)
  }
  
  const submitPayment = () => {
    setSubmitting(true);
    createCardCart(restaurantId, { email }, amount)
      .then(() => {
        payCardCart(restaurantId)
          .then(({ data }) => {
            const { paymentUrl } = (data || {});
            if (paymentUrl) {
              router.push(paymentUrl).then(noop).catch(noop);
            }
          })
          .catch(() => {
            setSubmitting(false);
          })
      })
      .catch(() => setSubmitting(false))
  }
  
  const isValid = amount && amount > 0 && email && isValidEmail(email);
  
  if (success) {
    return (
      <div>
        <main className={classes.main}>
          <div style={{ maxWidth: "calc(100% - 24px)", margin: "48px auto", width: '100%' }}>
            <FlexDiv style={{ flexDirection: "column", justifyContent: "center", maxWidth: "calc(100% - 24px)", margin: '0 auto', width: 320}}>
              <FlexDiv style={{ marginBottom: 32, justifyContent: "center" }}>
                <PaymentIllustration160 />
              </FlexDiv>
              <Typography style={{ ...typography.large.semiBold, marginBottom: 10 }}>
                {t('your-gift-card')}
              </Typography>
              <Typography style={{ ...typography.body.regular, marginBottom: 32, textAlign: "center" }}>
                {t('please-check-your-email-for-the-details-and-purchase-confirmation', { cartEmail })}
              </Typography>
            </FlexDiv>
          </div>
        </main>
      </div>
    )
  }

  return (
    <div style={{ backgroundColor: "#f0edea" }}>
      <main className={classes.main}>
        <div style={{ maxWidth: "calc(100% - 24px)", margin: "48px auto", width: 600, background: palette.grayscale["100"], padding: isMobile ? 16 : 32, borderRadius: isMobile ? 12 : 16 }}>
          <div style={{ margin: "0 auto 32px" }}>
            <Typography style={{ ...typography.large.semiBold, marginBottom: 16, fontSize: 28, fontWeight: 600, lineHeight: '36px' }}>{t("gift-card")}</Typography>
            <Typography style={{ ...typography.body.regular, fontSize: 18, fontWeight: 400, lineHeight: '26px' }}>{t("order-your-digital-gift-card-at-restaurant", { name: restaurantName })} {t('select-your-card-amount-to-proceed')}</Typography>
          </div>
          <div>
            <div style={{ display: "flex", flexDirection: isMobile ? "column" : "row", justifyContent: "space-around", width: "100%", marginTop: 32, borderRadius: 12, background: palette.grayscale["250"], padding: 2 }}>
              {suggestedAmounts?.map(({ type, value }) => {
                const selectedAmountSuggestion = selectedAmountId === value;
                const isCustom = type === "CUSTOM"
                const selectedButtonStyle = { padding: 12, backgroundColor: palette.grayscale["100"], ...shadows.email_base, borderRadius: 10, width: "100%" }
                const inactiveButtonStyle = { padding: 12, backgroundColor: 'transparent', borderRadius: 10, width: "100%" }
                return (
                  <ButtonBase key={`sug-${value}`} style={selectedAmountSuggestion ? selectedButtonStyle : inactiveButtonStyle} disableRipple disableTouchRipple onClick={() => setSelectedAmountId(value)}>
                    <Typography style={{ ...typography.body.medium }}>
                      {isCustom ? t('other') : `${formatNumber(value)}€`}
                    </Typography>
                  </ButtonBase>
                )
              })}
            </div>
            {customAmountIdSelected && (
              <div style={{ marginTop: 32 }}>
                <Field
                  label={t('gift-card-amount')}
                  placeholder={t('enter-amount')}
                  type={'number'}
                  value={amount}
                  onChange={updateCustomAmount}
                  skipNavigatorCheck
                  adornEnd={"€"}
                />
              </div>
            )}
            <div style={{ paddingLeft: 4, paddingRight: 4 }}>
              <div style={{ borderBottom: `1px dashed ${palette.grayscale["300"]}`, paddingTop: 32, marginBottom: 32 }} />
            </div>
            <Typography style={{ ...typography.body.regular, fontSize: 18, fontWeight: 400, lineHeight: '26px' }}>
              {t("to-send-the-gift-card-we-need-your-email")}
            </Typography>
            <div style={{ marginTop: 32 }}>
              <Field
                label={t('digital-gift-card-email')}
                placeholder={t('enter-email')}
                value={email}
                onChange={updateEmail}
                skipNavigatorCheck
                error={email && !isValidEmail(email)}
              />
            </div>
          </div>
          <div style={{ marginTop: 32 }}>
            <ButtonBase style={{ padding: '12px', width: "100%", background: (submitting || !isValid) ? palette.grayscale["350"] : palette.primary["500"], borderRadius: 12 }} onClick={submitPayment} disabled={submitting || !isValid}>
              <div style={{ display: "flex", alignItems: "center", justifyContent: "space-between", width: "100%" }}>
                <div style={{ "display": "flex", alignItems: "center" }}>
                  <div style={{ display: "flex", transform: "rotate(-90deg)" }}>
                    <ArrowDownIconWhiteInCircle20 />
                  </div>
                  <Typography style={{ ...typography.body.medium, color: palette.grayscale["100"], marginLeft: 2 }}>
                    {t('common-continue')}
                  </Typography>
                </div>
                <Typography style={{ ...typography.body.medium, color: palette.grayscale["100"] }}>
                  {amount ? `${formatNumber(amount)}€` : ''}
                </Typography>
              </div>
            </ButtonBase>
          </div>
        </div>
      </main>
      <Notification />
    </div>
  );
};

export default withTranslation('common')(Cards);
