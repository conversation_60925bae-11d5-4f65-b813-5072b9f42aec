import { makeStyles } from '@material-ui/core/styles';
import { colors } from '../../../styles/theme';
import palette from "../../../styles/palette";
import shadows from "../../../styles/shadows";

const useStyles = makeStyles((theme) => ({
  wrapper: {
    background: palette.grayscale["100"],
    ...shadows.base,
    padding: 12,
    borderRadius: 12
  },
  carouselSlide: {
    width: '85%'
  },
  titleWrapper: {
  },
  actions: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingTop: theme.spacing(2)
  },
  tags: {
    marginTop: 8,
    '& > div:not(:first-child)': {
      marginLeft: 8
    }
  },
  detailsButton: {
    marginTop: 2,

    // fix issue with visible single pixel between menu and header on scroll stick
    paddingBottom: 1
  },
  menu: {
    marginTop: 12
  },
  messageDrawerWrapper: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    maxWidth: 300,
    margin: '0 auto'
  },
  secondaryText: {
    textAlign: 'center',
    color: colors.leviee.greyscale.darkGray
  }
}));

export default useStyles;
