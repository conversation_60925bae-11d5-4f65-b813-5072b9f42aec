import React, { useEffect, useState } from 'react';
import Container from '@material-ui/core/Container';
import SimpleReactLightbox from 'simple-react-lightbox';
import { useDispatch, useSelector } from 'react-redux';
import { useRouter } from 'next/router';
import Skeleton from '@material-ui/lab/Skeleton';
import isEmpty from '../../utilities/isEmpty';
import Menu from '../../components/Menu';
import ScrollableGallery from '../../components/ScrollableGallery';
import LightBox from '../../components/Carousel/LightBox';
import { restaurantsSelectors } from '../../../redux/selectors';
import { restaurantsActions } from '../../../redux/actions';
// eslint-disable-next-line import/named
import { withTranslation } from '../../../i18n';
import BottomPanel from '../../components/BottomPanel';
import HistoricalOrderDrawer from '../../components/HistoricalOrderDrawer';
import ReservationModal from "../../components/_popup/ReservationModal";
import AppBar from '../../components/AppBar';
import useStyles from './styles';
import removeUndefined from "../../utilities/removeUndefined";
import {noop} from "../../components/Menu/utils";
import {NextSeo} from "next-seo";
import Typography from "@material-ui/core/Typography";
import typography from "../../../styles/typography";
import StartNewOrderBtn from "../../components/StartNewOrderBtn";
import ButtonBase from "@material-ui/core/ButtonBase";
import StartOrderingModal from "../../components/_popup/StartOrderingModal";
import RestaurantInfoModal from "../../components/_popup/RestaurantInfoModal";
import NoTakeawayModal from "../../components/_popup/NoTakeawayModal";
import {getWebshopHours} from "../../../redux/api";
import Notification from "../../components/Notification";

const Restaurant = ({ t, slug, booking }) => {
  const router = useRouter();
  const classes = useStyles();
  const dispatch = useDispatch();

  const [isRestaurantDetailsDrawerOpen, setRestaurantDetailsDrawerOpen] = useState(false);
  const [isReservationDrawerOpen, setReservationDrawerOpen] = useState(!!booking);
  const [isTakeawayTypeDrawerOpen, setTakeawayTypeDrawerOpen] = useState(false);
  const [takeawayOrderTypes, setTakeawayOrderTypes] = useState({ hasPickup: false, hasDelivery: false });
  const [isMenuItemDetailsOpen, setMenuItemDetailsOpen] = useState(false);

  const showReservationDrawer = () => setReservationDrawerOpen(true);
  const hideReservationDrawer = () => {
    delete router.query['booking'];
    delete router.query['bookingId'];
    delete router.query['reservationId'];
    delete router.query['token'];
    delete router.query['timestamp'];
    router.replace(
      {
        pathname: router.pathname,
        query: router.query
      },
      undefined,
      /**
       * Do not refresh the page
       */
      { shallow: true }
    );
    setReservationDrawerOpen(false);
  }
  
  const highlight = (highlight) => {
    router.push(
      {
        pathname: router.pathname,
        query: removeUndefined({
          ...router.query,
          highlight
        })
      }, undefined, { shallow: true }
    ).then(noop).catch(noop)
  }
  
  const removeHighlight = () => {
    delete router.query['highlight'];
    router.replace(
      {
        pathname: router.pathname,
        query: removeUndefined({
          ...router.query
        })
      }, undefined, { shallow: true }
    ).then(noop).catch(noop)
  }
  
  const showRestaurantDetails = () => highlight('details');
  const hideRestaurantDetails = () => removeHighlight();

  const showTakeawayTypeDrawer = () => highlight('select-takeaway')
  const hideTakeawayTypeDrawer = () => removeHighlight();
  
  const showMenuItemDetails = () => setMenuItemDetailsOpen(true);
  const hideMenuItemDetails = () => setMenuItemDetailsOpen(false);
  
  useEffect(() => {
    if (isEmpty(router.query)) {
      setRestaurantDetailsDrawerOpen(false);
    } else {
      const { highlight } = router.query
      if (highlight === 'details') {
        setRestaurantDetailsDrawerOpen(true)
      } else {
        setRestaurantDetailsDrawerOpen(false)
      }
  
      if (highlight === 'select-takeaway') {
        setTakeawayTypeDrawerOpen(true)
      } else {
        setTakeawayTypeDrawerOpen(false)
      }
    }
    
  }, [router.query])

  const [showOrder, setShowOrder] = useState(false);

  const onPickup = () => {
    showTakeawayTypeDrawer();
  };

  const onRedirectToPickup = (type) => {
    // we check for ?delivery= or ?pickup=
    // setting here ?=xref to have a value
    router
      .push(
        `/restaurant/${slug}/takeaway?${type}=xref`,
        `/restaurant/${slug}/takeaway?${type}=xref`,
        { shallow: true }
      )
      .then(() => {});
  };

  const { restaurant } = useSelector(restaurantsSelectors.getRestaurant);
  const { gallery } = useSelector(restaurantsSelectors.getRestaurantGallery);
  
  const { id, tags = [], hasPickup, name = '', address = {} } = restaurant;
  const { street, number, zipCode, city } = address;
  const resolvedAddress = `${street} ${number}, ${zipCode} ${city}`;
  const mainTag = !isEmpty(tags) ? tags[0].label : '';

  const [isOpen, setIsOpen] = useState(false);

  useEffect(() => {
    window.scrollTo(0, 0);
    dispatch(restaurantsActions.getRestaurantBySlug(slug));
  }, []);

  useEffect(() => {
    if(!isEmpty(id)) {
      getWebshopHours(id)
        .then(({ data }) => {
          setIsOpen(data.isOpen);
        })
        .catch((e) => {
          console.log(e);
        });
    }
  }, [id]);

  useEffect(() => {
    if (!isEmpty(restaurant)) {
      setTakeawayOrderTypes({
        hasPickup: restaurant.hasPickup,
        hasDelivery: restaurant.hasDelivery,
      });
    }
  }, [restaurant]);

  const [showNoPickup, setShowNoPickup] = useState(false);
  const onBlockedPickup = () => {
    setShowNoPickup(true);
    dispatch(restaurantsActions.updateRestaurantPickupRequests(id));
  };

  const isLoading = isEmpty(restaurant);
  
  if (!isEmpty(restaurant) && restaurant.hidden) {
    return null;
  }

  return (
    <div>
      <NextSeo
        title={restaurant.name}
        canonical={"https://eat.allo.restaurant"}
      />
      <main>
        <AppBar />
        <div style={{ paddingTop: 8 }}>
          <Container>
            <div className={classes.wrapper}>
              {!isEmpty(gallery) && (
                <div style={{ marginBottom: 16 }}>
                  <SimpleReactLightbox>
                    <LightBox>
                      <ScrollableGallery
                        loading={false}
                        height={180}
                        width={319}
                        radius={12}
                        items={gallery.map(({ id, thumbnailUrl }) => ({
                          id,
                          src: thumbnailUrl,
                          alt: ''
                        }))}
                      />
                    </LightBox>
                  </SimpleReactLightbox>
                </div>
              )}
              <div className={classes.titleWrapper}>
                {isLoading ? (
                  <Skeleton width="250px">
                    <Typography component="h1" style={{ ...typography.large.semiBold }}>.</Typography>
                  </Skeleton>
                ) : (
                  <Typography component="h1" style={{ ...typography.large.semiBold }}>
                    {restaurant.name}
                  </Typography>
                )}
              </div>
      
              {/*<div className={classes.tags}>*/}
              {/*  <Tag text="€-€€" icon={<EuroIconDark16 />} />*/}
              {/*  <Tag text="15-20 min" icon={<ClockIconDark16 />} />*/}
              {/*  <Tag text="0.4 KM" icon={<PinIconDark16 />} />*/}
              {/*</div>*/}
      
              <div className={classes.detailsButton}>
                {isLoading ? (
                  <Skeleton width="100px">
                    <Typography component="h1" style={{ ...typography.large.semiBold }}>.</Typography>
                  </Skeleton>
                ) : (
                  <div style={{ display: "flex", alignItems: "center" }}>
                    <Typography style={{ ...typography.body.regular }}>{resolvedAddress}</Typography>
                    <span style={{ marginLeft: 4, marginRight: 4, ...typography.extraSmall.regular }}>{` • `}</span>
                    <ButtonBase style={{ padding: 0 }} disableRipple disableTouchRipple onClick={showRestaurantDetails}>
                      <Typography style={{ ...typography.body.medium }}>
                        {t('more-info')}
                      </Typography>
                    </ButtonBase>
                  </div>
                )}
              </div>
            </div>
          </Container>
        </div>
        <div className={classes.menu}>
          <Menu readOnly openDetails={showMenuItemDetails} closeDetails={hideMenuItemDetails}/>
        </div>
        {!isRestaurantDetailsDrawerOpen && !isTakeawayTypeDrawerOpen && !isReservationDrawerOpen && !showOrder && !showNoPickup && !isMenuItemDetailsOpen && (
          <BottomPanel zIndex={1702} transparent wrapperStyle={{ position: "sticky" }}>
            <StartNewOrderBtn
              isOpen={isOpen}
              restaurant={restaurant}
              onClick={hasPickup ? onPickup : onBlockedPickup}
              loading={isEmpty(restaurant)}
              labelKey={isOpen ? 'restaurant-order-btn-label' : 'restaurant-schedule-order-btn-label'}
              onMoreInfo={showRestaurantDetails}
            />
          </BottomPanel>
        )}
      </main>
      {isReservationDrawerOpen && !isEmpty(restaurant) && (
        <ReservationModal
          restaurantId={restaurant.id}
          open={isReservationDrawerOpen}
          onClose={hideReservationDrawer}
        />
      )}
      {/*{isRestaurantDetailsDrawerOpen && (*/}
      {/*  <RestaurantDetailsDrawer*/}
      {/*    open={isRestaurantDetailsDrawerOpen}*/}
      {/*    onClose={hideRestaurantDetails}*/}
      {/*  />*/}
      {/*)}*/}
      {isRestaurantDetailsDrawerOpen && !isLoading && (
        <RestaurantInfoModal
          titleI18n={'activity-title-restaurant-details'}
          open={isRestaurantDetailsDrawerOpen}
          onClose={hideRestaurantDetails}
        />
      )}
      {showOrder && (
        <HistoricalOrderDrawer open={showOrder} onClose={() => setShowOrder(false)} />
      )}
      {showNoPickup && (
        <NoTakeawayModal open={showNoPickup} onClose={() => setShowNoPickup(false)} name={restaurant.name} />
      )}
      {/*{isTakeawayTypeDrawerOpen && (*/}
      {/*  <TakeawayTypeDrawer*/}
      {/*    open={isTakeawayTypeDrawerOpen}*/}
      {/*    onClose={hideTakeawayTypeDrawer}*/}
      {/*    onOrder={onRedirectToPickup}*/}
      {/*    orderTypes={takeawayOrderTypes}*/}
      {/*  />*/}
      {/*)}*/}
      {isTakeawayTypeDrawerOpen && !isLoading && (
        <StartOrderingModal
          titleI18n={'takeaway-type-drawer-title'}
          open={isTakeawayTypeDrawerOpen}
          onClose={hideTakeawayTypeDrawer}
        />
      )}
      <Notification />
    </div>
  );
};

export default withTranslation('common')(Restaurant);
