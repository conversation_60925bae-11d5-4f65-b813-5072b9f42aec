import React, { useEffect, useState } from 'react';
import Container from '@material-ui/core/Container';
import Grid from '@material-ui/core/Grid';
import IconButton from '@material-ui/core/IconButton';
import InputAdornment from '@material-ui/core/InputAdornment';
import { useDispatch, useSelector } from 'react-redux';
import moment from 'moment';
import { NextSeo } from 'next-seo';
import { Checkbox, NativeSelect } from '@material-ui/core';
import ButtonBase from '@material-ui/core/ButtonBase';
import Typography from '@material-ui/core/Typography';
import isEmpty from '../../../utilities/isEmpty';
import ActivityBar from '../../../components/ActivityBar';
import Notification from '../../../components/Notification';
import { DisplayTitleContainer, TextContainer, TitleContainer } from '../../../components/Containers';
import { Paragraph, SmallParagraph, Title } from '../../../components/Text';
import {
  Arrow<PERSON>ackIcon,
  CardIllustration56,
  CardIllustrationGrayScale56,
  CashIllustration56,
  CashIllustrationGrayScale56,
  InAppIllustration56,
  InAppIllustrationGrayScale56,
  ReceiptIcon
} from '../../../components/Icons';
// eslint-disable-next-line import/named
import { withTranslation } from '../../../../i18n';
import isValidEmail from '../../../utilities/isValidEmail';
import { meSelectors, orderSelectors, } from '../../../../redux/selectors';
import { orderActions, restaurantsActions } from '../../../../redux/actions';
import AvatarItem from '../../../components/AvatarItem';
import OrderItem from '../../../components/OrderItem';
import { OrderItemsCategory } from '../../../components/PaymentDrawer';
import { IconCard } from '../../../components/Cards';
import StripePayAction from '../../../components/StripePayAction';
import BottomPanel from '../../../components/BottomPanel';
import useStyles from './styles';
import { googleTags } from '../../../../gtm';
import Field from '../../../components/_input/Field';
import palette from '../../../../styles/palette';
import typography from '../../../../styles/typography';
import isValidMobile from '../../../utilities/isValidMobile';
import {
  getRestaurantConfiguration,
  getDeliveryInfo,
  getWebshopHours,
  updateTakeawayOrderType,
  getGiftCardByCode
} from '../../../../redux/api';
import { CalendarBlankIcon, DeliveryIcon20, PlusIconFilled20 } from '../../../utilities/icons';
import SchedulingHoursModal from '../../../components/_popup/SchedulingHoursModal';

const noop = () => {};

const data = {
  customer: {
    title: 'other',
    firstName: '',
    lastName: '',
    address: {
      street: '',
      number: '',
      zipCode: '',
      additionalInfo: '',
      city: '',
      country: 'DE'
    },
    phone: '',
    email: ''
  },
  takeawayDate: null,
  pickupTime: '',
  notes: '',
  type: 'PICKUP',
  status: null
};

const giftCardData = [{
  code: '',
  balance: '',
  amount: '',
  applied: false
}];

const Checkout = ({ t, back, restaurant = {}, order = {}, submit, isSubmitting, setSubmitting }) => {
  const classes = useStyles();
  const dispatch = useDispatch();

  const { hasDelivery, id } = restaurant;
  const [configuration, setConfiguration] = useState(null);
  const { webshopConfig = {} } = (configuration || {});
  const { deliveryAreas = [], deliveryRadius = [], deliveryFeeMode, allowGiftCards = false } = (webshopConfig || {});
  const { deliveryPaymentChannels = [], pickupPaymentChannels = [] } = (webshopConfig || {});
  const enabledPaymentChannels = {
    PICKUP: (pickupPaymentChannels || ['CASH', 'CARD']),
    DELIVERY: (deliveryPaymentChannels || ['CASH', 'CARD'])
  };
  const [schedulingTimes, setSchedulingTimes] = useState([]);
  const [isOpen, setIsOpen] = useState(false);

  const [takeawayMode, setTakeawayMode] = useState('');
  const isStandardMode = takeawayMode === 'STANDARD';
  const [schedulingHoursOpen, setSchedulingHoursModalOpen] = useState(false);

  const [giftCards, setGiftCards] = useState(giftCardData);
  const [showGiftCardError, setShowGiftCardError] = useState([]);

  useEffect(() => {
    window.scrollTo(0, 0);
    if (restaurant && restaurant.id) {
      dispatch(restaurantsActions.getRestaurantPickupTimes(restaurant.id));
      getRestaurantConfiguration(restaurant.id).then(({ data }) => setConfiguration(data)).catch(() => {});
    }
  }, []);

  useEffect(() => {
    setTakeawayMode(isOpen ? 'STANDARD' : 'SCHEDULED');
  }, [isOpen]);

  const { customer = {} } = useSelector(meSelectors.getMe);

  const schedulingDaysOptions = schedulingTimes.map((p, index) => ({ id: p.value, value: p.value, label: t(`pickup-day-field-option-${index}-label`) }));

  const [form, setForm] = useState(data);
  const [schedulingHoursOptions, setSchedulingHoursOptions] = useState([]);
  const [schedulingDayIndex, setSchedulingDayIndex] = useState(0);

  const [selectedDeliveryRadius, setSelectedDeliveryRadius] = useState({});

  const selectedZipCode = form && form.customer && form.customer.address && form.customer.address.zipCode;
  const selectedZipCodeDeliveryArea = !isEmpty(deliveryAreas) && selectedZipCode && deliveryAreas.find((it) => it.zipCode === selectedZipCode);
  const { minOrderValue = 0, deliveryFee } = deliveryFeeMode === 'ZIP_CODES' ? (selectedZipCodeDeliveryArea || {}) : (selectedDeliveryRadius || {});

  const [withinRadius, setWithinRadius] = useState(null);

  const numbersRegex = /[^+0-9]/g;

  const fetchSchedulingHours = () => {
    getWebshopHours(id)
      .then(({ data }) => {
        const { scheduledTimes = [], isOpen } = (data || {});
        setSchedulingTimes(scheduledTimes);
        setIsOpen(isOpen);
      })
      .catch((e) => {
        console.log(e);
      });
  };

  useEffect(() => {
    fetchSchedulingHours();
  }, [id]);

  useEffect(() => {
    const consolidatedData = data;
    if (!isEmpty(customer)) {
      consolidatedData.customer = customer;
      if (isEmpty(customer.address)) {
        consolidatedData.customer.address = data.customer.address;
      }
    }
    if (!isEmpty(order)) {
      consolidatedData.type = order.type;
    }
    setForm(consolidatedData);
  }, [JSON.stringify(customer)]);

  const generateSchedulingTimesOptions = (times = [], index) => {
    setSchedulingDayIndex(index);
    if (isEmpty(times)) {
      return [{ id: 'none', value: '', label: t('pickup-complete-time-field-none-option-label') }];
    }
    const generatedOptions = (times || [])
      .map((p) => ({ id: p, value: p, label: moment.utc(p, 'HH:mm:ss').format('HH:mm') }));

    return generatedOptions;
  };

  useEffect(() => {
    if (!isEmpty(schedulingTimes) && !form.takeawayDate) {
      setForm({ ...form, takeawayDate: schedulingTimes[0].value });
      setSchedulingHoursOptions(generateSchedulingTimesOptions(schedulingTimes[0].times || [], 0));
    }
  }, [JSON.stringify(schedulingTimes)]);

  const isDelivery = form.type === 'DELIVERY';

  const giftCardTotal = giftCards?.reduce((sum, obj) => sum + Number(obj.amount), 0) ?? 0;
  const { total: unconfirmedTotalWithoutDiscount = 0, discountTotal: unconfirmedTotalDiscount = 0 } = useSelector(orderSelectors.getUnconfirmed);
  const resolvedUnconfirmedTotalDiscount = Math.round((unconfirmedTotalDiscount || 0) * 100) / 100;
  const unconfirmedTotal = unconfirmedTotalWithoutDiscount - resolvedUnconfirmedTotalDiscount - giftCardTotal;
  const resolvedAmount = (unconfirmedTotal || 0).toFixed(2);
  const totalAmount = (isDelivery && deliveryFee) ? (unconfirmedTotal + deliveryFee) : unconfirmedTotal;
  const giftCardAmountExceedsTotalAmount = (unconfirmedTotalDiscount + giftCardTotal) > unconfirmedTotalWithoutDiscount;

  const { summary = {} } = useSelector(orderSelectors.getOrderSummary);
  const { receipts = [] } = summary;
  const [selectedMethod, setSelectedMethod] = useState(enabledPaymentChannels[order.type][0] ?? 'APP');

  const selectPaymentMethod = (method) => {
    setSelectedMethod(method);
  };

  const onChange = (e) => {
    setForm({ ...form, [e.target.name]: e.target.value });
  };

  const onUpdateOrderType = (e) => {
    const newOrderType = e.target.value;
    const fieldName = e.target.name;
    updateTakeawayOrderType(order.id, newOrderType).then(({ data }) => {
      dispatch(orderActions.getOrderSuccess(data));
      setForm({ ...form, [fieldName]: data.type });
    }).catch(() => {});
  };

  const onTimeCheckboxChange = (timeValue) => {
    setForm({ ...form, pickupTime: timeValue });
  };

  const onTakeawayDateChange = (date) => {
    setForm({ ...form, takeawayDate: date, pickupTime: '' });
    const scheduleDateIndex = schedulingTimes.findIndex((i) => i.value === date);
    setSchedulingHoursOptions(generateSchedulingTimesOptions(schedulingTimes[scheduleDateIndex].times || [], scheduleDateIndex));
  };

  const onAddressChange = (e) => {
    const address = { ...form.customer.address, [e.target.name]: e.target.value };
    const customer = { ...form.customer, address };
    setForm({ ...form, customer });
  };

  const onCustomerChange = (e) => {
    let val = e.target.value;
    const { name } = e.target;
    if (name === 'phone') {
      val = ((val || '').replace(numbersRegex, ''));
    }
    const customer = { ...form.customer, [name]: val };
    setForm({ ...form, customer });
  };

  const customerIsValid = form.customer.firstName
    && form.customer.lastName
    && isValidMobile(form.customer.phone)
    && isValidEmail(form.customer.email);

  const addressIsValid = !isDelivery
    || form.customer.address?.street
    && form.customer.address?.number
    && form.customer.address?.zipCode
    && form.customer.address?.zipCode.length === 5;

  const passedThreshold = !isDelivery
    || (!isEmpty(selectedZipCodeDeliveryArea) && (unconfirmedTotal >= minOrderValue)) || (!isEmpty(selectedDeliveryRadius) && (unconfirmedTotal >= minOrderValue));

  let includesArea;

  if (!isDelivery) {
    includesArea = !isDelivery;
  }
  if (isDelivery && deliveryFeeMode === 'ZIP_CODES') {
    includesArea = !isDelivery || selectedZipCodeDeliveryArea;
  } else if (isDelivery && deliveryFeeMode === 'KM_RADIUS') {
    includesArea = !isDelivery || !isEmpty(selectedDeliveryRadius);
  }

  const isValid = customerIsValid
    && passedThreshold
    && includesArea
    // when no pickupTime, asap validates the form if asap is possible to be set
    && (form.pickupTime || (!form.pickupTime && isOpen && schedulingDayIndex === 0))
    && addressIsValid
    && !giftCardAmountExceedsTotalAmount
    && isEmpty(showGiftCardError);

  useEffect(() => {
    if (!isEmpty(form.customer.address) && addressIsValid && deliveryFeeMode === 'KM_RADIUS' && !isEmpty(deliveryRadius)) {
      getDeliveryInfo(restaurant.id, form.customer.address)
        .then(({ data }) => {
          const { deliveryFee, minOrderValue, withinRange } = data;
          if (withinRange) {
            setSelectedDeliveryRadius({ deliveryFee, minOrderValue });
            setWithinRadius(true);
          } else {
            setSelectedDeliveryRadius({});
            setWithinRadius(false);
          }
        })
        .catch(() => {});
    }
  }, [addressIsValid, form.customer.address, deliveryRadius, deliveryFeeMode, restaurant.id]);

  const selectTakeawayMode = (mode) => {
    setTakeawayMode(mode);
    if (mode === 'STANDARD') {
      setSchedulingHoursModalOpen(false);
      setForm({ ...form, pickupTime: '', takeawayDate: null });
    }
    if (mode === 'SCHEDULED') {
      setSchedulingHoursModalOpen(true);
    }
  };

  const getMissingDataMessage = () => {
    if (isValid) return null;

    if (!customerIsValid) {
      if (!isValidMobile(form.customer.phone)) {
        return <span>{t('please-enter-a-valid-phone-number')}</span>;
      }
      if (!isValidEmail(form.customer.email)) {
        return <span>{t('please-enter-a-valid-email-address')}</span>;
      }
      return <span>{t('please-fill-your-info')}</span>;
    }

    if (!addressIsValid) {
      return <span>{t('please-fill-in-your-address')}</span>;
    }

    if (!(form.pickupTime || (!form.pickupTime && isOpen && schedulingDayIndex === 0))) {
      return <span>{t('please-select-time')}</span>;
    }

    if (!includesArea) {
      return <span>{t('order-current-order-order-items-delivery-areas-modal-description')}</span>;
    }

    if (!passedThreshold) {
      return <span>{t('order-current-order-order-items-pickup-threshold-modal-description', { amount: minOrderValue ?? 0 })}</span>;
    }

    if (giftCardAmountExceedsTotalAmount && allowGiftCards) {
      return <span>{t('the-entered-amount-exceeds-gift-card-balance')}</span>;
    }

    return null;
  };

  const isPaymentChannelDisabled = (paymentMethod = '', type) => {
    if (!type) {
      type = form.type;
    }

    if (isEmpty(enabledPaymentChannels)) {
      return false;
    }

    if (isEmpty(enabledPaymentChannels[type])) {
      return false;
    }

    return enabledPaymentChannels[type].indexOf(paymentMethod) === -1;
  };

  const paymentMethods = [
    {
      id: 'APP',
      icon: <InAppIllustrationGrayScale56 />,
      selectedIcon: <InAppIllustration56 />,
      title: t('common-payment-method-app'),
      disabled: isPaymentChannelDisabled('APP')
    }, {
      id: 'CASH',
      icon: <CashIllustrationGrayScale56 />,
      selectedIcon: <CashIllustration56 />,
      title: t('common-payment-method-cash'),
      disabled: isPaymentChannelDisabled('CASH')
    }, {
      id: 'CARD',
      icon: <CardIllustrationGrayScale56 />,
      selectedIcon: <CardIllustration56 />,
      title: t('common-payment-method-card'),
      disabled: isPaymentChannelDisabled('CARD')
    }
  ];

  useEffect(() => {
    if (giftCardAmountExceedsTotalAmount) {
      setShowGiftCardError([...showGiftCardError, 'AMOUNT_EXCEEDED']);
    } else {
      const filteredErrors = showGiftCardError.filter((errorValue) => errorValue !== 'AMOUNT_EXCEEDED');
      setShowGiftCardError(filteredErrors);
    }
  }, [giftCardAmountExceedsTotalAmount]);

  const checkIfLastCardInputCompleted = (array, key) => {
    const lastEnteredCard = array[array.length - 1];
    return !!lastEnteredCard[key];
  };

  const canAddAnotherCard = checkIfLastCardInputCompleted(giftCards, 'amount') && totalAmount > 0;

  const onAddAnotherGiftCard = () => {
    if (!canAddAnotherCard) {
      return;
    }
    const updatedGiftCardInfo = [...giftCards, { code: '', balance: '', amount: '', applied: false }];
    setGiftCards(updatedGiftCardInfo);
  };

  const onGiftCardChange = (e, index) => {
    setShowGiftCardError([]);
    const { name } = e.target;
    const { value } = e.target;
    const updatedGiftCardInfo = [...giftCards];
    if (name === 'amount') {
      const validNumberInput = e.target.value.replace(/[^.\d]|^0+(?=\d)|(\.\d{2})\d+/g, '$1').trim();
      updatedGiftCardInfo[index] = { ...updatedGiftCardInfo[index], [name]: validNumberInput };
    } else {
      updatedGiftCardInfo[index] = { ...updatedGiftCardInfo[index], [name]: value };
    }
    setGiftCards(updatedGiftCardInfo);
  };

  const hasDuplicateCode = (code) => {
    const seenCodes = new Set();
    return giftCards.some((obj) => {
      if (seenCodes.has(obj.code)) {
        return true;
      }
      seenCodes.add(obj.code);
      return false;
    });
  };

  const applyGiftCardCode = (code, index) => {
    const codeAlreadyAdded = hasDuplicateCode(code);
    if (codeAlreadyAdded) {
      setShowGiftCardError([...showGiftCardError, 'CODE_DUPLICATE']);
    } else {
      getGiftCardByCode(restaurant.id, code)
        .then(({ data }) => {
          if (isEmpty(data) || data.initialCashAmount === 0) {
            setShowGiftCardError([...showGiftCardError, 'EMPTY_CARD']);
          }
          const updatedGiftCardInfo = [...giftCards];
          updatedGiftCardInfo[index] = { ...updatedGiftCardInfo[index], id: data.id, amount: data.initialCashAmount > totalAmount ? totalAmount.toFixed(2) : data.initialCashAmount, balance: data.initialCashAmount, applied: true };
          setGiftCards(updatedGiftCardInfo);
        })
        .catch(() => {
          setShowGiftCardError([...showGiftCardError, 'NOT_FOUND']);
        });
    }
  };

  const getGiftCardErrorMessage = () => {
    if (isEmpty(showGiftCardError) || !allowGiftCards) return null;

    if (showGiftCardError.includes('NOT_FOUND')) {
      return (
        <Typography style={{ ...typography.body.medium, marginTop: 8, color: palette.primary['500'] }}>
          {t('no-gift-card-found-with-this-code')}
        </Typography>
      );
    }
    if (showGiftCardError.includes('CODE_DUPLICATE')) {
      return (
        <Typography style={{ ...typography.body.medium, marginTop: 8, color: palette.primary['500'] }}>
          {t('gift-card-with-this-code-already-added')}
        </Typography>
      );
    }
  };

  const removeGiftCard = (code, indexToRemove) => {
    const updatedGiftCardInfo = [...giftCards];
    if (giftCards.length === 1) {
      updatedGiftCardInfo[indexToRemove] = { ...updatedGiftCardInfo[indexToRemove], code: '', amount: '', balance: '', applied: false };
    } else {
      updatedGiftCardInfo.splice(indexToRemove, 1);
    }
    setGiftCards(updatedGiftCardInfo);
  };

  const onSubmit = () => {
    let updatedForm = form;
    if (!isEmpty(giftCards[0].amount) && allowGiftCards) {
      const pendingCardCharges = giftCards.map((card) => ({
        cardId: card.id,
        amount: card.amount
      }));
      updatedForm = { ...updatedForm, pendingCardCharges };
    }
    submit(updatedForm);
  };

  return (
    <>
      <div>
        <NextSeo
          title={restaurant.name}
          nofollow
          noindex
          canonical="https://eat.allo.restaurant"
        />
        <main>
          <ActivityBar
            title={t('activity-title-pickup-complete')}
            back={(
              <IconButton component="a" edge="start" color="inherit" aria-label="menu" onClick={back}>
                <ArrowBackIcon />
              </IconButton>
          )}
          />
          <div style={{ maxWidth: 500, margin: '0 auto' }}>
            <div className={classes.wrapper}>
              <TitleContainer>
                <Container>
                  <Typography style={{ ...typography.medium.medium }}>
                    {t('pickup-complete-user-information-title')}
                  </Typography>
                  <div className={classes.description}>
                    <Typography style={{ ...typography.body.medium }}>
                      {t('welcome-details-form-mandatory')}
                    </Typography>
                  </div>
                </Container>
              </TitleContainer>
              <Container>
                <form className={classes.form}>
                  <Grid container spacing={3}>
                    <Grid item xs={12}>
                      <NativeSelect
                        name="type"
                        value={form.type}
                        onChange={(e) => {
                          onUpdateOrderType(e);
                          if (isPaymentChannelDisabled(selectedMethod, e.target.value)) {
                            setSelectedMethod(enabledPaymentChannels[e.target.value][0]);
                          }
                        }}
                        disabled={isSubmitting}
                        inputProps={{
                          name: 'type',
                          id: 'type-selector',
                        }}
                        input={<Field label={t('pickup-order-type-field-label')} required />}
                        IconComponent={() => null}
                      >
                        {[
                          { id: 'pickup', value: 'PICKUP', label: t('pickup-order-type-field-pickup-option-label') },
                          { id: 'delivery', value: 'DELIVERY', label: t('pickup-order-type-field-delivery-option-label'), disabled: !hasDelivery }
                        ]
                          .map(
                            ({
                              id,
                              value: optionValue,
                              label: optionLabel,
                              disabled
                            }) => <option key={id} value={optionValue} disabled={disabled}>{optionLabel}</option>
                          )}
                      </NativeSelect>
                    </Grid>
                    <Grid item xs={6}>
                      <Field
                        label={`${t('welcome-details-firstName-field-label')}*`}
                        name="firstName"
                        value={form.customer.firstName}
                        onChange={onCustomerChange}
                        variant="filled"
                        id="leviee-first-name-input"
                        autoComplete="off"
                        disabled={isSubmitting}
                      />
                    </Grid>
                    <Grid item xs={6}>
                      <Field
                        label={`${t('welcome-details-lastName-field-label')}*`}
                        name="lastName"
                        value={form.customer.lastName}
                        onChange={onCustomerChange}
                        variant="filled"
                        id="leviee-last-name-input"
                        autoComplete="off"
                        disabled={isSubmitting}
                      />
                    </Grid>
                    {isDelivery && (
                      <>
                        <Grid item xs={8}>
                          <Field
                            label={`${t('welcome-details-street-field-label')}*`}
                            name="street"
                            value={form.customer.address?.street}
                            onChange={onAddressChange}
                            variant="filled"
                            id="leviee-street-input"
                            autoComplete="off"
                            disabled={isSubmitting}
                          />
                        </Grid>
                        <Grid item xs={4}>
                          <Field
                            label={`${t('welcome-details-number-field-label')}*`}
                            variant="filled"
                            name="number"
                            value={form.customer.address?.number}
                            onChange={onAddressChange}
                            id="leviee-number-input"
                            autoComplete="off"
                            disabled={isSubmitting}
                          />
                        </Grid>
                        <Grid item xs={4}>
                          <Field
                            label={`${t('welcome-details-zipCode-field-label')}*`}
                            variant="filled"
                            name="zipCode"
                            value={form.customer.address?.zipCode}
                            onChange={onAddressChange}
                            id="leviee-zip-code-input"
                            autoComplete="off"
                            disabled={isSubmitting}
                          />
                        </Grid>
                      </>
                    )}
                    <Grid item xs={12}>
                      <Field
                        label={`${t('welcome-details-phone-field-label')}*`}
                        InputProps={{
                          startAdornment: <InputAdornment disableTypography style={{ marginTop: 20 }} position="start">+49</InputAdornment>,
                        }}
                        variant="filled"
                        name="phone"
                        type="string"
                        value={form.customer.phone}
                        onChange={onCustomerChange}
                        id="leviee-phone-input"
                        autoComplete="off"
                        disabled={isSubmitting}
                        placeholder="01571234567"
                        error={form.customer.phone ? !isValidMobile(form.customer.phone) : false}
                      />
                    </Grid>
                    <Grid item xs={12}>
                      <Field
                        label={`${t('welcome-details-email-field-label')}*`}
                        defaultValue=""
                        variant="filled"
                        name="email"
                        value={form.customer.email}
                        onChange={onCustomerChange}
                        id="leviee-email-input"
                        autoComplete="off"
                        disabled={isSubmitting}
                        placeholder="<EMAIL>"
                        error={form.customer.email ? !isValidEmail(form.customer.email) : false}
                      />
                    </Grid>
                  </Grid>
                </form>
              </Container>
              <TitleContainer>
                <Container>
                  <Typography style={{ ...typography.medium.medium }}>
                    {isDelivery ? t('pick-up-options') : t('delivery-options')}
                  </Typography>
                </Container>
              </TitleContainer>
              <Container>
                <form className={classes.form}>
                  <Grid container spacing={3}>
                    <ButtonBase
                      style={{ borderRadius: 10, border: isStandardMode ? `1px solid ${palette.primary['500']}` : null, backgroundColor: !isOpen ? palette.grayscale['300'] : palette.grayscale.white, padding: 12, width: '100%', display: 'flex', alignItems: 'center', gap: 8, justifyContent: 'flex-start' }}
                      onClick={() => selectTakeawayMode('STANDARD')}
                      disabled={!isOpen}
                    >
                      <DeliveryIcon20 />
                      <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'flex-start' }}>
                        <Typography style={{ ...typography.body.medium, color: !isOpen ? palette.grayscale['500'] : null }}>{t('pickup-complete-time-field-now-option-label')}</Typography>
                        <Typography style={{ ...typography.body.regular, color: !isOpen ? palette.grayscale['500'] : palette.grayscale['400'] }}>30-50 min</Typography>
                      </div>
                    </ButtonBase>
                    <ButtonBase
                      style={{ borderRadius: 10, border: !isStandardMode ? `1px solid ${palette.primary['500']}` : null, backgroundColor: palette.grayscale.white, padding: 12, width: '100%', display: 'flex', alignItems: 'center', gap: 8, justifyContent: 'flex-start', marginTop: 12, marginBottom: 12 }}
                      onClick={() => selectTakeawayMode('SCHEDULED')}
                    >
                      <CalendarBlankIcon />
                      <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'flex-start' }}>
                        <Typography style={{ ...typography.body.medium }}>{t('pickup-complete-time-field-schedule-option-label')}</Typography>
                        <Typography style={{ ...typography.body.regular, color: palette.grayscale['400'] }}>{!isStandardMode && form.pickupTime && form.takeawayDate ? t('scheduled-for', { date: moment(form.takeawayDate, 'DD-MM-YYYY').format('DD.MM.YYYY'), time: form.pickupTime }) : t('choose-scheduling-time')}</Typography>
                      </div>
                    </ButtonBase>
                    <Grid item xs={12}>
                      <Field
                        label={`${t('pickup-complete-request-to-restaurant-field-label')}`}
                        defaultValue=""
                        variant="filled"
                        name="notes"
                        value={form.notes}
                        onChange={onChange}
                        id="leviee-notes-input"
                        autoComplete="off"
                        disabled={isSubmitting}
                      />
                    </Grid>
                  </Grid>
                </form>
              </Container>
              <div className={classes.receipt}>
                <TitleContainer withText divider>
                  <Container>
                    <TextContainer>
                      <Typography style={{ ...typography.medium.medium }}>
                        {t('order-payment-drawer-order-summary-title')}
                      </Typography>
                      <div className={classes.description}>
                        <Typography style={{ ...typography.body.medium }}>
                          {t('order-payment-drawer-order-summary-description')}
                        </Typography>
                      </div>
                    </TextContainer>
                  </Container>
                </TitleContainer>
                {receipts.map(({ unconfirmed, customer = {} }, index) => (
                  <div key={!isEmpty(customer) ? customer.id : 'guest'}>
                    <Container>
                      {!isEmpty(customer) && (
                        <AvatarItem
                          firstName={customer.firstName}
                          lastName={customer.lastName || ''}
                        />
                      )}
                      {!isEmpty(unconfirmed) && (
                        <>
                          <div className={classes.orderItemWrapper}>
                            {unconfirmed.items
                              .map((i) => (
                                <OrderItem
                                  unitPrice={i.unitPrice}
                                  name={i.name}
                                  qtd={i.qtd}
                                  {...i}
                                />
                              ))}
                          </div>
                          {!isEmpty(customer) && (
                            <div className={classes.userTotal}>
                              <OrderItemsCategory
                                icon={<ReceiptIcon />}
                                text={t('order-current-order-drawer-users-total-label', { name: customer.firstName })}
                                amount={unconfirmed ? unconfirmed.total : 0}
                              />
                            </div>
                          )}
                        </>
                      )}
                    </Container>
                    {receipts.length - 1 > index && <TitleContainer withText />}
                  </div>
                ))}
              </div>
              <div className={classes.paymentMethods}>
                <TitleContainer withText divider>
                  <Container>
                    <TextContainer>
                      <Typography style={{ ...typography.medium.medium }}>
                        {t('order-payment-drawer-payment-method-title')}
                      </Typography>
                      <div className={classes.description}>
                        <Typography style={{ ...typography.body.medium }}>
                          {t('order-payment-drawer-payment-method-description')}
                        </Typography>
                      </div>
                    </TextContainer>
                    <div className={classes.iconCardContainer}>
                      <Grid container spacing={2} justify="center">
                        {paymentMethods.map(({ id, icon, selectedIcon, title, disabled }) => !disabled && (
                          <Grid item xs={4} key={id}>
                            <IconCard
                              showBadge={id === selectedMethod}
                              icon={(id === selectedMethod) ? selectedIcon : icon}
                              title={title}
                              onClick={() => selectPaymentMethod(id)}
                              disabled={disabled || isSubmitting}
                            />
                          </Grid>
                        ))}
                      </Grid>
                    </div>
                    {allowGiftCards && (
                      <div style={{ marginTop: 12 }}>
                        <TextContainer>
                          <Typography style={{ ...typography.medium.medium }}>
                            {t('do-you-want-to-redeem-a-gift-card')}
                          </Typography>
                          <div className={classes.description}>
                            <Typography style={{ ...typography.body.medium }}>
                              {t('enter-your-gift-card-code-and-select-the-desired-amount-in-the-next-step')}
                            </Typography>
                          </div>
                        </TextContainer>
                        {getGiftCardErrorMessage()}
                        {giftCards.map((card, index) => {
                          const { balance, amount, applied } = card;
                          const showAmountError = amount > balance || giftCardAmountExceedsTotalAmount;
                          return (
                            <div>
                              {showAmountError && (
                                <Typography style={{ ...typography.body.medium, marginTop: 8, color: palette.primary['500'] }}>
                                  {t('the-entered-amount-exceeds-gift-card-balance')}
                                </Typography>
                              )}
                              <Grid item xs={12} style={{ marginTop: 8, paddingBottom: 16, marginBottom: 16 }}>
                                <Typography style={{ ...typography.body.medium, marginBottom: 8 }}>
                                  {t('gift-card-code')}
                                </Typography>
                                <div style={{ display: 'flex', gap: 8, alignItems: 'center' }}>
                                  <Field
                                    variant="filled"
                                    name="code"
                                    value={card.code}
                                    onChange={(e) => onGiftCardChange(e, index)}
                                    id="leviee-notes-input"
                                    autoComplete="off"
                                    disabled={isSubmitting}
                                    readOnly={applied}
                                    error={showGiftCardError.includes('CODE_DUPLICATE') || showGiftCardError.includes('NOT_FOUND')}
                                  />
                                  <ButtonBase style={{ borderRadius: 10, border: `1px solid ${palette.grayscale['350']}`, padding: '11px 9px', width: 'fit-content' }} onClick={applied ? () => removeGiftCard(card.code, index) : () => applyGiftCardCode(card.code, index)}>
                                    <Typography style={{ ...typography.body.regular, whiteSpace: 'nowrap', color: applied ? palette.negative['600'] : null }}>
                                      {applied ? t('order-current-order-remove-item-btn-label') : t('common-apply')}
                                    </Typography>
                                  </ButtonBase>
                                </div>
                                {card.balance && (
                                  <div style={{ display: 'flex', gap: 8, alignItems: 'center', marginTop: 12 }}>
                                    <Field
                                      label={`${t('gift-card-balance')}`}
                                      variant="filled"
                                      name="balance"
                                      value={balance}
                                      readOnly
                                      id="leviee-notes-input"
                                      autoComplete="off"
                                      disabled={isSubmitting}
                                    />
                                    <Field
                                      label={`${t('amount-to-redeem')}`}
                                      variant="filled"
                                      name="amount"
                                      value={amount}
                                      type="number"
                                      onChange={(e) => onGiftCardChange(e, index)}
                                      id="leviee-notes-input"
                                      autoComplete="off"
                                      error={amount > balance || showGiftCardError.includes('AMOUNT_EXCEEDED')}
                                    />
                                  </div>
                                )}
                              </Grid>
                            </div>
                          );
                        })}
                        {canAddAnotherCard && (
                          <div style={{ display: 'flex', justifyContent: 'flex-end', borderTop: `1px dashed ${palette.grayscale['350']}`, paddingTop: 12 }}>
                            <ButtonBase style={{ display: 'flex', gap: 4, alignItems: 'center' }} onClick={onAddAnotherGiftCard} disabled={!canAddAnotherCard}>
                              <PlusIconFilled20 />
                              <Typography style={{ ...typography.body.regular, color: palette.grayscale['600'] }}>
                                {t('add-another-gift-card')}
                              </Typography>
                            </ButtonBase>
                          </div>
                        )}
                      </div>
                    )}
                  </Container>
                </TitleContainer>
              </div>
              <TitleContainer withText divider>
                {isDelivery && deliveryFee && (
                  <>
                    <Container>
                      <div className={classes.subtotalRow}>
                        <Paragraph>
                          {t('order-payment-drawer-total-breakdown-subtotal-label')}
                        </Paragraph>
                        <Paragraph>
                          {`${resolvedAmount}€`}
                        </Paragraph>
                      </div>
                    </Container>
                    <Container>
                      <div className={classes.subtotalRow}>
                        <Paragraph>
                          {t('delivery-fee-label')}
                        </Paragraph>
                        <Paragraph>
                          {`${(deliveryFee || 0).toFixed(2)}€`}
                        </Paragraph>
                      </div>
                    </Container>
                  </>
                )}
                {!!unconfirmedTotalDiscount && (
                  <>
                    <Container>
                      <div className={classes.totalRow}>
                        <Typography style={{ ...typography.body.medium }}>
                          {t('subtotal')}
                        </Typography>
                        <Typography style={{ ...typography.body.medium }}>
                          {`${(unconfirmedTotalWithoutDiscount || 0).toFixed(2)}€`}
                        </Typography>
                      </div>
                    </Container>
                    <Container>
                      <div className={classes.totalRow}>
                        <Typography style={{ ...typography.body.medium }}>
                          {t('discount')}
                        </Typography>
                        <Typography style={{ ...typography.body.medium }}>
                          -
                          {`${(unconfirmedTotalDiscount || 0).toFixed(2)}€`}
                        </Typography>
                      </div>
                    </Container>
                  </>
                )}
                {!isEmpty(giftCards) && !isEmpty(giftCards[0].amount) && !giftCardAmountExceedsTotalAmount && (
                  giftCards
                    .filter((card) => card.amount > 0)
                    .map((card) => {
                      const { amount } = card;

                      return (
                        <>
                          <Container>
                            <div className={classes.subtotalRow}>
                              <Typography style={{ ...typography.body.medium }}>
                                {t('gift-card')}
                              </Typography>
                              <Typography style={{ ...typography.body.medium }}>
                                -
                                {`${amount}€`}
                              </Typography>
                            </div>
                          </Container>
                        </>
                      );
                    })
                )}
                <Container>
                  <div className={classes.totalRow}>
                    <Title weight="medium">
                      {t('pickup-success-order-summary-total-label')}
                    </Title>
                    <Title weight="medium" style={{ color: giftCardAmountExceedsTotalAmount ? palette.primary['500'] : null }}>
                      {`${(totalAmount || 0).toFixed(2)}€`}
                    </Title>
                  </div>
                </Container>
              </TitleContainer>
              <DisplayTitleContainer>
                <Container>
                  <div className={classes.vatDisclaimer}>
                    <SmallParagraph>
                      {t('order-summary-vat-disclaimer-label')}
                    </SmallParagraph>
                  </div>
                </Container>
              </DisplayTitleContainer>
              <DisplayTitleContainer>
                <Container>
                  <div className={classes.vatDisclaimer}>
                    <SmallParagraph>
                      {t('order-summary-data-policy-disclaimer-label')}
                    </SmallParagraph>
                  </div>
                </Container>
              </DisplayTitleContainer>
            </div>
          </div>
          {!schedulingHoursOpen && (
            <BottomPanel zIndex={1702} transparent wrapperStyle={{ position: 'sticky' }} size="l">
              {selectedMethod === 'APP' ? (
                <StripePayAction
                  disabled={!isValid}
                  loading={isSubmitting}
                  callback={() => setSubmitting(true)}
                  amount={totalAmount}
                  form={form}
                  restaurantId={restaurant.id}
                  // description={getOrderBlockingMessage()}
                  errorMsg={isValid ? null : getMissingDataMessage()}
                />
              ) : (
                <div style={{
                  position: 'absolute',
                  width: '100%',
                  bottom: 24,
                  paddingLeft: 16,
                  paddingRight: 16
                }}
                >
                  <div style={{
                    maxWidth: 400,
                    margin: '0 auto',
                    paddingTop: 12,
                    paddingLeft: 16,
                    paddingRight: 16,
                    paddingBottom: 12,
                    backgroundColor: palette.grayscale['800'],
                    borderRadius: 12,
                  }}
                  >
                    <div style={{ paddingBottom: 16 }}>
                      <Typography style={{ ...typography.body.medium, color: palette.grayscale.white }}>
                        {t(isValid ? 'checkout' : 'missing-information')}
                      </Typography>
                      <Typography style={{ ...typography.body.regular, color: palette.transparency.light['80'] }}>
                        {/* {(!passedThreshold || !includesArea) && getOrderBlockingMessage()} */}
                        {isValid ? t('confirm-checkout-preference-for-your-purchase') : getMissingDataMessage()}
                      </Typography>
                    </div>
                    <div
                      style={{
                        borderTop: `1px dashed ${palette.transparency.light['20']}`,
                        paddingTop: 12,
                        display: 'flex',
                        justifyContent: 'flex-end',
                        alignItems: 'center',
                        width: '100%'
                      }}
                    >
                      <ButtonBase
                        style={{
                          paddingTop: 12,
                          paddingBottom: 12,
                          paddingLeft: 24,
                          paddingRight: 24,
                          background: (isSubmitting || !isValid) ? palette.grayscale['400'] : palette.primary['500'],
                          borderRadius: 12,
                          width: '100%'
                        }}
                        onClick={isSubmitting ? noop : onSubmit}
                        disabled={!isValid}
                        loading={isSubmitting}
                        id={googleTags.dineOut.completeOrderBtn.id}
                      >
                        <Typography style={{ ...typography.body.medium, color: palette.grayscale['100'] }}>
                          {t('pickup-complete-user-information-complete-order-btn-label')}
                        </Typography>
                      </ButtonBase>
                    </div>
                  </div>
                </div>
              )}
            </BottomPanel>
          )}
          <Notification />
        </main>
      </div>
      {schedulingHoursOpen && (
      <SchedulingHoursModal
        setForm={setForm}
        open={schedulingHoursOpen}
        onClose={() => setSchedulingHoursModalOpen(false)}
        schedulingDaysOptions={schedulingDaysOptions}
        schedulingHoursOptions={schedulingHoursOptions}
        form={form}
        onTakeawayDateChange={onTakeawayDateChange}
        onTimeCheckboxChange={onTimeCheckboxChange}
        isSubmitting={isSubmitting}
        setSchedulingHoursModalOpen={setSchedulingHoursModalOpen}
      />
      )}
    </>
  );
};

export default withTranslation('common')(Checkout);
