import { makeStyles } from '@material-ui/core/styles';
import { fade } from '@material-ui/core';
import { colors } from '../../../../styles/theme';
import palette from '../../../../styles/palette';
import shadows from '../../../../styles/shadows';

const useStyles = makeStyles((theme) => ({
  wrapper: {
    // space below needed to overcome the floating action panel through minHeight and marginBottom
    minHeight: `calc(100vh - ${theme.mixins.toolbar.minHeight}px)`,
    marginBottom: theme.spacing(10)
  },
  formMandatory: {
    marginTop: 16
  },
  form: {
    marginTop: theme.spacing(3) - 2
  },
  row: {
    display: 'flex',
    justifyContent: 'space-between'
  },
  orderItemWrapper: {
    marginTop: 8,
    '& > div:not(:first-child)': {
      marginTop: 8
    }
  },
  description: {
    color: fade(colors.leviee.main.dark, 0.48),
    marginTop: 2
  },
  iconCardContainer: {
    marginTop: 16,
    marginBottom: 16
  },
  totalRow: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
    minHeight: 44
  },
  subtotalRow: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
    minHeight: 32
  },
  vatDisclaimer: {
    color: fade(colors.leviee.main.dark, 0.48)
  },
  actionDisclaimer: {
    padding: theme.spacing(0, '4px', '16px', '4px'),
  },
  dateCardOption: {
    marginTop: 12,
    width: 56,
    height: 62,
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    background: palette.grayscale['100'],
    ...shadows.base,
    borderRadius: 12,
    '&+&': {
      marginLeft: 12,
    }
  },
  selectedDateCardOption: {
    background: palette.primary['500'],
  },
}));

export default useStyles;
