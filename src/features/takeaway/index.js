import React, { useEffect, useState } from 'react';
import Container from '@material-ui/core/Container';
import { useDispatch, useSelector } from 'react-redux';
import { useRouter } from 'next/router';
import Cookies from 'js-cookie';
import { NextSeo } from 'next-seo';
import Typography from '@material-ui/core/Typography';
import ButtonBase from '@material-ui/core/ButtonBase';
import Skeleton from '@material-ui/lab/Skeleton';
import BottomPanel from '../../components/BottomPanel';
import Notification from '../../components/Notification';
import AppBar from '../../components/AppBar';
import Menu from '../../components/Menu';
import { menusSelectors, orderSelectors, restaurantsSelectors } from '../../../redux/selectors';
import { appActions, orderActions } from '../../../redux/actions';
import CurrentOrderAction from '../../components/CurrentOrderAction';
// eslint-disable-next-line import/named
import { withTranslation } from '../../../i18n';
import Success from './success';
import Checkout from './checkout';
import isEmpty from '../../utilities/isEmpty';
import { getRestaurantConfiguration, payPickup, updatePickup } from '../../../redux/api';
import Loader from '../../components/Loader';
import useStyles from './styles';
import typography from '../../../styles/typography';
import RestaurantInfoModal from '../../components/_popup/RestaurantInfoModal';
import OrderBasketModal from '../../components/_popup/OrderBasketModal';

const views = {
  ORDER: 'ORDER',
  CONFIRM: 'CONFIRM',
  SUMMARY: 'SUMMARY'
};

const Takeaway = ({ t, slug, delivery, session, checkout, cancelled }) => {
  const router = useRouter();
  const classes = useStyles();
  const dispatch = useDispatch();

  let pickupId = Cookies.get('lvPickupId');

  const { restaurant = {} } = useSelector(restaurantsSelectors.getRestaurant);
  const { id: restaurantId, address = {} } = restaurant;
  const { street, number, zipCode, city } = address;
  const resolvedAddress = `${street} ${number}, ${zipCode} ${city}`;
  const { quantity, total, discountTotal: unconfirmedTotalDiscount } = useSelector(orderSelectors.getUnconfirmed);
  const unconfirmedTotal = total - unconfirmedTotalDiscount;
  const { order = {} } = useSelector(orderSelectors.getOrder);
  const { menus } = useSelector(menusSelectors.getMenus);

  const [isRestaurantDetailsDrawerOpen, setRestaurantDetailsDrawerOpen] = useState(false);
  const [view, setView] = useState(views.ORDER);
  const [isCurrentOrderDrawerOpen, setCurrentOrderDrawerOpen] = useState(false);
  const [completingOrder, setCompletingOrder] = useState(false);
  const [redirectingToStripeCheckout, setRedirectingToStripeCheckout] = useState(false);
  const [isMenuItemDetailsOpen, setMenuItemDetailsOpen] = useState(false);

  const showRestaurantDetails = () => setRestaurantDetailsDrawerOpen(true);
  const hideRestaurantDetails = () => setRestaurantDetailsDrawerOpen(false);

  const showMenuItemDetails = () => setMenuItemDetailsOpen(true);
  const hideMenuItemDetails = () => setMenuItemDetailsOpen(false);

  const handleRouteChange = () => {
    setRestaurantDetailsDrawerOpen(false);
    setCurrentOrderDrawerOpen(false);
  };

  const onCheckout = () => {
    setView(views.CONFIRM);
    // eslint-disable-next-line no-restricted-globals,no-undef
    history.replaceState({}, document.title, `/restaurant/${slug}/takeaway/checkout`);
  };

  // eslint-disable-next-line no-unused-vars
  const onPayOrder = () => {
    setCompletingOrder(true);
    // eslint-disable-next-line no-console
    console.log(`[LOG] Payment triggered for order id ${Cookies.get('lvPickupId')}`);
    payPickup(pickupId).then(() => {
      Cookies.remove('lvPickupId');
      pickupId = '';
      setView(views.SUMMARY);
    }).catch((err = {}) => {
      // eslint-disable-next-line no-console
      console.log(`[ERROR] Payment trigger Failed for order id ${Cookies.get('lvPickupId')}`);
      const { response = {} } = err;
      const { status } = response;
      // eslint-disable-next-line no-console
      console.log(`[ERROR-STATUS] ${status}`);
    })
      .finally(() => setCompletingOrder(false));
  };

  useEffect(() => {
    router.events.on('beforeHistoryChange', handleRouteChange);
    return () => {
      router.events.off('beforeHistoryChange', handleRouteChange);
    };
  }, [isRestaurantDetailsDrawerOpen]);

  useEffect(() => {
    if (!pickupId && checkout) {
      // eslint-disable-next-line no-restricted-globals,no-undef
      history.replaceState({}, '', `/restaurant/${slug}`);
      router.push(`/restaurant/${slug}`, `/restaurant/${slug}`, { shallow: true });
    } else {
      const orderType = delivery ? 'DELIVERY' : 'PICKUP';
      dispatch(orderActions.createPickupBySlug(slug, orderType));

      // eslint-disable-next-line no-restricted-globals,no-undef
      history.replaceState({}, '', `/restaurant/${slug}/takeaway${checkout ? '/checkout' : ''}`);
    }
  }, []);

  useEffect(() => {
    if (order.id) {
      dispatch(orderActions.pollOrder(order.id));
    }
    return () => {
      dispatch(orderActions.stopPollOrder());
    };
  }, [order.id]);

  useEffect(() => {
    // eslint-disable-next-line no-console
    console.log(`[LOG] Loaded checkout as ${checkout}, cancelled as ${cancelled}, session as ${session}`);

    if (checkout && !cancelled && session) {
      Cookies.remove('lvPickupId');
      pickupId = '';
      setView(views.SUMMARY);
      return;
    }
    if (checkout && pickupId) {
      onCheckout();
    }
  }, [checkout]);

  const [configuration, setConfiguration] = useState(null);

  useEffect(() => {
    if (restaurantId) {
      getRestaurantConfiguration(restaurantId).then(({ data }) => setConfiguration(data)).catch(() => {});
    }
  }, [restaurantId]);

  const onAddOrderItem = (data) => {
    dispatch(orderActions.addPickupItem(data));
  };

  const onRemoveOrderItem = (id) => {
    dispatch(orderActions.removePickupItem(id));
  };

  const onCompleteOrder = (form) => {
    setCompletingOrder(true);
    const { customer, type, takeawayDate, pickupTime, notes, pendingCardCharges } = form;
    updatePickup(order.id, customer, type, null, takeawayDate, pickupTime, notes, pendingCardCharges)
      .then(({ data }) => {
       const { customer: storedCustomer } = data;
        Cookies.remove('lvPickupId');
        dispatch(orderActions.getOrderSuccess(data));
        dispatch(appActions.setMe(storedCustomer));
        setView(views.SUMMARY);
      })
      .catch(({ response }) => {
        const { data = {} } = response;
        const { title } = data;
        router.push(`/restaurant/${slug}/takeaway/checkout`, undefined, { shallow: true });
        if (title) {
          dispatch(appActions.showNotification(title, 'error'));
        };
      })
      .finally(() => setCompletingOrder(false));
  };


  if (isEmpty(restaurant) || isEmpty(order) || isEmpty(menus) || completingOrder) {
    let msg = '';
    if (completingOrder && session) {
      msg = t('common-loader-processing-payment-message');
    }
    if (completingOrder && redirectingToStripeCheckout) {
      msg = t('common-loader-redirecting-to-payment-message');
    }
    return (
      <Loader message={msg} />
    );
  }

  if (view === views.SUMMARY) {
    return (
      <Success
        restaurant={restaurant}
        order={order}
      />
    );
  }

  if (view === views.CONFIRM) {
    return (
      <Checkout
        back={() => {
          setView(views.ORDER);
          // eslint-disable-next-line no-restricted-globals,no-undef
          history.pushState({}, document.title, `/restaurant/${slug}/takeaway`);
        }}
        restaurant={restaurant}
        order={order}
        submit={onCompleteOrder}
        isSubmitting={completingOrder}
        setSubmitting={(state) => {
          setCompletingOrder(state);
          setRedirectingToStripeCheckout(state);
        }}
        cancelled={cancelled}
        paymentRedirect={checkout}
      />
    );
  }

  if (!isEmpty(restaurant) && restaurant.hidden) {
    return null;
  }

  const isLoading = isEmpty(restaurant);

  return (
    <div>
      <NextSeo
        title={restaurant.name}
        nofollow
        noindex
        canonical="https://eat.allo.restaurant"
      />
      <main style={{ minHeight: '100vh' }}>
        <AppBar />
        <div style={{ paddingTop: 8 }}>
          <Container>
            <div className={classes.wrapper}>
              <div className={classes.titleWrapper}>
                {isLoading ? (
                  <Skeleton width="250px">
                    <Typography component="h1" style={{ ...typography.large.semiBold }}>.</Typography>
                  </Skeleton>
                ) : (
                  <Typography component="h1" style={{ ...typography.large.semiBold }}>
                    {restaurant.name}
                  </Typography>
                )}
              </div>
              <div className={classes.detailsButton}>
                {isLoading ? (
                  <Skeleton width="100px">
                    <Typography component="h1" style={{ ...typography.large.semiBold }}>.</Typography>
                  </Skeleton>
                ) : (
                  <div style={{ display: 'flex', alignItems: 'center' }}>
                    <Typography style={{ ...typography.body.regular }}>{resolvedAddress}</Typography>
                    <span style={{ marginLeft: 4, marginRight: 4, ...typography.extraSmall.regular }}>{' • '}</span>
                    <ButtonBase style={{ padding: 0 }} disableRipple disableTouchRipple onClick={showRestaurantDetails}>
                      <Typography style={{ ...typography.body.medium }}>
                        {t('more-info')}
                      </Typography>
                    </ButtonBase>
                  </div>
                )}
              </div>
            </div>
          </Container>
        </div>
        <div className={classes.menu}>
          <Menu
            openDetails={showMenuItemDetails}
            closeDetails={hideMenuItemDetails}
            menus={menus}
            readOnly={!pickupId || isEmpty(order)}
            addOrderItem={onAddOrderItem}
            removeOrderItem={onRemoveOrderItem}
            configuration={configuration}
          />
        </div>
        {!!quantity && !(isRestaurantDetailsDrawerOpen || isCurrentOrderDrawerOpen || isMenuItemDetailsOpen) && (
          <BottomPanel zIndex={1702} transparent wrapperStyle={{ position: 'sticky' }}>
            <CurrentOrderAction
              amount={unconfirmedTotal}
              count={quantity}
              onClick={() => {
                // eslint-disable-next-line no-restricted-globals,no-undef
                history.pushState({}, '', `/restaurant/${slug}/takeaway`);
                setCurrentOrderDrawerOpen(true);
              }}
              orderType={order.type}
            />
          </BottomPanel>
        )}
        <RestaurantInfoModal
          titleI18n="activity-title-restaurant-details"
          open={isRestaurantDetailsDrawerOpen}
          onClose={() => {
            hideRestaurantDetails();
            router.back();
          }}
        />
        {isCurrentOrderDrawerOpen && (
          <OrderBasketModal
            open={isCurrentOrderDrawerOpen}
            onClose={() => {
              setCurrentOrderDrawerOpen(false);
              router.back();
            }}
            onRemove={onRemoveOrderItem}
            onCheckout={onCheckout}
          />
        )}
        <Notification />
      </main>
    </div>
  );
};

export default withTranslation('common')(React.memo(Takeaway));
