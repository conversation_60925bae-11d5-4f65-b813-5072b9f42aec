import React, { useEffect } from 'react';
import Container from '@material-ui/core/Container';
import { useSelector } from 'react-redux';
import Cookies from 'js-cookie';
import { ButtonContainer, DisplayTitleContainer, TitleContainer } from '../../../components/Containers';
import { Paragraph, SmallParagraph, Title } from '../../../components/Text';
// eslint-disable-next-line import/named
import { withTranslation } from '../../../../i18n';
import OrderItem from '../../../components/OrderItem';
import { meSelectors, orderSelectors } from '../../../../redux/selectors';
import useStyles from './styles';
import LinkButton from '../../../components/Buttons/LinkButton';
import { ArrowForwardIconWhite } from '../../../components/Icons';
import isEmpty from '../../../utilities/isEmpty';
import {NextSeo} from "next-seo";
import animationData from '../../../animations/salad.json';
// import Lottie from 'react-lottie';
import typography from "../../../../styles/typography";
import {Typography} from "@material-ui/core";

const Success = ({ t, restaurant = {}, order = {} }) => {
  const classes = useStyles();

  useEffect(() => {
    window.scrollTo(0, 0);
    Cookies.remove('lvPickupId');
  }, []);

  const { name } = restaurant;
  const { total = 0, items = [], type, deliveryFee = 0, giftCardCharges = [] } = order;
  const { allAmountCharged, cards = [] } = giftCardCharges;

  const { customer = {} } = useSelector(meSelectors.getMe);
  const { total: unconfirmedTotalWithoutDiscount = 0, discountTotal: unconfirmedTotalDiscount = 0 } = useSelector(orderSelectors.getUnconfirmed);
  const unconfirmedTotal = unconfirmedTotalWithoutDiscount - unconfirmedTotalDiscount;
  const { total: confirmedTotalWithoutDiscount = 0, discountTotal: confirmedTotalDiscount = 0 } = useSelector(orderSelectors.getConfirmed);
  const confirmedTotal = confirmedTotalWithoutDiscount - confirmedTotalDiscount;
  const itemsTotal = (unconfirmedTotal + confirmedTotal);
  const resolvedAmount = (itemsTotal).toFixed(2);
  const { firstName, lastName } = customer;

  const isDelivery = type === 'DELIVERY';
  let totalAmount = (isDelivery && deliveryFee) ? (itemsTotal + deliveryFee - allAmountCharged) : itemsTotal - allAmountCharged;

  if (order.status === 'CLOSED') {
    totalAmount = total;
  }
  totalAmount = (totalAmount || 0).toFixed(2);
  
  const defaultOptions = {
    loop: true,
    autoplay: true,
    animationData: animationData,
    rendererSettings: {
      preserveAspectRatio: "xMidYMid slice"
    }
  };

  return (
    <div>
      <NextSeo
        title={restaurant.name}
        nofollow
        noindex
        canonical={"https://eat.allo.restaurant"}
      />
      <main>
        <div className={classes.wrapper}>
          <div className={classes.top}>
            <Container>
              {/*<div className={classes.img}>*/}
              {/*  <Lottie*/}
              {/*    options={defaultOptions}*/}
              {/*    height={200}*/}
              {/*    width={180}*/}
              {/*  />*/}
              {/*</div>*/}
              <TitleContainer withText>
                {!isEmpty(customer) && (
                  <Typography style={{ ...typography.medium.medium }}>
                    {t('pickup-success-pending-title', { name: `${firstName} ${lastName || ''}`, restaurant: name })}
                  </Typography>
                )}
                <div className={classes.description}>
                  <Typography style={{ ...typography.body.regular }}>
                    {t('pickup-success-pending-description')}
                  </Typography>
                </div>
              </TitleContainer>
            </Container>
            <div className={classes.action}>
              <ButtonContainer>
                <LinkButton
                  href="/"
                  shallow={false}
                  endIcon={<ArrowForwardIconWhite />}
                  className={classes.actionBtn}
                >
                  {t('order-success-return-to-landing-btn-label')}
                </LinkButton>
              </ButtonContainer>
            </div>
          </div>
          <div className={classes.summary}>
            <Container>
              <TitleContainer withText>
                <Title weight="medium">
                  {t('pickup-success-order-summary-title')}
                </Title>
              </TitleContainer>
              <div className={classes.orderItemWrapper}>
                {!isEmpty(items) && items
                  .map((i) => (
                    <OrderItem
                      unitPrice={i.unitPrice}
                      name={i.name}
                      qtd={i.qtd}
                      {...i}
                    />
                  ))}
              </div>
            </Container>
            <TitleContainer withText divider>
              {isDelivery && deliveryFee && (
                <>
                  <Container>
                    <div className={classes.subtotalRow}>
                      <Paragraph>
                        {t('order-payment-drawer-total-breakdown-subtotal-label')}
                      </Paragraph>
                      <Paragraph>
                        {`${resolvedAmount}€`}
                      </Paragraph>
                    </div>
                  </Container>
                  <Container>
                    <div className={classes.subtotalRow}>
                      <Paragraph>
                        {t('delivery-fee-label')}
                      </Paragraph>
                      <Paragraph>
                        {`${(deliveryFee || 0).toFixed(2)}€`}
                      </Paragraph>
                    </div>
                  </Container>
                </>
              )}
              {giftCardCharges && cards && !isEmpty(cards) && (
                cards
                  .map((card) => {
                    const { amountCharged } = card;

                    return (
                      <>
                        <Container>
                          <div className={classes.subtotalRow}>
                            <Paragraph>
                              {t('gift-card')}
                            </Paragraph>
                            <Paragraph>
                                -
                                {`${amountCharged}€`}
                            </Paragraph>
                          </div>
                        </Container>
                      </>
                    );
                  })
              )}
              <Container>
                <div className={classes.totalRow}>
                  <Title weight="medium">
                    {t('pickup-success-order-summary-total-label')}
                  </Title>
                  <Title weight="medium">
                    {`${totalAmount}€`}
                  </Title>
                </div>
              </Container>
            </TitleContainer>
            <DisplayTitleContainer>
              <Container>
                <div className={classes.vatDisclaimer}>
                  <SmallParagraph>
                    {t('order-summary-vat-disclaimer-label')}
                  </SmallParagraph>
                </div>
              </Container>
            </DisplayTitleContainer>
          </div>
        </div>
      </main>
    </div>
  );
};

export default withTranslation('common')(Success);
