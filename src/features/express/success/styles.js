import { makeStyles } from '@material-ui/core/styles';
import { fade } from '@material-ui/core';
import { colors } from '../../../../styles/theme';
import palette from "../../../../styles/palette";

const useStyles = makeStyles((theme) => ({
  wrapper: {
    // space below needed to overcome the floating action panel through minHeight and marginBottom
    minHeight: "100vh",
    maxWidth: 500,
    margin: "0 auto",
    background: palette.grayscale["200"],
  },
  top: {
    paddingTop: 56,
    paddingBottom: 16,
    color: colors.leviee.main.white,
    background: palette.grayscale["200"],
  },
  img: {
    marginBottom: 32
  },
  title: {
    marginTop: 12
  },
  description: {
    marginTop: 6
  },
  summary: {
    paddingTop: 16
  },
  totalRow: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
    minHeight: 44
  },
  subtotalRow: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
    minHeight: 32
  },
  vatDisclaimer: {
    color: fade(colors.leviee.main.dark, 0.48)
  },
  actionBtn: {
    width: 'auto',
    color: theme.palette.common.white,
    background: theme.palette.primary.main,
    height: 48
  }
}));

export default useStyles;
