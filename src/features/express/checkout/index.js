import React, { useEffect, useState } from 'react';
import Container from '@material-ui/core/Container';
import Grid from '@material-ui/core/Grid';
import { useDispatch, useSelector } from 'react-redux';
import { NextSeo } from 'next-seo';
import ButtonBase from '@material-ui/core/ButtonBase';
import Typography from '@material-ui/core/Typography';
import Skeleton from '@material-ui/lab/Skeleton';
import { useRouter } from 'next/router';
import { useMediaQuery } from '@material-ui/core';
import moment from 'moment';
import isEmpty from '../../../utilities/isEmpty';
import Notification from '../../../components/Notification';
import { DisplayTitleContainer, TextContainer, TitleContainer } from '../../../components/Containers';
import { Paragraph, SmallParagraph, Title } from '../../../components/Text';
import {
  ReceiptIcon
} from '../../../components/Icons';
import { withTranslation } from '../../../../i18n';
import { orderSelectors, restaurantsSelectors } from '../../../../redux/selectors';
import AvatarItem from '../../../components/AvatarItem';
import OrderItem from '../../../components/OrderItem';
import { OrderItemsCategory } from '../../../components/PaymentDrawer';
import BottomPanel from '../../../components/BottomPanel';
import useStyles from './styles';
import Field from '../../../components/_input/Field';
import palette from '../../../../styles/palette';
import typography from '../../../../styles/typography';
import AppBar from '../../../components/AppBar';
import { orderActions, restaurantsActions } from '../../../../redux/actions';
import EmptyScreen from '../../../components/_placeholder/EmptyScreen';
import { NoItems80 } from '../../../utilities/icons';
import removeUndefined from '../../../utilities/removeUndefined';
import {
  addPromotionToCart,
  getCurrentCustomer,
  getRestaurantConfiguration,
  removePromoCodeFromCart,
  requestOrderCartPayment
} from '../../../../redux/api';
import { MainButton } from '../../../components/Buttons';
import CartPaymentStatusModal from '../../../components/_popup/CartPaymentStatusModal';
import Success from '../success';

const noop = () => {};

const Checkout = ({ t }) => {
  const router = useRouter();
  const classes = useStyles();
  const dispatch = useDispatch();
  const { slug, status: paymentStatus } = router.query;

  const isMobile = useMediaQuery('(max-width:980px)');

  const { restaurant } = useSelector(restaurantsSelectors.getRestaurant);
  const { id: restaurantId, address = {} } = restaurant;
  const { street, number, zipCode, city } = address;
  const resolvedAddress = `${street} ${number}, ${zipCode} ${city}`;

  const [customer, setCustomer] = useState(null);

  const isLoading = isEmpty(restaurant);

  const back = () => {
    router.replace(
      {
        pathname: router.pathname.replace('/checkout', ''),
        query: removeUndefined({
          ...router.query
        })
      }, undefined, { shallow: true }
    ).then(noop).catch(noop);
  };

  useEffect(() => {
    window.scrollTo(0, 0);
    if (isEmpty(restaurant)) {
      dispatch(restaurantsActions.getRestaurantBySlugAndOrderType(slug, 'EXPRESS'));
    }
  }, []);

  useEffect(() => {
    if (restaurantId) {
      getCurrentCustomer().then(({ data }) => {
        setCustomer(data);
        dispatch(orderActions.pollOrderCart());
      }).catch(() => {
        back();
      });
      return () => {
        dispatch(orderActions.stopPollOrderCart());
      };
    }
  }, [restaurantId]);

  const { total: unconfirmedTotal = 0 } = useSelector(orderSelectors.getUnconfirmed);

  const { order = {} } = useSelector(orderSelectors.getOrder);
  const {
    status,
    order: internalOrder = {},
    paymentRequestedAt,
    itemsTotal = 0,
    totalDiscounts = 0,
    total = 0,
    ratesAndBenefits = []
  } = order;
  const receipts = [{
    customer,
    confirmed: null,
    unconfirmed: { items: order.items ?? [], total: order.total }
  }];
  const { ratesAndBenefitsIdentifiers } = (ratesAndBenefits || []);
  const appliedPromotionCode = ratesAndBenefitsIdentifiers
    && ratesAndBenefitsIdentifiers[0]
    && ratesAndBenefitsIdentifiers[0].code;
  const hasPromotionCode = !!appliedPromotionCode;

  const isPaymentRequestOlderThan30Seconds = (date) => {
    if (!date) {
      return false;
    }

    try {
      const dateToMoment = moment(paymentRequestedAt, 'yyyy-MM-DDTHH:mm:ss.sssZ');
      const before30SecondsFromNow = new Date(new Date().getTime() - (1000 * 30));
      const isOlderThan30Seconds = dateToMoment.isBefore(before30SecondsFromNow, 'seconds');
      return isOlderThan30Seconds;
    } catch (e) {
      return false;
    }
  };

  const paymentRequestedBefore30Seconds = paymentRequestedAt && isPaymentRequestOlderThan30Seconds(paymentRequestedAt);
  const isPaymentInProgress = status === 'PAYMENT_REQUESTED' && (paymentStatus !== 'failure') && !paymentRequestedBefore30Seconds;
  const isCompleted = status === 'COMPLETED';

  const [processingPayment, setProcessingPayment] = useState(isPaymentInProgress);

  useEffect(() => {
    if (status && status === 'PAYMENT_REQUESTED' && (paymentStatus !== 'failure') && !isPaymentRequestOlderThan30Seconds(paymentRequestedAt)) {
      setProcessingPayment(true);
    } else {
      setProcessingPayment(false);
    }
  }, [status, paymentStatus, paymentRequestedAt]);

  const startPayment = () => {
    setProcessingPayment(true);
    requestOrderCartPayment(restaurantId, {
      paymentChannel: 'STRIPE'
    }).then(({ data: paymentRequest }) => {
      const { paymentUrl } = paymentRequest;
      router.push(paymentUrl).then(() => {});
    }).catch(() => setProcessingPayment(false));
  };

  const [promoCodeValue, setPromoCodeValue] = useState(null);
  const [promoCodeError, setPromoCodeError] = useState(false);
  const [removePromoCodeError, setRemovePromoCodeError] = useState(false);
  const addPromoCodeToCart = () => {
    addPromotionToCart(restaurantId, promoCodeValue).then(() => {
      dispatch(orderActions.getOrderCart());
    }).catch(() => {
      setPromoCodeError(true);
    });
  };
  
  const removePromoCode = () => {
    setRemovePromoCodeError(false);
    setPromoCodeValue("")
    removePromoCodeFromCart(restaurantId).then(() => {
      dispatch(orderActions.getOrderCart());
    }).catch((error) => {
      console.error('Error removing promo code:', error);
      setRemovePromoCodeError(true);
    });
  };

  const [config, setConfig] = useState(null);
  useEffect(() => {
    getRestaurantConfiguration(restaurantId)
      .then(({ data }) => {
        setConfig(data);
      })
      .catch((e) => {
        console.log(e);
      });
  }, [restaurantId]);

  const { status: internalOrderStatus } = internalOrder;

  if (internalOrderStatus === 'CLOSED') {
    return <Success restaurant={restaurant} order={internalOrder} />;
  }

  return (
    <div>
      <NextSeo
        title={restaurant.name}
        nofollow
        noindex
        canonical="https://eat.allo.restaurant"
      />
      <main style={{ minHeight: '100vh' }}>
        <AppBar />
        <div style={{ paddingTop: 8 }}>
          <Container>
            <div className={classes.infoWrapper}>
              <div className={classes.titleWrapper}>
                {isLoading ? (
                  <Skeleton width="250px">
                    <Typography component="h1" style={{ ...typography.large.semiBold }}>.</Typography>
                  </Skeleton>
                ) : (
                  <Typography component="h1" style={{ ...typography.large.semiBold }}>
                    {restaurant.name}
                  </Typography>
                )}
              </div>
              <div className={classes.detailsButton}>
                {isLoading ? (
                  <Skeleton width="100px">
                    <Typography component="h1" style={{ ...typography.large.semiBold }}>.</Typography>
                  </Skeleton>
                ) : (
                  <div style={{ display: 'flex', alignItems: 'center' }}>
                    <Typography style={{ ...typography.body.regular }}>{resolvedAddress}</Typography>
                  </div>
                )}
              </div>
            </div>
          </Container>
        </div>
        <div style={{ marginTop: 12 }}>
          <Container>
            <div style={{ display: 'flex', paddingTop: 8, paddingBottom: 8 }}>
              <ButtonBase
                disableRipple
                disableTouchRipple
                style={{ background: palette.grayscale['300'], borderRadius: 12, paddingLeft: 10, paddingRight: 10, ...typography.body.medium, paddingTop: 6, paddingBottom: 6 }}
                onClick={back}
              >
                ⬅️
                {' '}
                {t('back-to-order')}
              </ButtonBase>
            </div>
          </Container>
          {!isEmpty(order) && (
            <Container>
              <div style={{ display: 'flex', flexDirection: 'row', width: '100%' }}>
                <div style={{ flex: 1 }}>
                  <div className={classes.wrapper}>
                    {/* <div style={{ marginTop: 24, borderBottom: `1px solid ${palette.grayscale["350"]}` }} /> */}
                    {/* customer data */}
                    <TitleContainer divider>
                      <Container style={{ paddingLeft: 0, paddingRight: 0 }}>
                        <Typography style={{ ...typography.medium.medium }}>
                          {t('pickup-complete-about-your-order-title')}
                        </Typography>
                        {/* <div className={classes.description}> */}
                        {/*  <Typography style={{ ...typography.body.medium }}> */}
                        {/*    {t('welcome-details-form-mandatory')} */}
                        {/*  </Typography> */}
                        {/* </div> */}
                      </Container>
                    </TitleContainer>
                    <Container style={{ paddingLeft: 0, paddingRight: 0 }}>
                      <form className={classes.form}>
                        <Grid container spacing={3}>
                          <Grid item xs={6} style={{ maxWidth: 300 }}>
                            <Field
                              label={`${t('welcome-details-firstName-field-label')}`}
                              name="firstName"
                              value={customer && customer.firstName}
                              // onChange={onCustomerChange}
                              variant="filled"
                              id="leviee-first-name-input"
                              autoComplete="off"
                              disabled
                              // disabled={isLoading || processingPayment}
                              skipNavigatorCheck
                            />
                          </Grid>
                          <Grid item xs={12} style={{ maxWidth: 500 }}>
                            <Field
                              label={`${t('welcome-details-email-field-label')}`}
                              defaultValue=""
                              variant="filled"
                              name="email"
                              value={customer && customer.email}
                              // onChange={onCustomerChange}
                              id="leviee-email-input"
                              autoComplete="off"
                              disabled
                              skipNavigatorCheck
                            />
                          </Grid>
                          {config?.allowPromoCodesInScanToOrderExpress && (
                            <>
                              <Grid item xs={12} style={{ maxWidth: 500, display: 'flex', flexDirection: 'column', gap: 8 }}>
                                <div style={{ maxWidth: 500, display: 'flex', alignItems: 'flex-end' }}>
                                  <Field
                                    label={`${t('promo-code-field-label')}`}
                                    placeholder={hasPromotionCode ? '' : 'SALE10'}
                                    variant="filled"
                                    name="promoCode"
                                    value={hasPromotionCode ? appliedPromotionCode : promoCodeValue}
                                    onChange={(e) => {
                                      if (!hasPromotionCode) {
                                        setPromoCodeValue(e.target.value);
                                        if (promoCodeError) {
                                          setPromoCodeError(false);
                                        }
                                      }
                                    }}
                                    id="leviee-email-input"
                                    autoComplete="off"
                                    skipNavigatorCheck
                                    error={promoCodeError}
                                    disabled={hasPromotionCode}
                                  />
                                  <div style={{ marginLeft: 8 }}>
                                    {!hasPromotionCode ? (
                                      <ButtonBase
                                        onClick={addPromoCodeToCart}
                                        disableRipple
                                        disableTouchRipple
                                        disabled={isEmpty(promoCodeValue) || promoCodeError}
                                        style={{
                                          paddingTop: 12,
                                          paddingBottom: 12,
                                          paddingLeft: 24,
                                          paddingRight: 24,
                                          background: palette.primary['500'],
                                          opacity: isEmpty(promoCodeValue) || promoCodeError ? 0.4 : 1,
                                          borderRadius: 12,
                                          marginLeft: 8
                                        }}
                                      >
                                        <Typography style={{ ...typography.body.medium, color: palette.grayscale.white }}>
                                          {t('common-apply')}
                                        </Typography>
                                      </ButtonBase>
                                    ) : (
                                      <ButtonBase
                                        onClick={removePromoCode}
                                        disableRipple
                                        disableTouchRipple
                                        style={{
                                          paddingTop: 12,
                                          paddingBottom: 12,
                                          paddingLeft: 24,
                                          paddingRight: 24,
                                          borderRadius: 12,
                                          marginLeft: 8,
                                          border: `1px solid ${palette.grayscale["350"]}`
                                        }}
                                      >
                                        <Typography style={{ ...typography.body.regular }}>
                                          {t('order-current-order-remove-item-btn-label')}
                                        </Typography>
                                      </ButtonBase>
                                    )}
                                  </div>
                                </div>
                                {removePromoCodeError && (
                                  <Typography style={{ ...typography.body.regular, color: palette.negative["500"] }}>
                                      {t('express-checkout-promocode-error-message')}
                                  </Typography>
                                )}
                              </Grid>
                            </>
                          )}
                        </Grid>
                      </form>
                    </Container>
                    {/* notes */}
                    {/* <Container> */}
                    {/*  <form className={classes.form}> */}
                    {/*    <Grid container spacing={3}> */}
                    {/*      <Grid item xs={12}> */}
                    {/*        <Field */}
                    {/*          label={`${t('pickup-complete-request-to-restaurant-field-label')}`} */}
                    {/*          defaultValue="" */}
                    {/*          variant="filled" */}
                    {/*          name="notes" */}
                    {/*          value={order.notes} */}
                    {/*          // onChange={(e) => setNotes(e.target.value)} */}
                    {/*          id="leviee-notes-input" */}
                    {/*          autoComplete="off" */}
                    {/*          // disabled={isLoading || processingPayment} */}
                    {/*          skipNavigatorCheck */}
                    {/*          disabled */}
                    {/*        /> */}
                    {/*      </Grid> */}
                    {/*    </Grid> */}
                    {/*  </form> */}
                    {/* </Container> */}

                    {/* receipt */}
                    <div className={classes.receipt}>
                      <TitleContainer withText divider>
                        <Container style={{ paddingLeft: 0, paddingRight: 0 }}>
                          <TextContainer>
                            <Typography style={{ ...typography.medium.medium }}>
                              {t('order-payment-drawer-order-summary-title')}
                            </Typography>
                            {/* <div className={classes.description}> */}
                            {/*  <Typography style={{ ...typography.body.medium }}> */}
                            {/*    {t('order-payment-drawer-order-summary-description')} */}
                            {/*  </Typography> */}
                            {/* </div> */}
                          </TextContainer>
                        </Container>
                      </TitleContainer>
                      {receipts.map(({ unconfirmed, customer = {} }, index) => {
                        if (isEmpty(unconfirmed) || isEmpty(unconfirmed.items)) {
                          return null;
                        }
                        return (
                          <div key={!isEmpty(customer) ? customer.id : 'guest'}>
                            <Container>
                              {!isEmpty(customer) && (
                                <AvatarItem
                                  firstName={customer.firstName || ''}
                                  lastName={customer.lastName ?? ''}
                                />
                              )}
                              {!isEmpty(unconfirmed) && (
                                <>
                                  <div className={classes.orderItemWrapper}>
                                    {unconfirmed.items
                                      .map((i) => (
                                        <OrderItem
                                          unitPrice={i.unitPrice}
                                          name={i.name}
                                          qtd={i.qtd}
                                          {...i}
                                        />
                                      ))}
                                  </div>
                                  {!isEmpty(customer) && (
                                    <div className={classes.userTotal}>
                                      <OrderItemsCategory
                                        icon={<ReceiptIcon />}
                                        text={t('order-current-order-drawer-users-total-label', { name: customer.firstName })}
                                        amount={itemsTotal}
                                      />
                                    </div>
                                  )}
                                </>
                              )}
                            </Container>
                            {receipts.length - 1 > index && <TitleContainer withText />}
                          </div>
                        );
                      })}
                      {!unconfirmedTotal && !isCompleted && (
                        <div style={{ display: 'flex', paddingTop: 32, paddingBottom: 32, justifyContent: 'center' }}>
                          <EmptyScreen
                            icon={<NoItems80 />}
                            titleI18nKey="empty-basket"
                            descriptionI18nKey="add-items-to-proceed"
                          />
                        </div>
                      )}
                    </div>

                    {/* total */}
                    {isMobile && (
                      <div>
                        <TitleContainer withText divider>
                          <Container>
                            {!!totalDiscounts && totalDiscounts > 0 && (
                              <div className={classes.totalRow}>
                                <Paragraph weight="regular">
                                  {`${t('discount')}${hasPromotionCode ? ` (${appliedPromotionCode})` : ''}`}
                                </Paragraph>
                                <Paragraph weight="regular">
                                  {`- ${(totalDiscounts || 0).toFixed(2)}€`}
                                </Paragraph>
                              </div>
                            )}
                            <div className={classes.totalRow}>
                              <Title weight="medium">
                                {t('pickup-success-order-summary-total-label')}
                              </Title>
                              <Title weight="medium">
                                {`${(total || 0).toFixed(2)}€`}
                              </Title>
                            </div>
                          </Container>
                        </TitleContainer>
                        <DisplayTitleContainer>
                          <Container>
                            <div className={classes.vatDisclaimer}>
                              <SmallParagraph>
                                {t('order-summary-vat-disclaimer-label')}
                              </SmallParagraph>
                            </div>
                          </Container>
                        </DisplayTitleContainer>
                        <DisplayTitleContainer>
                          <Container>
                            <div className={classes.vatDisclaimer}>
                              <SmallParagraph>
                                {t('order-summary-data-policy-disclaimer-label')}
                              </SmallParagraph>
                            </div>
                          </Container>
                        </DisplayTitleContainer>
                      </div>
                    )}

                  </div>
                </div>
                {!isMobile && (
                  <div style={{ flex: 1, width: '100%', marginLeft: 32 }}>

                    {/* total */}
                    <div>
                      <TitleContainer withText divider>
                        <Container style={{ paddingLeft: 0, paddingRight: 0 }}>
                          {!!totalDiscounts && totalDiscounts > 0 && (
                            <div className={classes.totalRow}>
                              <Paragraph weight="regular">
                                {`${t('discount')}${hasPromotionCode ? ` (${appliedPromotionCode})` : ''}`}
                              </Paragraph>
                              <Paragraph weight="regular">
                                {`- ${(totalDiscounts || 0).toFixed(2)}€`}
                              </Paragraph>
                            </div>
                          )}
                          <div className={classes.totalRow}>
                            <Title weight="medium">
                              {t('pickup-success-order-summary-total-label')}
                            </Title>
                            <Title weight="medium">
                              {`${(total || 0).toFixed(2)}€`}
                            </Title>
                          </div>
                        </Container>
                      </TitleContainer>
                      <DisplayTitleContainer>
                        <Container style={{ paddingLeft: 0, paddingRight: 0 }}>
                          <div className={classes.vatDisclaimer}>
                            <SmallParagraph>
                              {t('order-summary-vat-disclaimer-label')}
                            </SmallParagraph>
                          </div>
                        </Container>
                      </DisplayTitleContainer>
                      <DisplayTitleContainer>
                        <Container style={{ paddingLeft: 0, paddingRight: 0 }}>
                          <div className={classes.vatDisclaimer}>
                            <SmallParagraph>
                              {t('order-summary-data-policy-disclaimer-label')}
                            </SmallParagraph>
                          </div>
                        </Container>
                      </DisplayTitleContainer>
                    </div>

                  </div>
                )}
              </div>
            </Container>
          )}
          {!(processingPayment || isCompleted) && (
            <BottomPanel zIndex={1702} transparent wrapperStyle={{ position: 'sticky' }} size="l">
              <div style={{ position: 'absolute', width: '100%', bottom: 24, paddingLeft: 16, paddingRight: 16 }}>

                {/* to be paid */}
                {!!unconfirmedTotal && (
                  <div style={{ maxWidth: 400, margin: '0 auto', paddingTop: 12, paddingLeft: 16, paddingRight: 16, paddingBottom: 12, backgroundColor: palette.grayscale['800'], borderRadius: 12, }}>
                    <div style={{ paddingBottom: 16 }}>
                      <Typography style={{ ...typography.body.medium, color: palette.grayscale.white }}>
                        {/* {t(isValid ? 'checkout' : 'missing-information')} */}
                        {t('checkout')}
                      </Typography>
                      <Typography style={{ ...typography.body.regular, color: palette.transparency.light['80'] }}>
                        {/* {(!passedThreshold || !includesArea) && getOrderBlockingMessage()} */}
                        {/* {isValid ? t('confirm-checkout-preference-for-your-purchase') : getMissingDataMessage()} */}
                        {t('confirm-checkout-preference-for-your-purchase')}
                      </Typography>
                    </div>
                    <div style={{ borderTop: `1px dashed ${palette.transparency.light['20']}`, paddingTop: 12, display: 'flex', justifyContent: 'flex-end', alignItems: 'center', width: '100%' }}>
                      <MainButton
                        style={{ paddingTop: 12, paddingBottom: 12, paddingLeft: 24, paddingRight: 24, background: palette.primary['500'], borderRadius: 12, width: '100%' }}
                        onClick={(isLoading || processingPayment) ? noop : startPayment}
                        disabled={!unconfirmedTotal || (isLoading || processingPayment)}
                        loading={(isLoading || processingPayment)}
                      >
                        <Typography style={{ ...typography.body.medium, color: palette.grayscale['100'] }}>
                          {t(isPaymentInProgress ? 'order-payment-btn-label-disabled' : 'order-payment-btn-label')}
                        </Typography>
                      </MainButton>
                    </div>
                  </div>
                )}

                {/* no items */}
                {!unconfirmedTotal && !isCompleted && (
                  <div style={{ maxWidth: 400, margin: '0 auto' }}>
                    <div style={{ paddingTop: 12, display: 'flex', justifyContent: 'flex-end', alignItems: 'center', width: '100%' }}>
                      <ButtonBase
                        style={{ paddingTop: 12, paddingBottom: 12, paddingLeft: 24, paddingRight: 24, background: palette.primary['500'], borderRadius: 12, width: '100%' }}
                        onClick={back}
                      >
                        <Typography style={{ ...typography.body.medium, color: palette.grayscale['100'] }}>
                          {t('back-to-order')}
                        </Typography>
                      </ButtonBase>
                    </div>
                  </div>
                )}

                {/* paid */}
                {isCompleted && (
                  <div style={{ maxWidth: 400, margin: '0 auto' }}>
                    <div style={{ paddingTop: 12, display: 'flex', justifyContent: 'flex-end', alignItems: 'center', width: '100%' }}>
                      <ButtonBase
                        style={{ paddingTop: 12, paddingBottom: 12, paddingLeft: 24, paddingRight: 24, background: palette.primary['500'], borderRadius: 12, width: '100%' }}
                        onClick={back}
                      >
                        <Typography style={{ ...typography.body.medium, color: palette.grayscale['100'] }}>
                          {t('back-to-order')}
                        </Typography>
                      </ButtonBase>
                    </div>
                  </div>
                )}
              </div>
            </BottomPanel>
          )}
        </div>
        <CartPaymentStatusModal
          titleI18n="your-order"
          open={isPaymentInProgress || isCompleted}
          values={order}
        />
        <Notification />
      </main>
    </div>
  );
};

export default withTranslation('common')(Checkout);
