import React, { useEffect, useState } from 'react';
import Container from '@material-ui/core/Container';
import { useDispatch, useSelector } from 'react-redux';
import { useRouter } from 'next/router';
import BottomPanel from '../../components/BottomPanel';
import Notification from '../../components/Notification';
import AppBar from '../../components/AppBar';
import Menu from '../../components/Menu';
import { menusSelectors, orderSelectors, restaurantsSelectors } from '../../../redux/selectors';
import {orderActions, restaurantsActions} from '../../../redux/actions';
import CurrentOrderAction from '../../components/CurrentOrderAction';
import { withTranslation } from '../../../i18n';
import isEmpty from '../../utilities/isEmpty';
import {createOrderCart, getCurrentCustomer, getCurrentOrderCart} from '../../../redux/api';
import Loader from '../../components/Loader';
import useStyles from './styles';
import {NextSeo} from "next-seo";
import typography from "../../../styles/typography";
import Typography from "@material-ui/core/Typography";
import ButtonBase from "@material-ui/core/ButtonBase";
import RestaurantInfoModal from "../../components/_popup/RestaurantInfoModal";
import Skeleton from "@material-ui/lab/Skeleton";
import CustomerRegistrationModal from "../../components/_popup/CustomerRegistrationModal";
import OrderCartModal from "../../components/_popup/OrderCartModal";
import removeUndefined from "../../utilities/removeUndefined";
import {noop} from "../../components/Menu/utils";

const views = {
  ORDER: 'ORDER',
  CONFIRM: 'CONFIRM',
  SUMMARY: 'SUMMARY'
};

const Express = ({ t, session }) => {
  const router = useRouter();
  const classes = useStyles();
  const dispatch = useDispatch();
  
  const { restaurant = {} } = useSelector(restaurantsSelectors.getRestaurant);
  const { id: restaurantId, address = {} } = restaurant;
  const { street, number, zipCode, city } = address;
  const resolvedAddress = `${street} ${number}, ${zipCode} ${city}`;
  const { quantity, total } = useSelector(orderSelectors.getUnconfirmed);
  const { order = {} } = useSelector(orderSelectors.getOrder);
  const { menus } = useSelector(menusSelectors.getMenus);
  
  const { slug, highlight, tableId } = router.query;
  const [isCustomerRegistrationDrawerOpen, setCustomerRegistrationDrawerOpen] = useState(false);
  const showCustomerRegistration = () => setCustomerRegistrationDrawerOpen(true);
  const hideCustomerRegistration = () => setCustomerRegistrationDrawerOpen(false);
  
  useEffect(() => {
    dispatch(restaurantsActions.getRestaurantBySlugAndOrderType(slug, 'EXPRESS'))
  }, [])
  
  const onCustomerRegistration = (customer) => {
    let table = null;
    if (tableId) {
      table = { id: tableId }
    }
    if (restaurantId) {
      createOrderCart(restaurantId, customer, table).then(() => {
        dispatch(orderActions.pollOrderCart());
        hideCustomerRegistration();
      }).catch(() => {
        dispatch(orderActions.stopPollOrderCart());
      })
    }
  }
  
  const navigateToCheckout = () => {
    router.push(
      {
        pathname: `${router.pathname}/checkout`,
        query: removeUndefined({
          ...router.query
        })
      }, undefined, { shallow: true }
    ).then(noop).catch(noop)
  };
  
  useEffect(() => {
    if (restaurantId) {
      // if no customer, show registration
      getCurrentCustomer().then(({ data: customerData }) => {
        if (isEmpty(customerData) || !customerData.email) {
          // if no email, create same as customer registration flow
          showCustomerRegistration();
        } else {
          // if customer, check cart
          getCurrentOrderCart(restaurantId).then(({ data: cartData = {} }) => {
            // if cart, check status
            if (!isEmpty(cartData)) {
              const { status, table = {} } = cartData;
              const { id: currentTableId } = table || {}
              // if completed create new
              if (status === "COMPLETED") {
                onCustomerRegistration(customerData);
                return;
              }
              // if other table id than existing, create new cart
              else if (tableId !== currentTableId) {
                onCustomerRegistration(customerData);
                return;
              }
              // if payment requested, send to checkout
              else if (status === 'PAYMENT_REQUESTED') {
                navigateToCheckout();
              }
            }
            dispatch(orderActions.pollOrderCart());
          }).catch(() => {
            // if no cart, create same as customer registration flow
            onCustomerRegistration(customerData);
          })
        }
      }).catch(() => {
        showCustomerRegistration();
      });
      return () => {
        dispatch(orderActions.stopPollOrderCart());
      };
    }
  }, [restaurantId])
  
  const addHighlight = (highlightVal) => {
    router.push(
      {
        pathname: router.pathname,
        query: removeUndefined({
          ...router.query,
          highlight: highlightVal
        })
      }, undefined, { shallow: true }
    ).then(noop).catch(noop)
  }
  
  const removeHighlight = () => {
    delete router.query['highlight'];
    router.replace(
      {
        pathname: router.pathname,
        query: removeUndefined({
          ...router.query
        })
      }, undefined, { shallow: true }
    ).then(noop).catch(noop)
  }

  const [isRestaurantDetailsDrawerOpen, setRestaurantDetailsDrawerOpen] = useState(false);
  const [view, setView] = useState(views.ORDER);
  const [isCurrentOrderDrawerOpen, setCurrentOrderDrawerOpen] = useState(false);
  const [completingOrder, setCompletingOrder] = useState(false);
  const [redirectingToStripeCheckout, setRedirectingToStripeCheckout] = useState(false);
  const [isMenuItemDetailsOpen, setMenuItemDetailsOpen] = useState(false);
  
  const showRestaurantDetails = () => addHighlight('details');
  const hideRestaurantDetails = () => removeHighlight();
  
  const showMenuItemDetails = () => addHighlight('item');
  const hideMenuItemDetails = () => removeHighlight();
  
  const showCurrentOrderCartDetails = () => addHighlight('basket');
  const hideCurrentOrderCartDetails = () => removeHighlight();
  
  useEffect(() => {
    if (highlight === 'details') {
      setRestaurantDetailsDrawerOpen(true)
    } else {
      setRestaurantDetailsDrawerOpen(false)
    }

    if (highlight === 'basket') {
      setCurrentOrderDrawerOpen(true)
    } else {
      setCurrentOrderDrawerOpen(false)
    }

    if (highlight === 'item') {
      setMenuItemDetailsOpen(true)
    } else {
      setMenuItemDetailsOpen(false)
    }
  }, [highlight])
  
  const onAddOrderItem = (data) => {
    dispatch(orderActions.addOrderCartItem(data));
  };
  
  const onRemoveOrderItem = (id) => {
    dispatch(orderActions.removeOrderCartItem(id));
  };
  
  if (isEmpty(restaurant) || isEmpty(menus)) {
    let msg = '';
    if (completingOrder && session) {
      msg = t('common-loader-processing-payment-message');
    }
    if (completingOrder && redirectingToStripeCheckout) {
      msg = t('common-loader-redirecting-to-payment-message');
    }
    return (
      <Loader message={msg} />
    );
  }
  
  const isLoading = isEmpty(restaurant);
  
  return (
    <div>
      <NextSeo
        title={restaurant.name}
        nofollow
        noindex
        canonical={"https://eat.allo.restaurant"}
      />
      <main style={{ minHeight: "100vh" }}>
        <AppBar />
        <div style={{ paddingTop: 8 }}>
          <Container>
            <div className={classes.wrapper}>
              <div className={classes.titleWrapper}>
                {isLoading ? (
                  <Skeleton width="250px">
                    <Typography component="h1" style={{...typography.large.semiBold}}>.</Typography>
                  </Skeleton>
                ) : (
                  <Typography component="h1" style={{...typography.large.semiBold}}>
                    {restaurant.name}
                  </Typography>
                )}
              </div>
              <div className={classes.detailsButton}>
                {isLoading ? (
                  <Skeleton width="100px">
                    <Typography component="h1" style={{ ...typography.large.semiBold }}>.</Typography>
                  </Skeleton>
                ) : (
                  <div style={{ display: "flex", alignItems: "center" }}>
                    <Typography style={{ ...typography.body.regular }}>{resolvedAddress}</Typography>
                    <span style={{ marginLeft: 4, marginRight: 4, ...typography.extraSmall.regular }}>{` • `}</span>
                    <ButtonBase style={{ padding: 0 }} disableRipple disableTouchRipple onClick={showRestaurantDetails}>
                      <Typography style={{ ...typography.body.medium }}>
                        {t('more-info')}
                      </Typography>
                    </ButtonBase>
                  </div>
                )}
              </div>
            </div>
          </Container>
        </div>
        <div className={classes.menu}>
          <Menu
            openDetails={showMenuItemDetails} closeDetails={hideMenuItemDetails}
            detailsOpen={isMenuItemDetailsOpen}
            menus={menus}
            readOnly={isEmpty(order)}
            addOrderItem={onAddOrderItem}
            removeOrderItem={onRemoveOrderItem}
            noAddButton
          />
        </div>
        {!!quantity && !(isRestaurantDetailsDrawerOpen || isCurrentOrderDrawerOpen || isMenuItemDetailsOpen) && (
          <BottomPanel zIndex={1702} transparent wrapperStyle={{ position: "sticky" }}>
            <CurrentOrderAction
              amount={total}
              count={quantity}
              onClick={showCurrentOrderCartDetails}
              orderType={'EXPRESS'}
            />
          </BottomPanel>
        )}
        <RestaurantInfoModal
          titleI18n={'activity-title-restaurant-details'}
          open={isRestaurantDetailsDrawerOpen}
          onClose={hideRestaurantDetails}
        />
        <CustomerRegistrationModal
          titleI18n={'activity-title-welcome'}
          open={isCustomerRegistrationDrawerOpen}
          onSubmit={onCustomerRegistration}
        />
        {isCurrentOrderDrawerOpen && (
          <OrderCartModal
            open={isCurrentOrderDrawerOpen}
            onClose={hideCurrentOrderCartDetails}
            onRemove={onRemoveOrderItem}
            onCheckout={navigateToCheckout}
          />
        )}
        <Notification />
      </main>
    </div>
  );
};

export default withTranslation('common')(React.memo(Express));
