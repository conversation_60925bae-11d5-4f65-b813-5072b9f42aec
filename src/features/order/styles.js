import { makeStyles } from '@material-ui/core/styles';
import { drawerModalStyle } from '../../../styles/theme';
import palette from "../../../styles/palette";
import shadows from "../../../styles/shadows";

const useStyles = makeStyles((theme) => ({
  wrapper: {
    background: palette.grayscale["100"],
    ...shadows.base,
    padding: 12,
    borderRadius: 12
  },
  titleWrapper: {
    // marginTop: 16
  },
  actions: {
    marginTop: 8,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between'
  },
  detailsButton: {
    marginTop: 2,
    
    // fix issue with visible single pixel between menu and header on scroll stick
    paddingBottom: 1
  },
  menu: {
    marginTop: 12
  },
  drawerModal: {
    background: theme.palette.common.white,
    position: 'absolute',
    top: 200,
    width: '100%',
    maxWidth: '700px',
    borderTopLeftRadius: 8,
    borderTopRightRadius: 8,
    minHeight: '100%',
    bottom: 0
  },
  drawerContainer: {
    '&&': {
      ...drawerModalStyle
    },
  },
  modalWrapper: {
    marginTop: theme.spacing(4),
    background: theme.palette.common.white
  },
  listItemWrapper: {
    '& > div:not(:first-child)': {
      marginTop: 8
    }
  }
}));

export default useStyles;
