import React, { useEffect } from 'react';
import Container from '@material-ui/core/Container';
import { useDispatch, useSelector } from 'react-redux';
import { ButtonContainer, TitleContainer } from '../../../components/Containers';
import { Paragraph, Title } from '../../../components/Text';
import { withTranslation } from '../../../../i18n';
import Img from '../../../components/Img';
import isEmpty from '../../../utilities/isEmpty';
import List from '../../../components/List';
import { restaurantsSelectors } from '../../../../redux/selectors';
import LinkButton from '../../../components/Buttons/LinkButton';
import { ArrowForwardIconWhite } from '../../../components/Icons';
import { restaurantsActions } from '../../../../redux/actions';
import useStyles from './styles';
import animationData from "../../../animations/salad.json";
import {NextSeo} from "next-seo";
// import Lottie from "react-lottie";
import {Typography} from "@material-ui/core";
import typography from "../../../../styles/typography";

const Success = ({ t, restaurant = {}, customer = {} }) => {
  const classes = useStyles();
  const dispatch = useDispatch();

  useEffect(() => {
    window.scrollTo(0, 0);
    dispatch(restaurantsActions.getRestaurants());
  }, []);

  const { name } = restaurant;
  const { firstName, lastName } = customer;
  const { restaurants = [] } = useSelector(restaurantsSelectors.getRestaurants);
  
  const defaultOptions = {
    loop: true,
    autoplay: true,
    animationData: animationData,
    rendererSettings: {
      preserveAspectRatio: "xMidYMid slice"
    }
  };

  return (
    <div>
      <NextSeo
        title={restaurant.name}
        nofollow
        noindex
        canonical={"https://eat.allo.restaurant"}
      />
      <main>
        <div className={classes.wrapper}>
          <div className={classes.top}>
            <Container>
              {/*<div className={classes.img}>*/}
              {/*  <Lottie*/}
              {/*    options={defaultOptions}*/}
              {/*    height={200}*/}
              {/*    width={180}*/}
              {/*  />*/}
              {/*</div>*/}
              <TitleContainer withText>
                {!isEmpty(customer) && (
                  <Typography style={{ ...typography.medium.medium }}>
                    {t('order-success-title', { name: `${firstName} ${lastName || ''}`, restaurant: name })}
                  </Typography>
                )}
                <div className={classes.description}>
                  <Typography style={{ ...typography.body.regular }}>
                    {t('order-success-description')}
                  </Typography>
                </div>
              </TitleContainer>
            </Container>
            <div className={classes.action}>
              <ButtonContainer>
                <LinkButton
                  href="/"
                  shallow={false}
                  endIcon={<ArrowForwardIconWhite />}
                  className={classes.actionBtn}
                >
                  {t('order-success-return-to-landing-btn-label')}
                </LinkButton>
              </ButtonContainer>
            </div>
          </div>
          {/*{!isEmpty(restaurants) && (*/}
          {/*  <List restaurants={restaurants} />*/}
          {/*)}*/}
        </div>
      </main>
    </div>
  );
};

export default withTranslation('common')(Success);
