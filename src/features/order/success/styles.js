import { makeStyles } from '@material-ui/core/styles';
import { fade } from '@material-ui/core';
import { colors } from '../../../../styles/theme';
import palette from "../../../../styles/palette";

const useStyles = makeStyles((theme) => ({
  wrapper: {
    // space below needed to overcome the floating action panel through minHeight and marginBottom
    minHeight: '100vh',
    background: palette.grayscale["200"],
    maxWidth: 500,
    margin: "0 auto",
  },
  top: {
    paddingTop: 56,
    paddingBottom: 16,
    color: palette.grayscale["800"],
    // background: colors.leviee.main.green
  },
  img: {
    marginBottom: 32
  },
  title: {
    marginTop: 12
  },
  description: {
    marginTop: 12
  },
  summary: {
    paddingTop: 16
  },
  totalRow: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between'
  },
  vatDisclaimer: {
    color: fade(colors.leviee.main.dark, 0.48)
  },
  actionBtn: {
    width: 'auto',
    color: theme.palette.common.white,
    background: theme.palette.primary.main,
    height: 48
  }
}));

export default useStyles;
