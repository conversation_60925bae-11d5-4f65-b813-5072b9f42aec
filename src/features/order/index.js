import React, { useEffect, useState } from 'react';
import Container from '@material-ui/core/Container';
import { useDispatch, useSelector } from 'react-redux';
import BottomPanel from '../../components/BottomPanel';
import Notification from '../../components/Notification';
import AppBar from '../../components/AppBar';
import Menu from '../../components/Menu';
import TableStatus from '../../components/TableStatus';
import { orderSelectors, restaurantsSelectors } from '../../../redux/selectors';
import { appActions, orderActions } from '../../../redux/actions';
import CurrentOrderAction from '../../components/CurrentOrderAction';
import GetVerifiedAction from '../../components/GetVerifiedAction';
// eslint-disable-next-line import/named
import { withTranslation } from '../../../i18n';
import Success from './success';
import useStyles from './styles';
import Typography from "@material-ui/core/Typography";
import typography from "../../../styles/typography";
import ButtonBase from "@material-ui/core/ButtonBase";
import RestaurantInfoModal from "../../components/_popup/RestaurantInfoModal";
import {NextSeo} from "next-seo";
import isEmpty from "../../utilities/isEmpty";
import Skeleton from "@material-ui/lab/Skeleton";
import TableManagementModal from "../../components/_popup/TableManagementModal";
import OrderItemDetailsModal from "../../components/_popup/OrderItemDetailsModal";
import UserVerificationModal from "../../components/_popup/UserVerificationModal";
import OrderBasketModal from "../../components/_popup/OrderBasketModal";
import ReceiptModal from "../../components/_popup/ReceiptModal";
import LanguageSelector from "../../components/LanguageSelector";
import OrderingBlockedModal from "../../components/_popup/OrderingBlockedModal";
import {getRestaurantConfiguration} from "../../../redux/api";

const Order = ({ t, id }) => {
  const classes = useStyles();
  const dispatch = useDispatch();

  const [isRestaurantDetailsDrawerOpen, setRestaurantDetailsDrawerOpen] = useState(false);
  const [isVerificationDrawerOpen, setVerificationDrawerOpen] = useState(false);
  const [isTableManagementDrawerOpen, setTableManagementDrawerOpen] = useState(false);
  const [isCurrentOrderDrawerOpen, setCurrentOrderDrawerOpen] = useState(false);
  const [isPaymentDrawerOpen, setPaymentDrawerOpen] = useState(false);
  const [isMenuItemDetailsOpen, setMenuItemDetailsOpen] = useState(false);

  const showRestaurantDetails = () => setRestaurantDetailsDrawerOpen(true);
  const hideRestaurantDetails = () => setRestaurantDetailsDrawerOpen(false);

  const showTableManagementDrawer = () => setTableManagementDrawerOpen(true);
  const hideTableManagementDrawer = () => setTableManagementDrawerOpen(false);

  const showVerificationDrawer = () => setVerificationDrawerOpen(true);
  const hideVerificationDrawer = () => setVerificationDrawerOpen(false);

  const showCurrentOrderDrawer = () => setCurrentOrderDrawerOpen(true);
  const hideCurrentOrderDrawer = () => setCurrentOrderDrawerOpen(false);

  const showPaymentDrawer = () => setPaymentDrawerOpen(true);
  const hidePaymentDrawer = () => setPaymentDrawerOpen(false);
  
  const showMenuItemDetails = () => setMenuItemDetailsOpen(true);
  const hideMenuItemDetails = () => setMenuItemDetailsOpen(false);

  const { restaurant = {} } = useSelector(restaurantsSelectors.getRestaurant);
  const { id: restaurantId, address = {} } = restaurant;
  const { street, number, zipCode, city } = address;
  const resolvedAddress = `${street} ${number}, ${zipCode} ${city}`;
  const { order = {} } = useSelector(orderSelectors.getOrder);
  const { status, totalDue, mergedInto } = order;
  const showOrderingBlocker = useSelector(orderSelectors.getShowOrderingBlocker)

  const { quantity: unconfirmedQtd, total: unconfirmedTotal } = useSelector(orderSelectors.getUnconfirmed);
  const { quantity: confirmedQtd } = useSelector(orderSelectors.getConfirmed);

  const { participant = {} } = useSelector(orderSelectors.getParticipant);
  const { approvalId, customer = {} } = participant;
  const participantIsApproved = !!approvalId;
  
  const { item: orderItemDetails, menuItem: menuItemDetails } = useSelector(orderSelectors.getItemDetails);
  const isOrderItemDetailsOpen = !isEmpty(orderItemDetails) && !isEmpty(menuItemDetails)

  useEffect(() => {
    if (isVerificationDrawerOpen && participantIsApproved) {
      hideVerificationDrawer();
      dispatch(appActions.showNotification('notification-participant-approved-confirmation'));
    }
  }, [participantIsApproved]);
  
  const [configuration, setConfiguration] = useState(null)
  
  useEffect(() => {
    if (restaurantId) {
      getRestaurantConfiguration(restaurantId).then(({ data }) => setConfiguration(data)).catch(() => {})
    }
  }, [restaurantId])

  const hasUnconfirmedItems = unconfirmedQtd > 0;
  const { items = [] } = order;
  const hasItems = items.length > 0;

  useEffect(() => {
    dispatch(orderActions.pollOrder(id));
    return () => {
      dispatch(orderActions.stopPollOrder());
    };
  }, []);

  const onAddOrderItem = (data) => {
    dispatch(orderActions.addItem(order.id, data));
  };

  const onAddNestedOrderItem = (orderItemId, data) => {
    setCurrentOrderDrawerOpen(true)
    dispatch(orderActions.addNestedItem(order.id, orderItemId, data));
  };

  const onRemoveOrderItem = (itemId) => {
    dispatch(orderActions.removeItem(order.id, itemId));
  };

  const confirmOrderItems = () => {
    dispatch(orderActions.confirmItems(order.id));
    hideCurrentOrderDrawer();
  };

  const payOrder = () => {
    hideCurrentOrderDrawer();
    showPaymentDrawer();
  };

  const isClosed = status === 'CLOSED';
  if (isClosed && !mergedInto) {
    return <Success customer={customer} restaurant={restaurant} />;
  }
  
  const isLoading = isEmpty(restaurant);
  
  return (
    <div>
      <NextSeo
        title={restaurant.name}
        nofollow
        noindex
        canonical={"https://eat.allo.restaurant"}
      />
      <main style={{ minHeight: "100vh" }}>
        <AppBar
          action={(
            <div style={{ width: "100%", display: "flex", alignItems: "center", justifyContent: "space-between" }}>
              <TableStatus
                verify={showVerificationDrawer}
                manage={showTableManagementDrawer}
              />
              <div style={{ flex: 1 }}/>
              <LanguageSelector />
            </div>
          )}
        />
        <div style={{ paddingTop: 8 }}>
          <Container>
            <div className={classes.wrapper}>
              <div className={classes.titleWrapper}>
                {isLoading ? (
                  <Skeleton width="250px">
                    <Typography component="h1" style={{...typography.large.semiBold}}>.</Typography>
                  </Skeleton>
                  ) : (
                  <Typography component="h1" style={{...typography.large.semiBold}}>
                    {restaurant.name}
                  </Typography>
                )}
              </div>
              <div className={classes.detailsButton}>
                {isLoading ? (
                  <Skeleton width="100px">
                    <Typography component="h1" style={{ ...typography.large.semiBold }}>.</Typography>
                  </Skeleton>
                ) : (
                  <div style={{ display: "flex", alignItems: "center" }}>
                    <Typography style={{ ...typography.body.regular }}>{resolvedAddress}</Typography>
                    <span style={{ marginLeft: 4, marginRight: 4, ...typography.extraSmall.regular }}>{` • `}</span>
                    <ButtonBase style={{ padding: 0 }} disableRipple disableTouchRipple onClick={showRestaurantDetails}>
                      <Typography style={{ ...typography.body.medium }}>
                        {t('more-info')}
                      </Typography>
                    </ButtonBase>
                  </div>
                )}
              </div>
            </div>
          </Container>
        </div>
        <div className={classes.menu}>
          <Menu
            addOrderItem={onAddOrderItem}
            removeOrderItem={onRemoveOrderItem}
            openDetails={showMenuItemDetails}
            closeDetails={hideMenuItemDetails}
            readOnly={!participantIsApproved}
            configuration={configuration}
          />
        </div>
        {!participantIsApproved && !isVerificationDrawerOpen && <GetVerifiedAction onClick={showVerificationDrawer} />}
        {participantIsApproved && hasItems && !(isRestaurantDetailsDrawerOpen || isVerificationDrawerOpen || isCurrentOrderDrawerOpen || isPaymentDrawerOpen || isOrderItemDetailsOpen || isMenuItemDetailsOpen) && (
          <BottomPanel zIndex={1702} transparent wrapperStyle={{ position: "sticky" }}>
            <CurrentOrderAction
              amount={unconfirmedTotal}
              count={unconfirmedQtd}
              onClick={showCurrentOrderDrawer}
              orderType={order.type}
            />
          </BottomPanel>
        )}
        {isRestaurantDetailsDrawerOpen && (
          <RestaurantInfoModal
            titleI18n={'activity-title-restaurant-details'}
            open={isRestaurantDetailsDrawerOpen}
            onClose={hideRestaurantDetails}
          />
        )}
        {isVerificationDrawerOpen && (
          <UserVerificationModal
            open={isVerificationDrawerOpen}
            onClose={hideVerificationDrawer}
          />
        )}
        {isTableManagementDrawerOpen && (
          <TableManagementModal
            open={isTableManagementDrawerOpen}
            onClose={hideTableManagementDrawer}
          />
        )}
        {isCurrentOrderDrawerOpen && (
          <OrderBasketModal
            open={isCurrentOrderDrawerOpen}
            onClose={hideCurrentOrderDrawer}
            onRemove={onRemoveOrderItem}
            onOpenReceipt={payOrder}
            onConfirmOrderItems={confirmOrderItems}
          />
        )}
        {isPaymentDrawerOpen && (
          <ReceiptModal
            open={isPaymentDrawerOpen}
            onClose={hidePaymentDrawer}
          />
        )}
        <Notification />
        {isOrderItemDetailsOpen && (
          <OrderItemDetailsModal addItem={onAddOrderItem} addNestedItem={onAddNestedOrderItem} />
        )}
        {showOrderingBlocker && (
          <OrderingBlockedModal open={showOrderingBlocker} onClose={() => dispatch(orderActions.setShowOrderingBlocker(false))} />
        )}
      </main>
    </div>
  );
};

export default withTranslation('common')(React.memo(Order));
