import React, {Fragment, useEffect, useState} from 'react';
import useStyles from './styles';
import {useRouter} from "next/router";
import isEmpty from "../../utilities/isEmpty";
import {getGuestMonitorData} from "../../../redux/api";
import {withTranslation} from "../../../i18n";
import {Typography, useMediaQuery} from "@material-ui/core";
import typography from "../../../styles/typography";
import palette from "../../../styles/palette";
import {PaymentIllustration160, ReceiptDarkIcon20} from "../../utilities/icons";
import FlexDiv from "../../components/_div/FlexDiv";
import Notification from "../../components/Notification";
import shadows from "../../../styles/shadows";
import formatNumber from "../../utilities/formatNumber";
import {SplashScreen} from "../splash";
import QRCode from "qrcode.react";
import Head from 'next/head';

let timer;

const SideScreen = ({ t }) => {
  const classes = useStyles();
  const isMobile = useMediaQuery('(max-width:600px)');
  
  const router = useRouter();
  const { query = {} } = router;
  
  const [sideScreenData, setSideScreenData] = useState(null);
  const { checkoutRequest = {}, order = {}, orderId } = (sideScreenData || {});
  const { paymentChannel } = (checkoutRequest || {})
  const { items: orderItems = [], total: orderTotal, paymentStatus } = (order || {})
  
  const fetchGuestMonitorData = () => {
    getGuestMonitorData(query.code).then(({ data }) => setSideScreenData(data)).catch(() => {
      if (timer) {
        clearInterval(timer);
      }
    });
  }
  
  useEffect(() => {
    if (isEmpty(query) || !query.code) {
      router.replace('/').then(() => {}).catch(() => {});
    } else {
      fetchGuestMonitorData();
      timer = setInterval(fetchGuestMonitorData, orderId ? 2000 : 3000)
    }
    
    return () => {
      clearInterval(timer)
    }
  }, [JSON.stringify(query), orderId])
  
  const isOrdering = !isEmpty(order);
  const isPaying = !isEmpty(checkoutRequest)
  const isIdle = isEmpty(sideScreenData) || (!isPaying && !isOrdering)
  
  if (isIdle) {
    return (
      <Fragment>
        <Head>
          <link rel="manifest" href={`/manifest/${query.code}`} />
        </Head>
        <SplashScreen />
      </Fragment>
      
    );
  }
  
  if (!isEmpty(order) && paymentStatus === "NOT_PAID") {
    return (
      <div style={{ backgroundColor: "#f0edea" }}>
        <main className={classes.main}>
          <div style={{ maxWidth: "calc(100% - 24px)", margin: "48px auto", width: 600, background: palette.grayscale["100"], padding: isMobile ? 16 : 32, borderRadius: isMobile ? 12 : 16 }}>
            <div style={{ margin: "0 auto 32px" }}>
              {/*<AllOProviderLogo20 />*/}
              <Typography style={{ ...typography.email.large, marginBottom: 16 }}>{t("your-order")}</Typography>
            </div>
            <div>
              <div style={{ borderRadius: 12, padding: '16px 12px', background: palette.grayscale["250"] }}>
                <div style={{ display: "flex", alignItems: "center", justifyContent: "space-between", marginBottom: 14, paddingRight: 4 }}>
                  <div style={{ display: "flex", alignItems: "center" }}>
                    <ReceiptDarkIcon20 />
                    <Typography style={{ ...typography.email.medium, marginLeft: 4 }}>{t('order-summary')}</Typography>
                  </div>
                  <div>
                    <Typography style={{ ...typography.email.regular }}>{formatNumber(orderTotal)}€</Typography>
                  </div>
                </div>
                <div style={{ paddingLeft: 4, paddingRight: 4 }}>
                  <div style={{ borderBottom: `1px dashed ${palette.grayscale["400"]}`, marginBottom: 12, opacity: 0.9 }} />
                </div>
                {orderItems.map((orderItem = {}, index) => {
                  const { id, qtd, name, total } = orderItem;
                  const first = index === 0;
                  return (
                    <div key={id} style={{ borderRadius: 12, padding: 12, background: palette.grayscale["100"], display: "flex", alignItems: "center", justifyContent: "space-between", width: "100%", ...shadows.email_base, marginTop: first ? 0 : 8 }}>
                      <div style={{ display: "flex", alignItems: "center" }}>
                        <Typography style={{ ...typography.email.extraSmall.medium, minWidth: 20, display: "flex", justifyContent: "center", borderRadius: "100%", marginRight: 8, background: palette.grayscale["300"] }}>{qtd}</Typography>
                        <Typography style={{ ...typography.email.medium }}>{name}</Typography>
                      </div>
                      <Typography style={{ ...typography.email.regular }}>{formatNumber(total)}€</Typography>
                    </div>
                  )
                })}
                <div style={{ paddingLeft: 4, paddingRight: 4 }}>
                  <div style={{ paddingTop: 16, display: "flex", alignItems: "center", justifyContent: "space-between" }}>
                    <Typography style={{ ...typography.email.regular }}>{t('subtotal')}</Typography>
                    <Typography style={{ ...typography.email.regular }}>{formatNumber(orderTotal)}€</Typography>
                  </div>
                  <div style={{ marginTop: 8, display: "flex", alignItems: "center", justifyContent: "space-between" }}>
                    <Typography style={{ ...typography.email.regular }}>{t('discounts')}</Typography>
                    <Typography style={{ ...typography.email.regular }}>{formatNumber(0)}€</Typography>
                  </div>
                  <div style={{ marginTop: 8, display: "flex", alignItems: "center", justifyContent: "space-between" }}>
                    <Typography style={{ ...typography.email.medium }}>{t('total')}</Typography>
                    <Typography style={{ ...typography.email.medium }}>{formatNumber(orderTotal)}€</Typography>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </main>
        <Notification />
      </div>
    );
  }
  
  if (!isEmpty(checkoutRequest) && (isEmpty(order) || paymentStatus === "PAYING")) {
    if (paymentChannel === "ALLO_PAY") {
      return (
        <div style={{ backgroundColor: "#f0edea", height: "100vh", display: "flex", alignItems: "center", justifyContent: "center" }}>
          <main>
            <div style={{ maxWidth: "calc(100% - 24px)", margin: "48px auto" }}>
              <FlexDiv style={{ flexDirection: "column", justifyContent: "center" }}>
                <FlexDiv style={{ marginBottom: 48, justifyContent: "center" }}>
                  <PaymentIllustration160 />
                </FlexDiv>
                <Typography style={{ ...typography.email.large, marginBottom: 10 }}>
                  {t('waiting-for-payment')}
                </Typography>
                <Typography style={{ ...typography.email.regular, marginBottom: 32, textAlign: "center" }}>
                  {t('please-follow-the-instructions-on-the-terminal')}
                </Typography>
              </FlexDiv>
            </div>
          </main>
        </div>
      )
    }
    
    if (paymentChannel === "ALLO_PAY_LINK") {
      // const isDevelopment = process.env.NEXT_PUBLIC_LEVIEE_ENV !== 'production';
      const qrUrl = `https://eat.allo.restaurant/pay/${orderId}`;
      return (
        <div style={{ backgroundColor: "#f0edea", height: "100vh", display: "flex", alignItems: "center", justifyContent: "center" }}>
          <main>
            <div style={{ maxWidth: "calc(100% - 24px)", margin: "48px auto" }}>
              <FlexDiv style={{ flexDirection: "column", justifyContent: "center", }}>
                <FlexDiv style={{ marginBottom: 48, justifyContent: "center" }}>
                  <div style={{ display: "flex", alignItem: "center", justifyContent: "center", padding: 20 }}>
                    <QRCode
                      id={orderId}
                      value={qrUrl}
                      size={200}
                      level={"H"}
                      includeMargin={true}
                      bgColor={palette.grayscale["100"]}
                      style={{
                        backgroundColor: 'transparent',
                        borderRadius: 20
                      }}
                    />
                  </div>
                </FlexDiv>
                <Typography style={{ ...typography.email.large, marginBottom: 10 }}>
                  {t('scan-qr-code-to-pay')}
                </Typography>
                <Typography style={{ ...typography.email.regular, marginBottom: 32, textAlign: "center" }}>
                  {t('scan-the-qr-code-above-to-pay-for-your-order')}
                </Typography>
              </FlexDiv>
            </div>
          </main>
        </div>
      )
    }
  
    return (
      <div style={{ backgroundColor: "#f0edea", height: "100vh", display: "flex", alignItems: "center", justifyContent: "center" }}>
        <main>
          <div style={{ maxWidth: "calc(100% - 24px)", margin: "48px auto" }}>
            <FlexDiv style={{ flexDirection: "column", justifyContent: "center", width: 320, maxWidth: "calc(100% - 24px)", }}>
              <FlexDiv style={{ marginBottom: 48, justifyContent: "center" }}>
                <PaymentIllustration160 />
              </FlexDiv>
              <Typography style={{ ...typography.email.large, marginBottom: 10 }}>
                {t('processing-payment')}...
              </Typography>
              <Typography style={{ ...typography.email.regular, marginBottom: 32, textAlign: "center" }}>
                {t('we-are-processing-your-payment')} {t('thank-you-for-your-order')}
              </Typography>
            </FlexDiv>
          </div>
        </main>
      </div>
    )
  }
  
  return null;
  
};

export default withTranslation('common')(SideScreen);
