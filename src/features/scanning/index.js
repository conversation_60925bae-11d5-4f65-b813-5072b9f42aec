import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import Container from '@material-ui/core/Container';
import Scanner from '../../components/Scanner';
import InputCodeAction from '../../components/InputCodeAction';
import { MainButton } from '../../components/Buttons';
import { appActions } from '../../../redux/actions';
// eslint-disable-next-line import/named
import { withTranslation } from '../../../i18n';
import { uiSelectors } from '../../../redux/selectors';
import WeChatMask from '../../components/WeChatMask';
import useStyles from './styles';
import Notification from '../../components/Notification';
import isEmpty from "../../utilities/isEmpty";
import Field from "../../components/_input/Field";
import {Typography} from "@material-ui/core";
import typography from "../../../styles/typography";
import AppBar from "../../components/AppBar";
import palette from "../../../styles/palette";
import {noop} from "../../components/Menu/utils";

const inputMode = {
  CAMERA: 'camera',
  MANUAL: 'manual'
};

const Scanning = ({ t, externalCode = '' }) => {
  const classes = useStyles();
  const dispatch = useDispatch();

  // eslint-disable-next-line no-undef
  const ua = navigator.userAgent.toLowerCase();
  const isWeChat = (ua.match(/MicroMessenger/i) || []).includes('micromessenger');

  useEffect(() => {
    window.scrollTo(0, 0);
    if (externalCode && !isWeChat) {
      dispatch(appActions.scan(externalCode));
    }
  }, [externalCode]);

  const { loading } = useSelector(uiSelectors.getScan);

  const [mode, setMode] = useState(externalCode ? inputMode.MANUAL : inputMode.CAMERA);
  const [orderCode, setOrderCode] = useState(externalCode);
  // eslint-disable-next-line no-unused-vars
  const [cameraError, setCameraError] = useState(false);

  const onCameraError = () => {
    setMode(inputMode.MANUAL);
    setCameraError(true);
  };

  const onCreateOrder = (code) => {
    dispatch(appActions.scan(code));
  };

  const onSubmit = () => {
    onCreateOrder(orderCode);
  };

  const onChange = (e) => {
    setOrderCode(e.target.value);
  };

  const onScan = (code) => {
    let normalizedCode = code;
    if(!isEmpty(normalizedCode)) {
      if (normalizedCode.indexOf('%') > -1) {
        if ((normalizedCode || '').indexOf('/booking/') > -1) {
          const matchArr = normalizedCode.split('/booking/');
          if (matchArr.length === 2) {
            normalizedCode = decodeURIComponent(matchArr[1]);
          }
        }

        if ((normalizedCode || '').indexOf('/scan/') > -1) {
          const matchArr = normalizedCode.split('/scan/');
          if (matchArr.length === 2) {
            normalizedCode = decodeURIComponent(matchArr[1]);
          }
        }
      }
      setOrderCode(normalizedCode);
      setMode(inputMode.MANUAL);
      onCreateOrder(normalizedCode);
    }
  };

  if (isWeChat) {
    return <WeChatMask />;
  }

  if (mode === inputMode.CAMERA) {
    return (
      <div>
        <main style={{
          background: palette.grayscale["800"],
          minHeight: '100vh',
          display: "flex",
          flexDirection: "column",
          justifyContent: "center"
        }}>
          <Scanner onScan={(data) => data && onScan(data)} onError={onCameraError} />
        </main>
        <div style={{
          position: 'sticky',
          bottom: 0,
          left: 0,
          right: 0,
          display: 'flex',
          alignItems: 'center',
          zIndex: '1700',
          justifyContent: 'center',
          padding: 8,
          paddingBottom: 32,
          background: palette.grayscale["800"]
        }}>
          <InputCodeAction onClick={() => setMode(inputMode.MANUAL)} />
        </div>
      </div>
    );
  }
  
  const canContinue = !isEmpty(orderCode) && !loading && orderCode.indexOf("#") > -1
  return (
    <div>
      <main style={{ minHeight: "100vh" }}>
        <AppBar justifyContent={"center"} />
        <Container>
          <div style={{
            maxWidth: 420,
            marginLeft: "auto",
            marginRight: "auto",
            marginTop: 76
          }}>
            <Typography style={{ ...typography.large.semiBold, marginBottom: 12 }}>{t('enter-your-table-to-order')}</Typography>
            <Typography style={{ ...typography.body.regular, marginBottom: 32 }}>{t('camera-not-found-enter-table-code-manually')}</Typography>
            <div className={classes.tableCodeTextField} style={{ marginBottom: 24 }}>
              <Field
                label={t('scanning-input-qr-code-field-label')}
                placeholder={"DEXXXXX#00"}
                required
                defaultValue=""
                value={orderCode}
                onChange={loading ? noop : onChange}
                variant="filled"
                id="leviee-table-code-input"
                autoComplete="off"
              />
            </div>
            <MainButton onClick={loading ? noop : onSubmit} disabled={!canContinue} loading={loading}>
              {t(loading ? 'scanner-view-processing-code' : 'common-continue')}
            </MainButton>
          </div>
          
        </Container>
      </main>
      <Notification />
    </div>
  );
};

export default withTranslation('common')(Scanning);
