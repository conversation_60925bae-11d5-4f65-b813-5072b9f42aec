import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useRouter } from 'next/router';
import Cookies from 'js-cookie';
import Info from './info';
import Details from './details';
import { restaurantsSelectors } from '../../../redux/selectors';
import { orderActions, restaurantsActions } from '../../../redux/actions';

const customer = {
  firstName: '',
  lastName: '',
  address: {
    street: '',
    number: '',
    zipCode: '',
    additionalInfo: '',
    city: '',
    country: 'DE'
  },
  phone: '',
  email: ''
};

const Welcome = ({ slug }) => {
  const dispatch = useDispatch();
  const router = useRouter();

  const customerId = Cookies.get('lvCustomerId');
  if (customerId) {
    router.push('/', undefined, { shallow: true });
  };

  const { restaurant } = useSelector(restaurantsSelectors.getRestaurant);
  const { code } = router.query;
  
  useEffect(() => {
    if (!code) {
      router.replace('/', undefined, { shallow: true });
    }
  }, [code]);

  useEffect(() => {
    window.scrollTo(0, 0);
    dispatch(restaurantsActions.getRestaurantBySlugAndOrderType(slug, "DINE_IN"))
  }, []);

  /**
   * Set mode to simple to show welcome or advanced to show full requirements
   */
  const [mode, setMode] = useState('simple');

  /**
   * Store the nickname from welcome screen to use in advanced mode
   */
  const [form, setForm] = useState(customer);

  const onUpdate = (e) => {
    setForm({ ...form, [e.target.name]: e.target.value });
  };

  const onUpdateAddress = (e) => {
    const address = { ...form.address, [e.target.name]: e.target.value };
    setForm({ ...form, address });
  };

  const setAdvancedMode = () => setMode('advanced');

  const setSimpleMode = () => setMode('simple');

  const onSubmit = () => {
    dispatch(orderActions.resolveOrder(code, form));
  };

  return (
    <div>
      {mode === 'simple' && <Info data={form} update={onUpdate} restaurant={restaurant} submit={onSubmit} />}
      {mode === 'advanced' && <Details switchMode={setSimpleMode} data={form} update={onUpdate} updateAddress={onUpdateAddress} submit={onSubmit} />}
    </div>
  );
};

export default Welcome;
