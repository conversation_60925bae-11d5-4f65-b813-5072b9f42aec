import React from 'react';
import Container from '@material-ui/core/Container';
import { useSelector } from 'react-redux';
import isEmpty from '../../../utilities/isEmpty';
import Notification from '../../../components/Notification';
import { MainButton } from '../../../components/Buttons';
import {restaurantsSelectors, uiSelectors} from '../../../../redux/selectors';
import { withTranslation } from '../../../../i18n';
import useStyles from './styles';
import AppBar from "../../../components/AppBar";
import {Typography} from "@material-ui/core";
import typography from "../../../../styles/typography";
import Field from "../../../components/_input/Field";
import {noop} from "../../../components/Menu/utils";

const Info = ({ t, restaurant, submit, data, update }) => {
  const classes = useStyles();
  const { loading, error } = useSelector(uiSelectors.getWelcome);
  const { gallery } = useSelector(restaurantsSelectors.getRestaurantGallery);

  const isValid = !!data.firstName;

  return (
    <div>
      <main style={{ minHeight: "100vh" }}>
        <AppBar justifyContent={"center"} />
        {!isEmpty(restaurant) && (
          <Container>
            <div style={{
              maxWidth: 420,
              marginLeft: "auto",
              marginRight: "auto",
              marginTop: 76
            }}>
              <Typography style={{ ...typography.large.semiBold, marginBottom: 12 }}>
                {t('welcome-info-title', { name: restaurant.name })}
              </Typography>
              <Typography style={{ ...typography.body.regular, marginBottom: 32 }}>
                {t('welcome-info-description')}
              </Typography>
              <div style={{ marginBottom: 24 }}>
                <Field
                  label={t('welcome-info-firstName-field-label')}
                  placeholder={t('welcome-info-firstName-field-placeholder')}
                  name="firstName"
                  value={data.firstName || ''}
                  onChange={loading ? noop : update}
                  variant="filled"
                  id="leviee-welcome-name-input"
                  autoComplete="off"
                />
              </div>
              <MainButton onClick={loading ? noop : submit} disabled={!isValid} loading={loading}>
                {t(loading ? 'welcome-view-preparing-order' : 'welcome-details-start-ordering-btn-label')}
              </MainButton>
            </div>
          </Container>
        )}
        <Notification />
      </main>
    </div>
  );
};

export default withTranslation('common')(Info);
