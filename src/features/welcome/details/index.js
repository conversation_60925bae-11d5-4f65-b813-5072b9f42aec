import React from 'react';
import Container from '@material-ui/core/Container';
import Grid from '@material-ui/core/Grid';
import IconButton from '@material-ui/core/IconButton';
import InputAdornment from '@material-ui/core/InputAdornment';
import { useSelector } from 'react-redux';
import ActivityBar from '../../../components/ActivityBar';
import BottomPanel from '../../../components/BottomPanel';
import TextField from '../../../components/TextField';
import Notification from '../../../components/Notification';
import { ButtonContainer, TitleContainer } from '../../../components/Containers';
import { SmallParagraph, Title } from '../../../components/Text';
import { MainButton } from '../../../components/Buttons';
import { ArrowBackIcon } from '../../../components/Icons';
import { withTranslation } from '../../../../i18n';
import { uiSelectors } from '../../../../redux/selectors';
import useStyles from './styles';

const Details = ({ t, data, switchMode, update, updateAddress, submit }) => {
  const classes = useStyles();
  const { loading, error } = useSelector(uiSelectors.getWelcome);

  const isValid = data.firstName && data.lastName && data.phone && data.email;

  return (
    <div>
      <main>
        <ActivityBar
          title={t('activity-title-corona-personal-information')}
          back={(
            <IconButton component="a" edge="start" color="inherit" aria-label="menu" onClick={switchMode}>
              <ArrowBackIcon />
            </IconButton>
        )}
        />
        <Container>
          <div className={classes.wrapper}>
            <TitleContainer>
              <Title>
                {t('welcome-details-title')}
              </Title>
              <div className={classes.formMandatory}>
                <SmallParagraph>
                  {t('welcome-details-form-mandatory')}
                </SmallParagraph>
              </div>
            </TitleContainer>
            <form className={classes.form}>
              <Grid container spacing={3}>
                <Grid item xs={6}>
                  <TextField
                    label={`${t('welcome-details-firstName-field-label')}*`}
                    name="firstName"
                    value={data.firstName}
                    onChange={update}
                    variant="filled"
                    id="leviee-welcome-firstName-input"
                    autoComplete="off"
                  />
                </Grid>
                <Grid item xs={6}>
                  <TextField
                    label={`${t('welcome-details-lastName-field-label')}*`}
                    name="lastName"
                    value={data.lastName}
                    onChange={update}
                    variant="filled"
                    id="leviee-welcome-lastName-input"
                    autoComplete="off"
                  />
                </Grid>
                <Grid item xs={8}>
                  <TextField
                    label={t('welcome-details-street-field-label')}
                    name="street"
                    value={data.address.street}
                    onChange={updateAddress}
                    variant="filled"
                    id="leviee-welcome-street-input"
                    autoComplete="off"
                  />
                </Grid>
                <Grid item xs={4}>
                  <TextField
                    label={t('welcome-details-number-field-label')}
                    variant="filled"
                    name="number"
                    value={data.address.number}
                    onChange={updateAddress}
                    id="leviee-welcome-number-input"
                    autoComplete="off"
                  />
                </Grid>
                <Grid item xs={4}>
                  <TextField
                    label={t('welcome-details-zipCode-field-label')}
                    variant="filled"
                    name="zipCode"
                    value={data.address.zipCode}
                    onChange={updateAddress}
                    id="leviee-welcome-zipCode-input"
                    autoComplete="off"
                  />
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    label={`${t('welcome-details-phone-field-label')}*`}
                    InputProps={{
                      startAdornment: <InputAdornment disableTypography style={{ marginTop: 20 }} position="start">+49</InputAdornment>,
                    }}
                    variant="filled"
                    name="phone"
                    type="number"
                    value={data.phone}
                    onChange={update}
                    id="leviee-welcome-phone-input"
                    autoComplete="off"
                  />
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    label={`${t('welcome-details-email-field-label')}*`}
                    defaultValue=""
                    variant="filled"
                    name="email"
                    value={data.email}
                    onChange={update}
                    id="leviee-welcome-email-input"
                    autoComplete="off"
                  />
                </Grid>
              </Grid>
            </form>

          </div>
        </Container>
        <BottomPanel>
          <ButtonContainer>
            <MainButton onClick={submit} disabled={!isValid || !!loading} loading={loading}>
              {t(loading ? 'welcome-view-preparing-order' : 'welcome-details-start-ordering-btn-label')}
            </MainButton>
          </ButtonContainer>
        </BottomPanel>
        <Notification />
      </main>
    </div>
  );
};

export default withTranslation('common')(Details);
