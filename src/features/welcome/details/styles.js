import { makeStyles } from '@material-ui/core/styles';

const useStyles = makeStyles((theme) => ({
  wrapper: {
    // space below needed to overcome the floating action panel through minHeight and marginBottom
    minHeight: `calc(100vh - ${theme.mixins.toolbar.minHeight}px)`,
    marginBottom: theme.spacing(10)
  },
  formMandatory: {
    marginTop: 16
  },
  form: {
    marginTop: theme.spacing(3) - 2
  },
  row: {
    display: 'flex',
    justifyContent: 'space-between'
  }
}));

export default useStyles;
