module.exports = {
  parser: 'babel-eslint',
  extends: 'airbnb',
  rules: {
    'react/prop-types': 0,
    'react/jsx-props-no-spreading': 0,
    'react/jsx-filename-extension': 0,
    'comma-dangle': 0,
    'jsx-a11y/anchor-is-valid': 0,
    'object-curly-newline': 0,
    'no-param-reassign': 0,
    'no-shadow': 0,
    'max-len': [2, { code: 140, ignoreUrls: true, ignoreStrings: true, ignoreTemplateLiterals: true }]
  },
};
