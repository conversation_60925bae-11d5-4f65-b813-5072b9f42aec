---
.gitlab-ci-cache.yarn:
  - CI_JOB_SKIP_EXIT_CODE=0
  - mkdir -p ${CI_PROJECT_DIR}/.yarn
  - mkdir -p ${CI_PROJECT_DIR}/.yarn/cache
  - mkdir -p ${CI_PROJECT_DIR}/node_modules
  - YARN_GLOBAL_FOLDER=${CI_PROJECT_DIR}/.yarn
  - |
    if grep -q packageManager package.json; then
      echo "packageManager defined in package.json - will run corepack"
      corepack yarn install  
    else
      yarn install
    fi
  - |
    YARN_LOCK_SHA256_HASH=$( sha256sum yarn.lock | awk '{ print $1 }')
    echo "Current sha256 hash is $YARN_LOCK_SHA256_HASH"
    echo "Checking sha256 hash of yarn.lock"
    
    if ! sha256sum -c yarn.lock.sha256sum; then
      echo "yarn.lock checksum does not match cache"
      sha256sum yarn.lock > yarn.lock.sha256sum
      CI_JOB_SKIP_EXIT_CODE=218
    else
      echo "Cache is the same as before, won't update cache"
    fi
  - exit $CI_JOB_SKIP_EXIT_CODE
