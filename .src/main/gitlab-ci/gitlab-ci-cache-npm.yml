---
.gitlab-ci-cache.npm:
  - CI_JOB_SKIP_EXIT_CODE=0
  - |
    PACKAGE_JSON_SHA256_HASH=$( sha256sum package.json | awk '{ print $1 }')
    echo "Current sha256 hash is $PACKAGE_JSON_SHA256_HASH"
    echo "Checking sha256 hash of package.json"

    # install node packages
    npm set progress=false && npm config set depth 0
    npm install --omit=dev

    # copy production node_modules aside
    cp -R node_modules prod_node_modules

    # install ALL node_modules, including 'devDependencies'
    npm install

    ls -lah
    
    if ! sha256sum -c package-lock.json.sha256sum; then
      echo "package-lock.json checksum does not match cache"

      sha256sum package-lock.json > package-lock.json.sha256sum
      CI_JOB_SKIP_EXIT_CODE=218
    else
      echo "Cache is the same as before, won't update cache"
    fi
  - exit $CI_JOB_SKIP_EXIT_CODE
