import React from 'react';
import { useRouter } from 'next/router';
import dynamic from 'next/dynamic';

const Scanning = dynamic(() => import('../../src/features/scanning'), { ssr: false });

const BookingPage = () => {
  const router = useRouter();
  const { code } = router.query;

  return (
    <div>
      <main>
        <Scanning externalCode={code} />
      </main>
    </div>
  );
};

BookingPage.getInitialProps = async () => ({
  namespacesRequired: ['common']
});

export default BookingPage;
