import React from 'react';
import { useRouter } from 'next/router';
import Layout from '../../../../../src/components/Layout';
import Takeaway from '../../../../../src/features/takeaway';

const CheckoutPage = () => {
  const router = useRouter();
  const { slug, session, orderId, customerId, cancelled } = router.query;

  return (
    <div>
      <main>
        <Layout>
          <Takeaway slug={slug} session={session} checkout orderId={orderId} customerId={customerId} cancelled={cancelled} />
        </Layout>
      </main>
    </div>
  );
};

CheckoutPage.getInitialProps = async () => ({
  namespacesRequired: ['common']
});

export default CheckoutPage;
