import React from 'react';
import { useRouter } from 'next/router';
import Layout from '../../../../src/components/Layout';
import Takeaway from '../../../../src/features/takeaway';

const TakeawayPage = () => {
  const router = useRouter();
  const { slug, delivery, pickup } = router.query;

  return (
    <div>
      <main>
        <Layout>
          <Takeaway slug={slug} delivery={delivery} pickup={pickup} />
        </Layout>
      </main>
    </div>
  );
};

TakeawayPage.getInitialProps = async () => ({
  namespacesRequired: ['common']
});

export default TakeawayPage;
