import React, {useEffect} from 'react';
import {useRouter} from 'next/router';
import Restaurant from '../../../src/features/restaurant';
import {getReservationV2Flag} from "../../../redux/api";

const getRedirectUrl = (slug) => {
  const isProd = process.env.NODE_ENV === 'production';
  return `https://reservations${isProd ? '' : '.dev'}.allo.restaurant/de/${slug}`;
}

const RestaurantPage = () => {
  const router = useRouter();
  const { slug, booking } = router.query;

  useEffect(() => {
    if (!window) return;
    if (!booking) return;
    if (!slug) return;
    getReservationV2Flag(slug)
      .then(flag => {
        if (flag && flag.data && flag.data === true) {
          const url = getRedirectUrl(slug);
          window.location.href = url;
        }
      }).catch(console.error)
  }, [booking, slug, window]);

  return (
    <main>
      <Restaurant slug={slug} booking={booking} />
    </main>
  );
};

RestaurantPage.getInitialProps = async () => ({
  namespacesRequired: ['common']
});

export default RestaurantPage;
