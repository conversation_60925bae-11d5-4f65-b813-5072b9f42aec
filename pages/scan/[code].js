import React from 'react';
import { useRouter } from 'next/router';
import dynamic from 'next/dynamic';

const Scanning = dynamic(() => import('../../src/features/scanning'), { ssr: false });

const ScanPage = () => {
  const router = useRouter();
  const { code } = router.query;

  return (
    <div>
      <main style={{ minHeight: "100vh" }}>
        <Scanning externalCode={code} />
      </main>
    </div>
  );
};

ScanPage.getInitialProps = async () => ({
  namespacesRequired: ['common']
});

export default ScanPage;
