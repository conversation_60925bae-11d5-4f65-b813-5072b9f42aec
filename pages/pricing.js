import React from 'react';
import Head from 'next/head';
import Layout from '../src/components/Layout';
import NavigationBar from '../src/components/NavigationBar';
import GetStarted from '../src/sections/GetStarted';
import Footer from '../src/sections/Footer';
import PricingHero from '../src/sections/PricingHero';
import TryOnline from '../src/sections/TryOnline';
import SuccessGuaranteed from '../src/sections/SuccessGuaranteed';

const PricingPage = () => (
  <>
    <Head>
      <title>Pricing | Leviee Restaurant Management System</title>
      <link rel="canonical" href="https://www.leviee.de/pricing" />
    </Head>
    <div>
      <main>
        <Layout>
          <NavigationBar />
          <PricingHero />
          <TryOnline />
          <SuccessGuaranteed />
          <GetStarted />
          <Footer />
        </Layout>
      </main>
    </div>
  </>
);

export default PricingPage;
