import getConfig from 'next/config';

const { publicRuntimeConfig } = getConfig();

const isProd = publicRuntimeConfig.APPLICATION_ENV === 'production';

const testKey = 'pk_test_51GqhEoKL2AAUnNZPgjROpLVBY0JPxrpu5m3eAkoaVyYePyLTHeOu7twzjl10gzw8RDmVSCyEXnhQLtNGeLEkeC4K00VqzlgoiI';
const liveKey = 'pk_live_88jxp85ykJgEpQKYgUA5Uvqh00fUGMGUTi';

// eslint-disable-next-line import/prefer-default-export
export const key = isProd ? liveKey : testKey;
