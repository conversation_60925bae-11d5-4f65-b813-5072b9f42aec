/* eslint-disable no-unused-vars */
module.exports = () => {
  const publicRuntimeConfig = {
    basePath: '',
    // eslint-disable-next-line consistent-return
    APPLICATION_ENV: (() => {
      if (process.env.NEXT_PUBLIC_LEVIEE_ENV === 'staging') return 'development';
      if (process.env.NEXT_PUBLIC_LEVIEE_ENV === 'production') return 'production';
      if (!process.env.NEXT_PUBLIC_LEVIEE_ENV) return 'local';
    })(),
  };

  const webpack = (config, { buildId, dev, isServer, defaultLoaders, webpack }) => {
    // Note: we provide webpack above so you should not `require` it
    // Perform customizations to webpack config
    config.plugins.push(new webpack.IgnorePlugin(/^\.\/locale$/, /moment$/));

    // Important: return the modified config
    return config;
  };

  return {
    basePath: '',
    assetPrefix: '',
    publicRuntimeConfig,
    webpack
  };
};
