{"name": "customer-ui", "version": "0.1.125", "private": true, "scripts": {"dev": "NODE_ENV=production NEXT_PUBLIC_LEVIEE_ENV=local NODE_OPTIONS=--openssl-legacy-provider next dev", "build": "NODE_OPTIONS=--openssl-legacy-provider next build", "startDev": "NODE_ENV=staging NEXT_PUBLIC_LEVIEE_ENV=local NODE_OPTIONS=--openssl-legacy-provider node server/app.js", "start": "NODE_OPTIONS=--openssl-legacy-provider node server/app.js", "lint": "eslint", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "test": "jest", "install-ui-lib": "npx google-artifactregistry-auth ./.npmrc && yarn add @allo/ui-lib@latest", "lint:fix": "eslint --fix \"src/**/*.+(js|jsx)\"", "format": "prettier --write \"src/**/*.+(js|jsx)\""}, "engines": {"node": "^18", "yarn": "^1"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e", "nodeLinker": "node-modules", "dependencies": {"@material-ui/core": "^4.11.0", "@material-ui/icons": "^4.9.1", "@material-ui/lab": "^4.0.0-alpha.56", "@material-ui/pickers": "^4.0.0-alpha.11", "@reduxjs/toolkit": "^1.4.0", "@stripe/stripe-js": "^1.11.0", "@zxing/library": "^0.17.1", "axios": "0.22.0", "babel-plugin-transform-imports": "^2.0.0", "body-parser": "^1.19.0", "clsx": "^1.1.1", "compression": "^1.7.4", "cookie": "^0.4.1", "default-passive-events": "^2.0.0", "dotenv": "^8.2.0", "express": "^4.17.1", "i18next": "^19.8.2", "immutable": "^4.0.0-rc.12", "initials": "^3.1.1", "intersection-observer": "^0.11.0", "js-cookie": "^2.2.1", "lodash": "^4.17.20", "moment": "^2.29.1", "next": "9.5.5", "next-compose-plugins": "^2.2.0", "next-i18next": "^6.0.3", "next-optimized-images": "^2.6.2", "next-redux-wrapper": "^6.0.2", "next-seo": "^5.14.1", "prop-types": "latest", "qrcode.react": "^3.1.0", "react": "16.13.1", "react-dom": "16.13.1", "react-drag-drawer": "^3.3.4", "react-lazy-load-image-component": "^1.5.6", "react-lottie": "^1.2.3", "react-masonry-css": "^1.0.14", "react-motion": "^0.5.2", "react-otp-input": "^2.4.0", "react-qr-code": "^1.0.3", "react-redux": "^7.2.1", "react-socks": "^2.1.0", "redux": "^4.0.5", "redux-immutablejs": "^0.0.8", "redux-saga": "^1.1.3", "reselect": "^4.1.5", "shortid": "^2.2.16", "simple-react-lightbox": "^3.3.4-1", "swiper": "^6.3.2"}, "devDependencies": {"babel-eslint": "^10.1.0", "corepack": "^0.29.4", "eslint": "^7.10.0", "eslint-config-airbnb": "^18.2.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-jsx-a11y": "^6.3.1", "eslint-plugin-react": "^7.21.3", "eslint-plugin-react-hooks": "^4.1.2", "prettier": "3.2.5", "redux-devtools-extension": "^2.13.8"}}