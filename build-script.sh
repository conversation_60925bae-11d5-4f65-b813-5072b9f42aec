#!/bin/sh
set -o errexit

. ./build-context.sh
yarn
yarn install
yarn lint
#yarn format
yarn build



#. ./build-context.sh corepack yarn


#yarn
#
#if [ -f yarn.lock ]; then yarn --frozen-lockfile;
#elif [ -f package-lock.json ]; then npm ci;
#elif [ -f pnpm-lock.yaml ]; then corepack enable pnpm && pnpm i --frozen-lockfile;
#   else echo "no supported lockfile found." && exit 1;
#fi


#npm run install-devDependencies
