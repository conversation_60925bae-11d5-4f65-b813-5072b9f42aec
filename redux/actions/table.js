import { ok, fail } from '../helpers';

const prefix = 'table';

export const GET = `${prefix}/get`;
export const SUCCESS = `${GET}-success`;
export const FAILURE = `${GET}-failure`;

export function get() {
  return { type: GET };
}

export function success(payload) {
  return {
    type: SUCCESS,
    payload,
  };
}

export function failure(error) {
  return {
    type: FAILURE,
    error,
  };
}

export const APPROVE_PARTICIPANT = `${prefix}/approve-participant`;

export function approveParticipant(id, participantId) {
  return {
    type: APPROVE_PARTICIPANT,
    id,
    participantId
  };
}

export function approveParticipantSuccess(data) {
  return {
    type: ok(APPROVE_PARTICIPANT),
    data,
  };
}

export function approveParticipantFailure(error) {
  return {
    type: fail(APPROVE_PARTICIPANT),
    error
  };
}

export const DELETE_PARTICIPANT = `${prefix}/delete-participant`;

export function deleteParticipant(id, participantId) {
  return {
    type: DELETE_PARTICIPANT,
    id,
    participantId
  };
}

export function deleteParticipantSuccess(data) {
  return {
    type: ok(DELETE_PARTICIPANT),
    data,
  };
}

export function deleteParticipantFailure(error) {
  return {
    type: fail(DELETE_PARTICIPANT),
    error
  };
}

export const NOTIFY_WAITER_FOR_VERIFICATION = `${prefix}/notify-waiter-for-verification`;

export function notifyWaiterForParticipantVerification(id, participantId) {
  return {
    type: NOTIFY_WAITER_FOR_VERIFICATION,
    id,
    participantId
  };
}

export function notifyWaiterForParticipantVerificationSuccess(data) {
  return {
    type: ok(NOTIFY_WAITER_FOR_VERIFICATION),
    data,
  };
}

export function notifyWaiterForParticipantVerificationFailure(error) {
  return {
    type: fail(NOTIFY_WAITER_FOR_VERIFICATION),
    error
  };
}
