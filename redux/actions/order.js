import { ok, fail } from '../helpers';

const prefix = 'order';

export const VALIDATE_CODE = `${prefix}/validate-code`;
export const SCAN_CONFIGURATION = `${prefix}/scan-configuration`;
export const validateCode = (code) => ({
  type: VALIDATE_CODE,
  code
});
export const validateCodeSuccess = (data) => ({
  type: ok(VALIDATE_CODE),
  data,
});
export const validateCodeFailure = (error) => ({
  type: fail(VALIDATE_CODE),
  error
});

export const scanConfigurationBlocker = (error) => ({
  type: fail(SCAN_CONFIGURATION),
  error
});

export const RESOLVE_ORDER = `${prefix}/resolve-order`;
export const resolveOrder = (code, customer) => ({
  type: RESOLVE_ORDER,
  code,
  customer
});
export const resolveOrderSuccess = (data) => ({
  type: ok(RESOLVE_ORDER),
  data,
});
export const resolveOrderFailure = (error) => ({
  type: fail(RESOLVE_ORDER),
  error
});

export const GET_ACTIVE_ORDER = `${prefix}/get-active-order`;
export function getActiveOrder() {
  return {
    type: GET_ACTIVE_ORDER
  };
}

export const GET_ORDER = `${prefix}/get-order`;
export function getOrder(id) {
  return {
    type: GET_ORDER,
    id
  };
}
export function getOrderSuccess(payload) {
  return {
    type: ok(GET_ORDER),
    payload,
  };
}
export function getOrderFailure(error) {
  return {
    type: fail(GET_ORDER),
    error
  };
}

export const GET_ORDER_CART = `${prefix}/get-order-cart`;
export function getOrderCart(id) {
  return {
    type: GET_ORDER_CART,
    id
  };
}
export function getOrderCartSuccess(payload) {
  return {
    type: ok(GET_ORDER_CART),
    payload,
  };
}
export function getOrderCartFailure(error) {
  return {
    type: fail(GET_ORDER_CART),
    error
  };
}

export const GET_ORDER_FOR_RECEIPT = `${prefix}/get-order`;
export function getOrderForReceipt(id) {
  return {
    type: GET_ORDER_FOR_RECEIPT,
    id
  };
}
export function getOrderForReceiptSuccess(payload) {
  return {
    type: ok(GET_ORDER_FOR_RECEIPT),
    payload,
  };
}
export function getOrderForReceiptFailure(error) {
  return {
    type: fail(GET_ORDER_FOR_RECEIPT),
    error
  };
}

export const GET_ORDER_CART_FOR_RECEIPT = `${prefix}/get-order-cart`;
export function getOrderCartForReceipt(id) {
  return {
    type: GET_ORDER_CART_FOR_RECEIPT,
    id
  };
}
export function getOrderCartForReceiptSuccess(payload) {
  return {
    type: ok(GET_ORDER_CART_FOR_RECEIPT),
    payload,
  };
}
export function getOrderCartForReceiptFailure(error) {
  return {
    type: fail(GET_ORDER_CART_FOR_RECEIPT),
    error
  };
}

export const GET_ORDER_SUMMARY = `${prefix}/get-order-summary`;
export function getOrderSummary(id) {
  return {
    type: GET_ORDER_SUMMARY,
    id
  };
}
export function getOrderSummarySuccess(payload) {
  return {
    type: ok(GET_ORDER_SUMMARY),
    payload,
  };
}
export function getOrderSummaryFailure(error) {
  return {
    type: fail(GET_ORDER_SUMMARY),
    error
  };
}

export const GET_ORDER_CART_SUMMARY = `${prefix}/get-order-cart-summary`;
export function getOrderCartSummary(id) {
  return {
    type: GET_ORDER_CART_SUMMARY,
    id
  };
}
export function getOrderCartSummarySuccess(payload) {
  return {
    type: ok(GET_ORDER_CART_SUMMARY),
    payload,
  };
}
export function getOrderCartSummaryFailure(error) {
  return {
    type: fail(GET_ORDER_CART_SUMMARY),
    error
  };
}

export const POLL_ORDER = `${prefix}/get-order-polling`;
export const STOP_POLL_ORDER = `${prefix}/stop-order-polling`;
export function pollOrder(id) {
  return {
    type: POLL_ORDER,
    id
  };
}
export function stopPollOrder() {
  return {
    type: STOP_POLL_ORDER
  };
}

export const POLL_ORDER_SUMMARY = `${prefix}/start-order-summary-polling`;
export const STOP_POLL_ORDER_SUMMARY = `${prefix}/stop-order-summary-polling`;
export function pullOrderSummary(id) {
  return {
    type: POLL_ORDER_SUMMARY,
    id
  };
}
export function stopPollOrderSummary() {
  return {
    type: STOP_POLL_ORDER_SUMMARY
  };
}

export const POLL_ORDER_CART = `${prefix}/get-order-cart-polling`;
export const STOP_POLL_ORDER_CART = `${prefix}/stop-order-cart-polling`;
export function pollOrderCart() {
  return {
    type: POLL_ORDER_CART,
  };
}
export function stopPollOrderCart() {
  return {
    type: STOP_POLL_ORDER_CART
  };
}

export const POLL_ORDER_CART_SUMMARY = `${prefix}/start-order-cart-summary-polling`;
export const STOP_POLL_ORDER_CART_SUMMARY = `${prefix}/stop-order-cart-summary-polling`;
export function pullOrderCartSummary(id) {
  return {
    type: POLL_ORDER_CART_SUMMARY,
    id
  };
}
export function stopPollOrderCartSummary() {
  return {
    type: STOP_POLL_ORDER_CART_SUMMARY
  };
}

export const ADD_ITEM = `${prefix}/add-item`;
export function addItem(orderId, itemData) {
  return {
    type: ADD_ITEM,
    orderId,
    itemData
  };
}
export function addItemSuccess(payload) {
  return {
    type: ok(ADD_ITEM),
    payload,
  };
}
export function addItemFailure(error) {
  return {
    type: fail(ADD_ITEM),
    error
  };
}

export const ADD_NESTED_ITEM = `${prefix}/add-nested-item`;
export function addNestedItem(orderId, itemId, itemData) {
  return {
    type: ADD_NESTED_ITEM,
    orderId,
    itemId,
    itemData
  };
}
export function addNestedItemSuccess(payload) {
  return {
    type: ok(ADD_NESTED_ITEM),
    payload,
  };
}
export function addNestedItemFailure(error) {
  return {
    type: fail(ADD_NESTED_ITEM),
    error
  };
}

export const REMOVE_ITEM = `${prefix}/remove-item`;
export function removeItem(id, itemId) {
  return {
    type: REMOVE_ITEM,
    id,
    itemId
  };
}
export function removeItemSuccess(payload) {
  return {
    type: ok(REMOVE_ITEM),
    payload,
  };
}
export function removeItemFailure(error) {
  return {
    type: fail(REMOVE_ITEM),
    error
  };
}

export const CONFIRM_ITEMS = `${prefix}/confirm-items`;
export function confirmItems(id) {
  return {
    type: CONFIRM_ITEMS,
    id
  };
}
export function confirmItemsSuccess(payload) {
  return {
    type: ok(CONFIRM_ITEMS),
    payload,
  };
}
export function confirmItemsFailure(error) {
  return {
    type: fail(CONFIRM_ITEMS),
    error
  };
}

export const SHOW_ORDERING_BLOCKER = `${prefix}/show-ordering-blocker`;
export function setShowOrderingBlocker(show) {
  return {
    type: SHOW_ORDERING_BLOCKER,
    show
  }
}

export const CREATE_PICKUP_BY_SLUG = `${prefix}/create-pickup-by-slug`;
export function createPickupBySlug(slug, orderType) {
  return {
    type: CREATE_PICKUP_BY_SLUG,
    slug,
    orderType
  };
}
export function createPickupBySlugSuccess(payload) {
  return {
    type: ok(CREATE_PICKUP_BY_SLUG),
    payload,
  };
}
export function createPickupBySlugFailure(error) {
  return {
    type: fail(CREATE_PICKUP_BY_SLUG),
    error
  };
}

export const ADD_PICKUP_ITEM = `${prefix}/add-pickup-item`;
export function addPickupItem(itemData) {
  return {
    type: ADD_PICKUP_ITEM,
    itemData
  };
}
export function addPickupItemSuccess(payload) {
  return {
    type: ok(ADD_PICKUP_ITEM),
    payload,
  };
}
export function addPickupItemFailure(error) {
  return {
    type: fail(ADD_PICKUP_ITEM),
    error
  };
}

export const ADD_ORDER_CART_ITEM = `${prefix}/add-order-cart-item`;
export function addOrderCartItem(itemData) {
  return {
    type: ADD_ORDER_CART_ITEM,
    itemData
  };
}
export function addOrderCartItemSuccess(payload) {
  return {
    type: ok(ADD_ORDER_CART_ITEM),
    payload,
  };
}
export function addOrderCartItemFailure(error) {
  return {
    type: fail(ADD_ORDER_CART_ITEM),
    error
  };
}

export const REMOVE_PICKUP_ITEM = `${prefix}/remove-pickup-item`;
export function removePickupItem(id) {
  return {
    type: REMOVE_PICKUP_ITEM,
    id
  };
}

export function removePickupItemSuccess(payload) {
  return {
    type: ok(REMOVE_PICKUP_ITEM),
    payload,
  };
}
export function removePickupItemFailure(error) {
  return {
    type: fail(REMOVE_PICKUP_ITEM),
    error
  };
}

export const REMOVE_ORDER_CART_ITEM = `${prefix}/remove-order-cart-item`;
export function removeOrderCartItem(id) {
  return {
    type: REMOVE_ORDER_CART_ITEM,
    id
  };
}

export function removeOrderCartItemSuccess(payload) {
  return {
    type: ok(REMOVE_ORDER_CART_ITEM),
    payload,
  };
}
export function removeOrderCartItemFailure(error) {
  return {
    type: fail(REMOVE_ORDER_CART_ITEM),
    error
  };
}

export const SET_ITEM = `${prefix}/set-item`;
export function setItem(item) {
  return {
    type: SET_ITEM,
    item
  };
}
export function setItemSuccess(item, menuItem) {
  return {
    type: ok(SET_ITEM),
    item,
    menuItem
  };
}
export function setItemFailure(error) {
  return {
    type: fail(SET_ITEM),
    error
  };
}

export const RESET_ITEM = `${prefix}/reset-item`;
export function resetItem() {
  return {
    type: RESET_ITEM
  };
}
export function resetItemSuccess(payload) {
  return {
    type: ok(RESET_ITEM),
    payload,
  };
}
export function resetItemFailure(error) {
  return {
    type: fail(RESET_ITEM),
    error
  };
}
