import { fail, ok } from '../helpers';

const prefix = 'app';

export const INIT = `${prefix}/init`;
export function init() {
  return {
    type: INIT
  };
}
export function initSuccess() {
  return {
    type: ok(INIT)
  };
}
export function initFailure() {
  return {
    type: fail(INIT)
  };
}

export const SET_ME = `${prefix}/set-me`;
export function setMe(payload) {
  return {
    type: SET_ME,
    payload
  };
}

export const SET_MY_ACTIVE_ORDER = `${prefix}/set-my-active-order`;
export function setMyActiveOrder(payload) {
  return {
    type: SET_MY_ACTIVE_ORDER,
    payload
  };
}

export const SCAN = `${prefix}/scan`;
export function scan(code) {
  return {
    type: SCAN,
    code
  };
}
export function scanSuccess() {
  return {
    type: ok(SCAN)
  };
}
export function scanFailure() {
  return {
    type: fail(SCAN)
  };
}

export const SHOW_NOTIFICATION = `${prefix}/show-notification`;
export function showNotification(msgKey, severity) {
  return {
    type: SHOW_NOTIFICATION,
    msgKey,
    severity
  };
}

export const CLOSE_NOTIFICATION = `${prefix}/close-notification`;
export function closeNotification() {
  return {
    type: CLOSE_NOTIFICATION
  };
}
