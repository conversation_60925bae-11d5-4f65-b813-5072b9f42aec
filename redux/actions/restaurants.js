import { ok, fail } from '../helpers';

const prefix = 'restaurants';

export const GET_RESTAURANTS = `${prefix}/get-restaurant`;

export function getRestaurants() {
  return { type: GET_RESTAURANTS };
}

export function getRestaurantsSuccess(payload) {
  return {
    type: ok(GET_RESTAURANTS),
    payload,
  };
}

export function getRestaurantsFailure(error) {
  return {
    type: fail(GET_RESTAURANTS),
    error,
  };
}

export const GET_RESTAURANT_BY_SLUG = `${prefix}/get-restaurant-by-slug`;
export const GET_RESTAURANT_BY_SLUG_AND_ORDER_TYPE = `${prefix}/get-restaurant-by-slug-and-order-type`;

export function getRestaurantBySlug(slug) {
  return {
    type: GET_RESTAURANT_BY_SLUG,
    slug
  };
}

export function getRestaurantBySlugAndOrderType(slug, orderType) {
  return {
    type: GET_RESTAURANT_BY_SLUG_AND_ORDER_TYPE,
    slug,
    orderType
  };
}

export function getRestaurantBySlugSuccess(payload) {
  return {
    type: ok(GET_RESTAURANT_BY_SLUG),
    payload,
  };
}

export function getRestaurantBySlugFailure(error) {
  return {
    type: fail(GET_RESTAURANT_BY_SLUG),
    error,
  };
}

export const GET_RESTAURANT_BY_ID = `${prefix}/get-restaurant-by-id`;

export function getRestaurantById(id) {
  return {
    type: GET_RESTAURANT_BY_ID,
    id
  };
}

export function getRestaurantByIdSuccess(payload) {
  return {
    type: ok(GET_RESTAURANT_BY_ID),
    payload,
  };
}

export function getRestaurantByIdFailure(error) {
  return {
    type: fail(GET_RESTAURANT_BY_ID),
    error,
  };
}

export const GET_RESTAURANT_GALLERY_ITEMS = `${prefix}/get-restaurant-gallery-items`;

export function getRestaurantGalleryItemsSuccess(payload) {
  return {
    type: ok(GET_RESTAURANT_GALLERY_ITEMS),
    payload,
  };
}
export const UPDATE_RESTAURANT_PICKUP_REQUESTS = `${prefix}/update-restaurant-pickup-requests`;

export function updateRestaurantPickupRequests(id) {
  return {
    type: UPDATE_RESTAURANT_PICKUP_REQUESTS,
    id
  };
}

export const GET_RESTAURANT_PICKUP_TIMES = `${prefix}/get-restaurant-pickup-times`;
export const GET_RESTAURANT_SCHEDULING_TIMES = `${prefix}/get-restaurant-scheduling-times`;

export function getRestaurantPickupTimes(id) {
  return {
    type: GET_RESTAURANT_PICKUP_TIMES,
    id
  };
}

export function getRestaurantPickupTimesSuccess(payload) {
  return {
    type: ok(GET_RESTAURANT_PICKUP_TIMES),
    payload,
  };
}

export function getRestaurantPickupTimesFailure(error) {
  return {
    type: fail(GET_RESTAURANT_PICKUP_TIMES),
    error,
  };
}

export function getRestaurantSchedulingTimesSuccess(payload) {
  return {
    type: ok(GET_RESTAURANT_SCHEDULING_TIMES),
    payload,
  };
}

export function getRestaurantSchedulingTimesFailure(error) {
  return {
    type: fail(GET_RESTAURANT_SCHEDULING_TIMES),
    error,
  };
}
