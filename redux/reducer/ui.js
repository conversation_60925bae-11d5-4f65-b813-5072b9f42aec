import { fromJS } from 'immutable';
import { appActions, orderActions } from '../actions';
import { fail, ok } from '../helpers';

export const initialState = fromJS({
  notification: {
    msgKey: null,
    severity: 'success'
  },
  me: {
    loaded: false
  },
  myActiveOrder: {
    loaded: false
  },
  scan: {
    loading: false,
    error: false
  },
  welcome: {
    loading: false,
    error: false
  },
  currentOrder: {
    loading: false
  }
});

function reducer(state = initialState, action) {
  switch (action.type) {
    case appActions.SET_ME: {
      return state
        .setIn(['me', 'loaded'], true);
    }
    case appActions.SET_MY_ACTIVE_ORDER: {
      return state
        .setIn(['myActiveOrder', 'loaded'], true);
    }
    case appActions.SCAN: {
      return state
        .setIn(['scan', 'loading'], true)
        .setIn(['scan', 'error'], false);
    }
    case ok(appActions.SCAN): {
      // view stays in progress since its covered by router push
      return state;
    }
    case fail(appActions.SCAN): {
      return state
        .setIn(['scan', 'loading'], false)
        .setIn(['scan', 'error'], true);
    }
    case fail(orderActions.SCAN_CONFIGURATION): {
      return state
        .setIn(['notification', 'msgKey'], 'notification-scan-not-configured')
        .setIn(['notification', 'severity'], 'error');
    }
    case fail(orderActions.VALIDATE_CODE): {
      return state
        .setIn(['notification', 'msgKey'], 'notification-invalid-order-code')
        .setIn(['notification', 'severity'], 'error');
    }
    case orderActions.RESOLVE_ORDER: {
      return state
        .setIn(['welcome', 'loading'], true)
        .setIn(['welcome', 'error'], false);
    }
    case ok(orderActions.RESOLVE_ORDER): {
      // view stays in progress since its covered by router push
      return state;
    }
    case fail(orderActions.RESOLVE_ORDER): {
      return state
        .setIn(['welcome', 'loading'], false)
        .setIn(['welcome', 'error'], true)
        .setIn(['scan', 'loading'], false)
        .setIn(['scan', 'error'], true);
    }
    case appActions.SHOW_NOTIFICATION: {
      const { msgKey, severity = 'success' } = action;
      return state
        .setIn(['notification', 'msgKey'], msgKey)
        .setIn(['notification', 'severity'], severity);
    }
    case appActions.CLOSE_NOTIFICATION: {
      return state
        .setIn(['notification', 'msgKey'], null);
    }
    case orderActions.ADD_PICKUP_ITEM:
    case orderActions.ADD_ITEM:
    case orderActions.ADD_NESTED_ITEM: {
      return state
        .setIn(['currentOrder', 'loading'], true);
    }
  
    case ok(orderActions.ADD_PICKUP_ITEM):
    case ok(orderActions.ADD_ITEM):
    case ok(orderActions.ADD_NESTED_ITEM):
    case fail(orderActions.ADD_PICKUP_ITEM):
    case fail(orderActions.ADD_ITEM):
    case fail(orderActions.ADD_NESTED_ITEM): {
      return state
        .setIn(['currentOrder', 'loading'], false);
    }
    
    default:
      return state;
  }
}

export default reducer;
