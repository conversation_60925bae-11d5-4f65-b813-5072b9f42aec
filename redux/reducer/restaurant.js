import { fromJS } from 'immutable';
import { ok, fail } from '../helpers';
import { restaurantsActions } from '../actions';

export const initialState = fromJS({
  restaurant: {},
  pickupTimes: null,
  schedulingTimes: [],
  gallery: []
});

function reducer(state = initialState, action) {
  switch (action.type) {
    case ok(restaurantsActions.GET_RESTAURANT_BY_SLUG):
    case ok(restaurantsActions.GET_RESTAURANT_BY_ID): {
      const data = action.payload;
      return state
        .set('restaurant', fromJS(data));
    }

    case fail(restaurantsActions.GET_RESTAURANT_BY_SLUG):
    case fail(restaurantsActions.GET_RESTAURANT_BY_ID): {
      return state;
    }

    case ok(restaurantsActions.GET_RESTAURANT_GALLERY_ITEMS): {
      const { items } = action.payload;
      return state
        .set('gallery', fromJS(items));
    }

    case ok(restaurantsActions.GET_RESTAURANT_PICKUP_TIMES): {
      const data = action.payload;
      return state
        .set('pickupTimes', fromJS(data));
    }

    case ok(restaurantsActions.GET_RESTAURANT_SCHEDULING_TIMES): {
      const data = action.payload;
      return state
        .set('schedulingTimes', fromJS(data));
    }

    case fail(restaurantsActions.GET_RESTAURANT_PICKUP_TIMES): {
      return state.set('pickupTimes', fromJS([]));
    }

    default:
      return state;
  }
}

export default reducer;
