import { fromJS } from 'immutable';
import Cookies from 'js-cookie';
import { appActions } from '../actions';
import isEmpty from '../../src/utilities/isEmpty';

export const initialState = fromJS({
  customer: {},
  activeOrder: {}
});

function reducer(state = initialState, action) {
  switch (action.type) {
    case appActions.SET_ME: {
      const data = action.payload;

      if (!isEmpty(data) && data.id) {
        // on completing pickup, we create customer if not there
        // if not customerId store, store the coming customerId when setting me()
        const customerId = Cookies.get('lvCustomerId');
        if (!customerId) {
          const { id } = data;
          Cookies.set('lvCustomerId', id, { expires: 90000 });
        }
      }

      return state
        .set('customer', fromJS(data));
    }
    case appActions.SET_MY_ACTIVE_ORDER: {
      const data = action.payload;
      return state
        .set('activeOrder', fromJS(data));
    }

    default:
      return state;
  }
}

export default reducer;
