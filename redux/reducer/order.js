import { fromJS } from 'immutable';
import Cookies from 'js-cookie';
import isEmpty from '../../src/utilities/isEmpty';
import { fail, ok } from '../helpers';
import { orderActions, tableActions } from '../actions';
import getByOrderItemCode from '../../src/utilities/getters/getByOrderItemCode';
import getOrderItemCodes from '../../src/utilities/getters/getOrderItemCodes';

export const initialState = fromJS({
  order: {},
  customer: {},
  participant: {},
  discount: {},
  items: {
    confirmed: {
      quantity: 0,
      total: 0,
      discountTotal: 0,
      items: [],
      codes: [],
      byCode: [],
    },
    unconfirmed: {
      quantity: 0,
      total: 0,
      discountTotal: 0,
      items: [],
      codes: [],
      byCode: [],
    },
    ongoing: {
      items: []
    }
  },
  summary: {},
  details: {
    // item specified for ongoing order / edit view of confirmed items
    item: {},
    menuItem: {}
  },
  showOrderingBlocker: false
});

const getDiscountAmount = (amount, discount = {}) => {
  let resolvedDiscountAmount = 0;
  if (!amount) {
    return resolvedDiscountAmount
  }
  
  if (!discount || isEmpty(discount)) {
    return resolvedDiscountAmount
  }
  
  const { amount: discountAmount, percentage } = (discount || {})
  
  try {
    if (discountAmount) {
      resolvedDiscountAmount = discountAmount
    } else if (percentage) {
      resolvedDiscountAmount = amount * percentage / 100
    }
  } catch (e) {
  
  }
  
  return resolvedDiscountAmount
}

function reducer(state = initialState, action) {
  switch (action.type) {
    case ok(orderActions.GET_ORDER):
    case ok(orderActions.CREATE_PICKUP_BY_SLUG):
    case ok(orderActions.ADD_ITEM):
    case ok(orderActions.ADD_PICKUP_ITEM): {
      const data = action.payload;
      let { items = [], discount = {} } = data;
      items = (items ?? []).filter((i) => !(i.isDiscount || i.isFee));

      const { participants = [], customer = {} } = data;

      const customerId = Cookies.get('lvCustomerId');
      const participant = (participants || []).find((i) => i.customerId === customerId) || {};

      // summarize unconfirmed items
      const unconfirmed = (items ?? []).filter((i) => (customerId ? i.customerId === customerId : !i.customerId) && i.status === 'UNCONFIRMED');
      let unconfirmedQtd = 0;
      let unconfirmedTotal = 0;
      unconfirmed.forEach(({ total, qtd = 1 }) => {
        unconfirmedQtd += qtd;
        unconfirmedTotal += total;
      });
      
      let unconfirmedTotalDiscount = getDiscountAmount(unconfirmedTotal, discount);

      // summarize confirmed items
      const confirmed = (items ?? []).filter((i) => (customerId ? i.customerId === customerId : !i.customerId) && i.status !== 'UNCONFIRMED');
      let confirmedQtd = 0;
      let confirmedTotal = 0;
      confirmed.forEach(({ total, qtd = 1 }) => {
        confirmedQtd += qtd;
        confirmedTotal += total;
      });
  
      let confirmedTotalDiscount = getDiscountAmount(confirmedTotal, discount);

      const updatedState = state;
      if (!isEmpty(customer)) {
        updatedState.set('customer', fromJS(customer));
      }

      const ongoingConfirmed = (items ?? []).filter((i) => i.status === 'CONFIRMED' && !!i.ongoing && !i.nested)

      return updatedState
        .set('order', fromJS(data ?? {}))
        .set('participant', fromJS(participant))
        .set('discount', fromJS(discount))
        .setIn(['items', 'unconfirmed', 'items'], fromJS(unconfirmed))
        .setIn(['items', 'unconfirmed', 'quantity'], unconfirmedQtd)
        .setIn(['items', 'unconfirmed', 'total'], unconfirmedTotal)
        .setIn(['items', 'unconfirmed', 'discountTotal'], unconfirmedTotalDiscount)
        .setIn(['items', 'unconfirmed', 'byCode'], fromJS(getByOrderItemCode(unconfirmed)))
        .setIn(['items', 'unconfirmed', 'codes'], fromJS(getOrderItemCodes(unconfirmed)))
        .setIn(['items', 'confirmed', 'items'], fromJS(confirmed))
        .setIn(['items', 'confirmed', 'quantity'], confirmedQtd)
        .setIn(['items', 'confirmed', 'total'], confirmedTotal)
        .setIn(['items', 'confirmed', 'discountTotal'], confirmedTotalDiscount)
        .setIn(['items', 'confirmed', 'byCode'], fromJS(getByOrderItemCode(confirmed)))
        .setIn(['items', 'confirmed', 'codes'], fromJS(getOrderItemCodes(confirmed)))
        .setIn(['items', 'ongoing', 'items'], fromJS(ongoingConfirmed));
    }
    
    case ok(orderActions.GET_ORDER_CART):
    case ok(orderActions.ADD_ORDER_CART_ITEM): {
      const data = action.payload;
      let { items = [] } = data;
      items = (items ?? []).filter((i) => !(i.isDiscount || i.isFee));
    
      const { participants = [], customer = {} } = data;
    
      const customerId = Cookies.get('lvCustomerId');
      const participant = (participants || []).find((i) => i.customerId === customerId) || {};
    
      // summarize unconfirmed items
      const unconfirmed = (items ?? []).filter((i) => i.status === 'UNCONFIRMED');
      let unconfirmedQtd = 0;
      let unconfirmedTotal = 0;
      unconfirmed.forEach(({ total, qtd = 1 }) => {
        unconfirmedQtd += qtd;
        unconfirmedTotal += total;
      });
    
      // summarize confirmed items
      const confirmed = (items ?? []).filter((i) => i.status !== 'UNCONFIRMED');
      let confirmedQtd = 0;
      let confirmedTotal = 0;
      confirmed.forEach(({ total, qtd = 1 }) => {
        confirmedQtd += qtd;
        confirmedTotal += total;
      });
    
      const updatedState = state;
      if (!isEmpty(customer)) {
        updatedState.set('customer', fromJS(customer));
      }
    
      return updatedState
        .set('order', fromJS(data ?? {}))
        .set('participant', fromJS(participant))
        .setIn(['items', 'unconfirmed', 'items'], fromJS(unconfirmed))
        .setIn(['items', 'unconfirmed', 'quantity'], unconfirmedQtd)
        .setIn(['items', 'unconfirmed', 'total'], unconfirmedTotal)
        .setIn(['items', 'unconfirmed', 'byCode'], fromJS(getByOrderItemCode(unconfirmed)))
        .setIn(['items', 'unconfirmed', 'codes'], fromJS(getOrderItemCodes(unconfirmed)))
        .setIn(['items', 'confirmed', 'items'], fromJS(confirmed))
        .setIn(['items', 'confirmed', 'quantity'], confirmedQtd)
        .setIn(['items', 'confirmed', 'total'], confirmedTotal)
        .setIn(['items', 'confirmed', 'byCode'], fromJS(getByOrderItemCode(confirmed)))
        .setIn(['items', 'confirmed', 'codes'], fromJS(getOrderItemCodes(confirmed)))
    }

    case ok(orderActions.GET_ORDER_SUMMARY): {
      const data = action.payload;
      return state
        .set('summary', fromJS(data ?? {}));
    }

    case ok(orderActions.REMOVE_ITEM):
    case ok(orderActions.REMOVE_ORDER_CART_ITEM):
    case ok(orderActions.REMOVE_PICKUP_ITEM): {
      const data = action.payload;
      return state
        .set('summary', fromJS(data ?? {}));
    }
    case ok(tableActions.NOTIFY_WAITER_FOR_VERIFICATION): {
      return state
        .setIn(['order', 'verificationRequestSent'], true);
    }

    case ok(orderActions.SET_ITEM): {
      const { item, menuItem } = action;
      return state
        .setIn(['details', 'item'], fromJS(item))
        .setIn(['details', 'menuItem'], fromJS(menuItem));
    }

    case fail(orderActions.ADD_NESTED_ITEM):
    case orderActions.RESET_ITEM: {
      return state
        .setIn(['details', 'item'], {})
        .setIn(['details', 'menuItem'], {});
    }
    
    case fail(orderActions.CONFIRM_ITEMS): {
      return state
        .set('showOrderingBlocker', true)
    }
    
    case orderActions.SHOW_ORDERING_BLOCKER: {
      const show = action.payload;
      return state
        .set('showOrderingBlocker', show)
    }

    default:
      return state;
  }
}

export default reducer;
