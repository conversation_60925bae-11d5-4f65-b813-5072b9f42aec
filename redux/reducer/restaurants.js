import { fromJS } from 'immutable';
import { ok, fail } from '../helpers';
import { restaurantsActions } from '../actions';
import ids from '../../src/utilities/ids';
import byId from '../../src/utilities/byId';

export const initialState = fromJS({
  ids: [],
  byId: []
});

function reducer(state = initialState, action) {
  switch (action.type) {
    case ok(restaurantsActions.GET_RESTAURANTS): {
      const items = action.payload;
      return state
        .set('ids', fromJS(ids((items || []).reverse())))
        .set('byId', fromJS(byId(items)));
    }

    case fail(restaurantsActions.GET_RESTAURANTS): {
      return state;
    }

    default:
      return state;
  }
}

export default reducer;
