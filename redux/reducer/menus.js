import { fromJS } from 'immutable';
import { menusActions } from '../actions';
import ids from '../../src/utilities/ids';
import byId from '../../src/utilities/byId';

export const initialState = fromJS({
  ids: [],
  byId: []
});

function reducer(state = initialState, action) {
  switch (action.type) {
    case menusActions.SUCCESS: {
      const data = action.payload;
      return state
        .set('ids', fromJS(ids(data)))
        .set('byId', fromJS(byId(data)));
    }

    default:
      return state;
  }
}

export default reducer;
