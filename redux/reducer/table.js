import { fromJS } from 'immutable';
import { ok } from '../helpers';
import { tableActions } from '../actions';

export const initialState = fromJS({
  table: {},
  participants: []
});

function reducer(state = initialState, action) {
  switch (action.type) {
    case ok(tableActions.GET): {
      const data = action.payload;
      return state.set('table', fromJS(data))
    }
    case ok(tableActions.APPROVE_PARTICIPANT): {
      return state;
    }

    default:
      return state;
  }
}

export default reducer;
