import { fork, all } from 'redux-saga/effects';

import appSaga from './app';
import restaurantsSaga from './restaurants';
import menusSaga from './menus';
import orderSaga, { pollOrderWatcher, pollOrderSummaryWatcher, pollOrderCartWatcher, pollOrderCartSummaryWatcher } from './order';
import tableSaga from './table';

export default function* root() {
  yield all([
    fork(appSaga),
    fork(restaurantsSaga),
    fork(menusSaga),
    fork(orderSaga),
    fork(pollOrderWatcher),
    fork(pollOrderCartWatcher),
    fork(pollOrderSummaryWatcher),
    fork(pollOrderCartSummaryWatcher),
    fork(tableSaga)
  ]);
}
