import { all, call, put, takeLatest } from 'redux-saga/effects';
import {tableActions} from '../actions';
import * as api from '../api';
import { getOrder } from './order';

export function* getTableById({ id }) {
  try {
    const { data } = yield call(api.getTableById, id);
    yield put(tableActions.success(data));
  } catch (err) {
    yield put(tableActions.failure(err));
  }
}

function* approveParticipant({ id, participantId }) {
  try {
    const { data } = yield call(api.approveParticipant, id, participantId);
    yield put(tableActions.approveParticipantSuccess(data));
    yield* getOrder({ id });
  } catch (err) {
    yield put(tableActions.approveParticipantFailure(err));
  }
}

function* deleteParticipant({ id, participantId }) {
  try {
    const { data } = yield call(api.deleteParticipant, id, participantId);
    yield put(tableActions.deleteParticipantSuccess(data));
    yield* getOrder({ id });
  } catch (err) {
    yield put(tableActions.deleteParticipantFailure(err));
  }
}

function* notifyWaiterForParticipantVerification({ id }) {
  try {
    yield call(api.notifyWaiterForParticipantVerification, id);
    yield put(tableActions.notifyWaiterForParticipantVerificationSuccess());
  } catch (err) {
    yield put(tableActions.notifyWaiterForParticipantVerificationFailure(err));
  }
}

function* rootSaga() {
  yield all([
    takeLatest(tableActions.APPROVE_PARTICIPANT, approveParticipant),
    takeLatest(tableActions.DELETE_PARTICIPANT, deleteParticipant),
    takeLatest(tableActions.NOTIFY_WAITER_FOR_VERIFICATION, notifyWaiterForParticipantVerification),
  ]);
}

export default rootSaga;
