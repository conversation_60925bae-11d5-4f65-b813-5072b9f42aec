import { all, call, put, takeLatest, select } from 'redux-saga/effects';
import { menusActions, restaurantsActions } from '../actions';
import * as api from '../api';
import { orderSelectors } from '../selectors';

function* getRestaurants() {
  try {
    const { data } = yield call(api.getRestaurants);
    yield put(restaurantsActions.getRestaurantsSuccess(data));
  } catch (err) {
    yield put(restaurantsActions.getRestaurantsFailure(err));
  }
}

export function* getRestaurantPickupTimes({ id }) {
  try {
    const { data: schedulingTimes } = yield call(api.getSchedulingTimes, id);
    const { data } = yield call(api.getRestaurantPickupTimes, id);
    yield put(restaurantsActions.getRestaurantPickupTimesSuccess(data));
    yield put(restaurantsActions.getRestaurantSchedulingTimesSuccess(schedulingTimes));
  } catch (err) {
    yield put(restaurantsActions.getRestaurantPickupTimesFailure(err));
  }
}

export function* getRestaurantBySlug({ slug }) {
  try {
    const { order = {} } = yield select(orderSelectors.getOrder);
    const { type } = (order ?? {});
    const { data } = yield call(api.getRestaurantBySlug, slug, type);
    yield put(restaurantsActions.getRestaurantBySlugSuccess(data));
    const { menus = [], galleries = [] } = data;
    const { items = [] } = (galleries[0] || {});
    yield put(restaurantsActions.getRestaurantGalleryItemsSuccess({ items }));
    yield put(menusActions.success(menus));
    // yield* getRestaurantPickupTimes({ id: data.id });
  } catch (err) {
    yield put(restaurantsActions.getRestaurantBySlugFailure(err));
  }
}

export function* getRestaurantBySlugAndOrderType({ slug, orderType = '' }) {
  try {
    const { data } = yield call(api.getRestaurantBySlug, slug, orderType);
    yield put(restaurantsActions.getRestaurantBySlugSuccess(data));
    const { menus = [], galleries = [] } = data;
    const { items = [] } = (galleries[0] || {});
    yield put(restaurantsActions.getRestaurantGalleryItemsSuccess({ items }));
    yield put(menusActions.success(menus));
  } catch (err) {
    yield put(restaurantsActions.getRestaurantBySlugFailure(err));
  }
}

export function* getRestaurantById({ id }) {
  try {
    const { order = {} } = yield select(orderSelectors.getOrder);
    const { type } = (order ?? {});
    const { data } = yield call(api.getRestaurantById, id, type);
    yield put(restaurantsActions.getRestaurantByIdSuccess(data));
    const { menus = [], galleries = [] } = data;
    const { items = [] } = (galleries[0] || {});
    yield put(restaurantsActions.getRestaurantGalleryItemsSuccess({ items }));
    yield put(menusActions.success(menus));
  } catch (err) {
    yield put(restaurantsActions.getRestaurantByIdFailure(err));
  }
}

function* updateRestaurantPickupRequests({ id }) {
  try {
    yield call(api.updateRestaurantPickupRequests, id);
    // eslint-disable-next-line no-empty
  } catch (err) {}
}

function* rootSaga() {
  yield all([
    takeLatest(restaurantsActions.GET_RESTAURANTS, getRestaurants),
    takeLatest(restaurantsActions.GET_RESTAURANT_BY_SLUG, getRestaurantBySlug),
    takeLatest(restaurantsActions.GET_RESTAURANT_BY_SLUG_AND_ORDER_TYPE, getRestaurantBySlugAndOrderType),
    takeLatest(restaurantsActions.GET_RESTAURANT_BY_ID, getRestaurantById),
    takeLatest(restaurantsActions.UPDATE_RESTAURANT_PICKUP_REQUESTS, updateRestaurantPickupRequests),
    takeLatest(restaurantsActions.GET_RESTAURANT_PICKUP_TIMES, getRestaurantPickupTimes),
  ]);
}

export default rootSaga;
