import { all, call, put, takeLatest } from 'redux-saga/effects';
import { menusActions } from '../actions';
import * as api from '../api';

export function* getMenusByRestaurantId({ id }) {
  try {
    const { data } = yield call(api.getRestaurantMenus, id);
    yield put(menusActions.success(data));
  } catch (err) {
    yield put(menusActions.failure(err));
  }
}

function* rootSaga() {
  yield all([
    takeLatest(menusActions.GET, getMenusByRestaurantId),
  ]);
}

export default rootSaga;
