import { all, call, put, takeLatest } from 'redux-saga/effects';
import Cookies from 'js-cookie';
import { appActions, orderActions } from '../actions';
import * as api from '../api';
import { validateCode } from './order';

export function* init() {
  try {
    const customerId = Cookies.get('lvCustomerId');
    if (customerId) {
      // eslint-disable-next-line no-underscore-dangle
      const { data: me } = yield call(api.getCurrentCustomer);
      yield put(appActions.setMe(me));

      const { data: myActiveOrder } = yield call(api.resolveActiveOrder);
      yield put(appActions.setMyActiveOrder(myActiveOrder));
    } else {
      yield put(appActions.setMyActiveOrder({}));
    }
    yield put(appActions.initSuccess());
  } catch (err) {
    const { response = {} } = err;
    const { status } = response;
    if (status === 404) {
      Cookies.remove('lvCustomerId');
      yield put(appActions.setMe({}));
      yield put(appActions.setMyActiveOrder({}));
      yield put(appActions.initSuccess());
    } else {
      yield put(appActions.initFailure());
    }
  }
}

function* scan({ code }) {
  try {
    const customerId = Cookies.get('lvCustomerId');
    if (!customerId) {
      yield* validateCode({ code });
    } else {
      yield put(orderActions.resolveOrder(code));
    }
    yield put(appActions.scanSuccess());
  } catch (e) {
    yield put(appActions.scanFailure());
  }
}

function* rootSaga() {
  yield all([
    takeLatest(appActions.INIT, init),
    takeLatest(appActions.SCAN, scan),
  ]);
}

export default rootSaga;
