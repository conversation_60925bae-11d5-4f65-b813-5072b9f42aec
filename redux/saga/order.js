import {all, take, race, call, put, takeLatest, delay, select} from 'redux-saga/effects';
import Cookies from 'js-cookie';
import Router from 'next/router';
import isEmpty from '../../src/utilities/isEmpty';
import {appActions, orderActions} from '../actions';
import * as api from '../api';
import { getRestaurantById } from './restaurants';
import { getTableById } from "./table";
import {restaurantsSelectors} from "../selectors";

export function* validateCode({ code }) {
  try {
    const { data: restaurant } = yield call(api.validateOrderCode, code);
    if (!isEmpty(restaurant)) {
      const { slug } = restaurant;
      Router.push(`/restaurant/${slug}/welcome?code=${encodeURIComponent(code)}`, undefined, { shallow: true });
    }
    yield put(orderActions.validateCodeSuccess());
  } catch (e) {
    const { response = {} } = e;
    const { status } = response;
    if (status === 409) {
      yield put(orderActions.scanConfigurationBlocker());
    }
    yield put(orderActions.validateCodeFailure());
  }
}

export function* resolveOrder({ code, customer }) {
  try {
    if (!code) {
      return Router.replace(`/`, undefined, { shallow: true });
    }
    const { data } = yield call(api.resolveOrderByOrderCode, code, customer);
    const { order, customer: fetchedCustomer } = data;
    if (isEmpty(order) || isEmpty(fetchedCustomer)) {
      throw new Error(`order or customer undefined for ${code}`);
    }
    yield put(appActions.setMe(fetchedCustomer));
    yield put(appActions.setMyActiveOrder(order));
    const { id: customerId } = fetchedCustomer;
    const currentCustomerId = Cookies.get('lvCustomerId');
    if ((!currentCustomerId || currentCustomerId !== customerId) && customerId) {
      Cookies.set('lvCustomerId', customerId, { expires: 90000 });
    }
    const { id: orderId, restaurant = {} } = order;
    if (!isEmpty(restaurant)) {
      const { slug } = restaurant;
      Router.replace(`/restaurant/${slug}/order/${orderId}`, undefined, { shallow: true });
    }
    yield put(orderActions.resolveOrderSuccess(order));
  } catch (err) {
    const { response = {} } = err;
    const { status } = response;
    if (status === 409) {
      yield put(orderActions.scanConfigurationBlocker());
    }
    yield put(orderActions.resolveOrderFailure(err));
  }
}

export function* getActiveOrder() {
  try {
    const customerId = Cookies.get('lvCustomerId');
    if (customerId) {
      const { data: myActiveOrder } = yield call(api.resolveActiveOrder);
      yield put(appActions.setMyActiveOrder(myActiveOrder));
    } else {
      yield put(appActions.setMyActiveOrder({}));
    }
  } catch (e) {
    yield put(appActions.setMyActiveOrder({}));
  }
}

const isOrderClosed = (order = {}) => order.status === 'CLOSED';

const isTakeaway = (order = {}) => order.type === 'DELIVERY' || order.type === 'PICKUP';

// eslint-disable-next-line consistent-return
export function* getOrder({ id }) {
  try {
    const { data } = yield call(api.getOrderById, id, true);
    if (data.mergedInto) {
      const { slug } = Router.query;
      yield put(orderActions.getOrderSuccess(data));
      Router.replace(`/restaurant/${slug}/order/${data.mergedInto}`, undefined).then(() => Router.reload())
    }
    yield put(orderActions.getOrderSuccess(data));
    if (isOrderClosed(data)) {
      // this is a consecutive call on closed, which means order is closed after
      // the user opened the current order, which can be made by payment only
      yield put(orderActions.stopPollOrder());
    }
  } catch (e) {
    const { response = {} } = e;
    const { status } = response;
    if (status === 404) {
      // consecutive call to get order
      // if it returns 404, the user is kicked out of the order
      return Router.push('/', undefined, { shallow: true });
    }
    yield put(orderActions.getOrderFailure(e));
  }
}

export function* getOrderCart() {
  try {
    const { restaurant = {} } = yield select(restaurantsSelectors.getRestaurant);
    const { id: restaurantId } = restaurant;
    const { data } = yield call(api.getCurrentOrderCart, restaurantId);
    yield put(orderActions.getOrderCartSuccess(data));
    if (isOrderClosed(data)) {
      // this is a consecutive call on closed, which means order is closed after
      // the user opened the current order, which can be made by payment only
      yield put(orderActions.stopPollOrder());
    }
  } catch (e) {
    const { response = {} } = e;
    const { status } = response;
    if (status === 404) {
      // consecutive call to get order
      // if it returns 404, the user is kicked out of the order
      return Router.push('/', undefined, { shallow: true });
    }
    yield put(orderActions.getOrderCartFailure(e));
  }
}

// eslint-disable-next-line consistent-return
function* getOrderSummary({ id }) {
  try {
    const customerId = Cookies.get('lvCustomerId');
    const pickupOrderId = Cookies.get('lvPickupId');
    if (!customerId && (id !== pickupOrderId)) {
      const { slug } = Router.query;
      // return Router.push(`/restaurant/${slug}/order/${id}/welcome`, undefined, { shallow: true });
      return Router.replace(`/`, undefined, { shallow: true });
    }
    const { data } = yield call(api.getOrderSummaryById, id, true);
    if (data.mergedInto) {
      const { slug } = Router.query;
      yield put(orderActions.getOrderSuccess(data));
      Router.replace(`/restaurant/${slug}/order/${data.mergedInto}`, undefined).then(() => Router.reload())
    }
    yield put(orderActions.getOrderSummarySuccess(data));
  } catch (err) {
    yield put(orderActions.getOrderSummaryFailure(err));
  }
}

// eslint-disable-next-line consistent-return
function* getOrderCartSummary() {
  try {
    const { data } = yield call(api.getCurrentOrderCart, id);
    yield put(orderActions.getOrderSummarySuccess(data));
  } catch (err) {
    yield put(orderActions.getOrderSummaryFailure(err));
  }
}

// eslint-disable-next-line consistent-return
function* getOrderWithData({ id }) {
  try {
    const customerId = Cookies.get('lvCustomerId');
    const pickupOrderId = Cookies.get('lvPickupId');
    if (!customerId && (id !== pickupOrderId)) {
      const { slug } = Router.query;
      // return Router.replace(`/restaurant/${slug}/welcome`, undefined, { shallow: true });
      return Router.replace(`/`, undefined, { shallow: true });
    }
    const { data } = yield call(api.getOrderById, id, true);
    if (data.mergedInto) {
      const { slug } = Router.query;
      yield put(orderActions.getOrderSuccess(data));
      Router.replace(`/restaurant/${slug}/order/${data.mergedInto}`, undefined).then(() => Router.reload())
    }
    if (isOrderClosed(data) && !(isTakeaway(data))) {
      // the first api call to resolve the order, before update polling
      // if resolved order has Closed status, this is an old order and not closed via payment
      return Router.push('/', undefined, { shallow: true });
    }
    yield put(orderActions.getOrderSuccess(data));
    yield* getOrderSummary({ id });
    const { restaurantId, tableId } = data;
    yield* getRestaurantById({ id: restaurantId });
    yield* getTableById({ id: tableId });
  } catch (err) {
    const { response = {} } = err;
    const { status } = response;
    if (status === 404) {
      // the first api call to resolve the order
      // if it returns 404, the user is kicked out of the order
      return Router.push('/', undefined, { shallow: true });
    }
    yield put(orderActions.getOrderFailure(err));
  }
}

// eslint-disable-next-line consistent-return
function* getOrderCartWithData() {
  try {
    const { restaurant = {} } = yield select(restaurantsSelectors.getRestaurant);
    const { id: restaurantId } = restaurant;
    
    //get cart
    const { data } = yield call(api.getCurrentOrderCart, restaurantId);
    yield put(orderActions.getOrderCartSuccess(data));
  } catch (err) {
    const { response = {} } = err;
    const { status } = response;
    yield put(orderActions.getOrderCartFailure(err));
  }
}

function* pollOrder({ id }) {
  yield* getOrderWithData({ id });
  yield delay(4000);
  while (true) {
    try {
      yield* getOrder({ id });
      yield delay(2000);
    } catch (err) {
      yield delay(2000);
      yield put(orderActions.getOrderFailure(err));
    }
  }
}

function* pollOrderCart() {
  yield* getOrderCartWithData();
  yield delay(4000);
  while (true) {
    try {
      yield* getOrderCart();
      yield delay(8000);
    } catch (err) {
      yield delay(8000);
      yield put(orderActions.getOrderCartFailure(err));
    }
  }
}

function* addItem({ orderId, itemData }) {
  try {
    const { data } = yield call(api.addOrderItem, orderId, itemData);
    yield put(orderActions.addItemSuccess(data));
  } catch (err) {
    yield put(orderActions.addItemFailure(err));
  }
}

function* addNestedItem({ orderId, itemId, itemData }) {
  try {
    const { data } = yield call(api.addNestedOrderItem, orderId, itemId, itemData);
    yield put(orderActions.addItemSuccess(data));
    yield put(orderActions.resetItem());
    yield* getOrder({ id: orderId })
    yield* getOrderSummary({ id: orderId })
  } catch (err) {
    const { response = {} } = err;
    const { status } = response;
    if (status === 409) {
      // validation failure, not server
      yield put(orderActions.addNestedItemFailure());
      yield put(appActions.showNotification('notification-order-items-ongoing-round-already-in-progress', 'error'));
    }
    yield put(orderActions.addItemFailure(err));
  }
}

function* removeItem({ id, itemId }) {
  try {
    const { data } = yield call(api.removeOrderItem, id, itemId);
    yield* getOrder({ id });
    yield put(orderActions.removeItemSuccess(data));
  } catch (err) {
    yield put(orderActions.removeItemFailure(err));
  }
}

function* confirmItems({ id }) {
  try {
    yield put(appActions.showNotification('notification-order-items-processing'));
    const { data } = yield call(api.confirmOrderItems, id);
    yield put(orderActions.confirmItemsSuccess(data));
    yield put(appActions.showNotification('notification-order-items-confirmation'));
    yield* getOrderSummary({ id });
  } catch (err) {
    yield put(orderActions.confirmItemsFailure(err));
    yield put(appActions.showNotification('notification-order-items-error', 'error'));
  }
}

function* createPickupBySlug({ slug, orderType }) {
  try {
    const pickupId = Cookies.get('lvPickupId');
    const { data } = yield call(api.createPickupBySlug, slug, pickupId, orderType);
    const { id, restaurantId, customerId } = data;
    if (!isOrderClosed(data)) {
      const inThirtyMinutes = new Date(new Date().getTime() + 30 * 60 * 1000);
      Cookies.set('lvPickupId', id, { expires: inThirtyMinutes });
    }
    if (customerId) {
      Cookies.set('lvCustomerId', customerId, { expires: 90000 });
    }
    yield put(orderActions.createPickupBySlugSuccess(data));
    yield* getRestaurantById({ id: restaurantId });
  } catch (err) {
    const { response = {} } = (err || {});
    const { status } = response;

    if (status === 404) {
      Cookies.remove('lvPickupId');
    }
    if (status === 409) {
      yield call([Router, Router.push], `/restaurant/${slug}`, undefined, { shallow: true });
    }
    yield put(appActions.showNotification(err.response?.data?.title || 'An error occurred', 'error'));

    yield put(orderActions.createPickupBySlugFailure(err));
  }
}

function* addPickupItem({ itemData }) {
  try {
    const pickupId = Cookies.get('lvPickupId');
    const { data } = yield call(api.addOrderPickupItem, pickupId, itemData);
    yield put(orderActions.addPickupItemSuccess(data));
  } catch (err) {
    yield put(orderActions.addPickupItemFailure(err));
  }
}

function* addOrderCartItem({ itemData }) {
  try {
    const { restaurant = {} } = yield select(restaurantsSelectors.getRestaurant);
    const { id: restaurantId } = restaurant;
    const { data } = yield call(api.addOrderCartItem, restaurantId, itemData);
    yield* getOrderCart();
    yield put(orderActions.addPickupItemSuccess(data));
  } catch (err) {
    yield put(orderActions.addPickupItemFailure(err));
  }
}

function* removePickupItem({ id }) {
  try {
    const pickupId = Cookies.get('lvPickupId');
    const { data } = yield call(api.removeOrderPickupItem, pickupId, id);
    yield* getOrder({ id: pickupId });
    yield put(orderActions.removePickupItemSuccess(data));
  } catch (err) {
    yield put(orderActions.removePickupItemFailure(err));
  }
}

function* removeOrderCartItem({ id }) {
  try {
    const { restaurant = {} } = yield select(restaurantsSelectors.getRestaurant);
    const { id: restaurantId } = restaurant;
    const { data } = yield call(api.removeOrderCartItem, restaurantId, id);
    yield* getOrderCart();
    yield put(orderActions.removePickupItemSuccess(data));
  } catch (err) {
    yield put(orderActions.removePickupItemFailure(err));
  }
}

function* setItem({ item = {} }) {
  try {
    const { code } = (item ?? {});
    const { data: menuItem } = yield call(api.getMenuItemByCode, code);
    yield put(orderActions.setItemSuccess(item, menuItem));
  } catch (err) {
    yield put(orderActions.setItemFailure(err));
  }
}

function* pollOrderWatcher() {
  while (true) {
    const action = yield take(orderActions.POLL_ORDER);
    yield race([
      call(pollOrder, action),
      take(orderActions.STOP_POLL_ORDER)
    ]);
  }
}

function* pollOrderCartWatcher() {
  while (true) {
    const action = yield take(orderActions.POLL_ORDER_CART);
    yield race([
      call(pollOrderCart, action),
      take(orderActions.STOP_POLL_ORDER_CART)
    ]);
  }
}

function* pollOrderSummary({ id }) {
  while (true) {
    try {
      const { data: summaryData } = yield call(api.getOrderSummaryById, id, true);
      yield put(orderActions.getOrderSummarySuccess(summaryData));
      yield delay(2000);
    } catch (err) {
      yield delay(4000);
      yield put(orderActions.getOrderSummaryFailure(err));
    }
  }
}

function* pollOrderSummaryWatcher() {
  while (true) {
    const action = yield take(orderActions.POLL_ORDER_SUMMARY);
    yield race([
      call(pollOrderSummary, action),
      take(orderActions.STOP_POLL_ORDER_SUMMARY)
    ]);
  }
}

function* pollOrderCartSummaryWatcher() {
  while (true) {
    const action = yield take(orderActions.POLL_ORDER_CART_SUMMARY);
    yield race([
      call(pollOrderSummary, action),
      take(orderActions.STOP_POLL_ORDER_CART_SUMMARY)
    ]);
  }
}

function* rootSaga() {
  yield all([
    takeLatest(orderActions.VALIDATE_CODE, validateCode),
    takeLatest(orderActions.RESOLVE_ORDER, resolveOrder),
    takeLatest(orderActions.GET_ORDER, getOrderWithData),
    takeLatest(orderActions.GET_ORDER_CART, getOrderCartWithData),
    takeLatest(orderActions.GET_ORDER_FOR_RECEIPT, getOrder),
    takeLatest(orderActions.GET_ORDER_CART_FOR_RECEIPT, getOrderCart),
    takeLatest(orderActions.GET_ACTIVE_ORDER, getActiveOrder),
    takeLatest(orderActions.GET_ORDER_SUMMARY, getOrderSummary),
    takeLatest(orderActions.GET_ORDER_CART_SUMMARY, getOrderCartSummary),
    takeLatest(orderActions.ADD_ITEM, addItem),
    takeLatest(orderActions.ADD_NESTED_ITEM, addNestedItem),
    takeLatest(orderActions.REMOVE_ITEM, removeItem),
    takeLatest(orderActions.CONFIRM_ITEMS, confirmItems),
    takeLatest(orderActions.ADD_PICKUP_ITEM, addPickupItem),
    takeLatest(orderActions.ADD_ORDER_CART_ITEM, addOrderCartItem),
    takeLatest(orderActions.REMOVE_PICKUP_ITEM, removePickupItem),
    takeLatest(orderActions.REMOVE_ORDER_CART_ITEM, removeOrderCartItem),
    takeLatest(orderActions.CREATE_PICKUP_BY_SLUG, createPickupBySlug),
    takeLatest(orderActions.SET_ITEM, setItem),
  ]);
}

export default rootSaga;

export {
  pollOrderWatcher,
  pollOrderCartWatcher,
  pollOrderSummaryWatcher,
  pollOrderCartSummaryWatcher
};
