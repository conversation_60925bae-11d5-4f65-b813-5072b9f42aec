import {createSelector} from "reselect";

const toJs = (s) => (s && typeof s.toJS !== 'undefined' ? s.toJS() : s);

export const getRestaurants = (state) => {
  const { ids, byId } = toJs(state.get('restaurants'));
  return {
    restaurants: ids.map((id) => byId[id])
  };
};

export const getRestaurant = (state) => {
  const restaurant = toJs(state.getIn(['restaurant', 'restaurant']));
  return {
    restaurant
  };
};

export const getRestaurantId = createSelector(
  (state) => state.getIn(['restaurant', 'restaurant', 'id']),
  id => toJs(id)
);

export const getRestaurantCode = createSelector(
  (state) => state.getIn(['restaurant', 'restaurant', 'code']),
  id => toJs(id)
);

export const getRestaurantGallery = (state) => {
  const gallery = toJs(state.getIn(['restaurant', 'gallery']));
  return {
    gallery
  };
};

export const getRestaurantPickupTimes = (state) => {
  const pickupTimes = toJs(state.getIn(['restaurant', 'pickupTimes']));
  return {
    pickupTimes
  };
};

export const getRestaurantSchedulingTimes = (state) => {
  const schedulingTimes = toJs(state.getIn(['restaurant', 'schedulingTimes']));
  return {
    schedulingTimes
  };
};

export const getRestaurantLogoUrl = createSelector(
  (state) => state.getIn(['restaurant', 'restaurant', 'logoUrl']),
  (logoUrl) => toJs(logoUrl)
);
