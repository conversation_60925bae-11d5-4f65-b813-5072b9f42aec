import {createSelector} from "reselect";

const toJs = (s) => (s && typeof s.toJS !== 'undefined' ? s.toJS() : s);

// eslint-disable-next-line import/prefer-default-export
export const getTable = (state) => {
  const table = toJs(state.getIn(['table', 'table'])) || {};
  return {
    table
  };
};

export const getTableCode = createSelector(
  (state) => state.getIn(['table', 'table', 'code']),
  code => code
);
