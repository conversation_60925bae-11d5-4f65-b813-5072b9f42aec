const toJs = (s) => (s && typeof s.toJS !== 'undefined' ? s.toJS() : s);

export const getNotification = (state) => {
  const notification = toJs(state.getIn(['ui', 'notification']));
  return {
    ...notification
  };
};

export const getMe = (state) => {
  const me = toJs(state.getIn(['ui', 'me']));
  return {
    ...me
  };
};

export const getMyActiveOrder = (state) => {
  const myActiveOrder = toJs(state.getIn(['ui', 'myActiveOrder']));
  return {
    ...myActiveOrder
  };
};

export const getScan = (state) => {
  const scan = toJs(state.getIn(['ui', 'scan']));
  return {
    ...scan
  };
};

export const getWelcome = (state) => {
  const welcome = toJs(state.getIn(['ui', 'welcome']));
  return {
    ...welcome
  };
};

export const getCurrentOrder = (state) => {
  const currentOrder = toJs(state.getIn(['ui', 'currentOrder']));
  return {
    ...currentOrder
  };
};
