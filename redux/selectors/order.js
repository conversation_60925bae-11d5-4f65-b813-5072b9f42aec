const toJs = (s) => (s && typeof s.toJS !== 'undefined' ? s.toJS() : s);

export const getOrder = (state) => {
  const order = toJs(state.getIn(['order', 'order']));
  return {
    order
  };
};

export const getOrderSummary = (state) => {
  const summary = toJs(state.getIn(['order', 'summary']));
  return {
    summary
  };
};

export const getParticipants = (state) => {
  const participants = toJs(state.getIn(['order', 'order', 'participants']));
  return {
    participants
  };
};

export const getParticipant = (state) => {
  const participant = toJs(state.getIn(['order', 'participant']));
  return {
    participant
  };
};

export const getCustomer = (state) => {
  const customer = toJs(state.getIn(['order', 'customer']));
  return {
    customer
  };
};

export const getUnconfirmed = (state) => toJs(state.getIn(['order', 'items', 'unconfirmed']));

export const getConfirmed = (state) => toJs(state.getIn(['order', 'items', 'confirmed']));

export const getOngoing = (state) => toJs(state.getIn(['order', 'items', 'ongoing']));

export const getItemDetails = (state) => {
  const { item, menuItem } = toJs(state.getIn(['order', 'details']));
  return {
    item,
    menuItem
  };
};

export const getDiscount = (state) => {
  const discount = toJs(state.getIn(['order', 'discount']));
  return {
    discount
  };
};

export const getShowOrderingBlocker = (state) => {
  return state.getIn(['order', 'showOrderingBlocker'])
};
