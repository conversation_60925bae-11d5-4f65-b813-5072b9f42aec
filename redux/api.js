import axios from 'axios';
import Cookies from 'js-cookie';
// eslint-disable-next-line import/named
import { i18n } from '../i18n';

const isProd = process.env.NODE_ENV === 'production';
const basePath = isProd ? '' : 'https://preview.leviee.de';
const restaurantBasePath = isProd ? '' : 'https://preview.leviee.de';

axios.interceptors.request.use((config) => {
  const lvClientToken = Cookies.get('lvClientToken');
  const language = Cookies.get('next-i18next');
  const lvCustomerId = Cookies.get('lvCustomerId');
  config.headers.common['Authorization'] = `Bearer ${lvClientToken}`;
  config.headers.common['customer'] = lvCustomerId || '';
  config.headers.common['content-language'] = language || i18n.language;
  return config;
}, (error) => Promise.reject(error));


axios.interceptors.response.use(null, (error) => {
  const originalRequest = error.config;
  const errorStatus = error.response ? error.response.status : null
  const isUnauthorized = errorStatus === 401;
  const isRetryLoop = originalRequest._retry

  if (isUnauthorized && !isRetryLoop) {
    originalRequest._retry = true;
    return axios.get('/validate-access', {})
      .then(() => {
        const lvClientToken = Cookies.get('lvClientToken');
        axios.defaults.headers.common['Authorization'] = `Bearer ${lvClientToken}`;
        originalRequest.headers['Authorization'] = `Bearer ${lvClientToken}`;
        return axios.request(originalRequest);
      })
      .catch(() => Promise.reject(error))
  }

  return Promise.reject(error)
})

const restaurantService = (path) => `${restaurantBasePath}/restaurant-api${path}`;
const marketingService = (path) => `${basePath}/marketing-service${path}`;
const reservationService = (path) => `${basePath}/reservation-service${path}`;
const uiService = (path) => `${basePath}/ui-service${path}`;
const orderService = (path) => `${basePath}/gluttony-api/api${path}`;
const orderCartsService = (path) => `${basePath}/gluttony-api${path}`;
const customerService = (path) => `${basePath}/gluttony-api/customers${path}`;
const businessService = (path) => `${basePath}/gluttony-api/ui-service${path}`;

export const getRestaurants = () => axios.get(restaurantService('/business/restaurants'), {});

export const getRestaurantById = (id, orderType) => {
    const basePath = ['PICKUP', 'DELIVERY', undefined].includes(orderType) ? 'webshop' : 'business';
    return axios.get(restaurantService(`/${basePath}/restaurants/${id}${orderType ? `?orderType=${orderType}` : ''}`));
};

export const getRestaurantBySlug = (slug, orderType) => {
    const basePath = ['PICKUP', 'DELIVERY', undefined].includes(orderType) ? 'webshop' : 'business';
    return axios.get(restaurantService(`/${basePath}/restaurants/slug/${slug}${orderType ? `?orderType=${orderType}` : ''}`));
};

export const getRestaurantConfiguration = (id) => axios.get(restaurantService(`/restaurants/${id}/external-config`), {});

export const getRestaurantPickupTimes = (id) => axios.get(restaurantService(`/restaurants/${id}/pickupTimes`), {});

export const getSchedulingTimes = (id) => axios.get(restaurantService(`/restaurants/${id}/scheduledPickupTimes`), {});

export const getWebshopHours = (restaurantId) => axios.get(restaurantService(`/restaurants/${restaurantId}/webshopHours`), {});

export const getReservableTimes = (id, cancelToken, people, date) => axios.get(reservationService(`/v1/restaurants/${id}/customer/time-suggestions`), { cancelToken, params: { people, day: date } });

export const getReservation = (restaurantId, reservationId, customerToken) => axios.post(uiService(`/graphql`),
  {
    query: `
      query {
        getReservation(request: {
          restaurantId: "${restaurantId}",
          id: "${reservationId}",
          customerToken: "${customerToken}"
        }) {
          id
          people
          startTime
          endTime
          formattedCustomerStartLocalTime
          note
          status
          customers {
            id
            firstName
            lastName
            fullName
            phone
            email
          }
        }
      }
    `,
  },
  {});

// OLD
// export const createReservation = (restaurantId, reservation, customers) => axios.post(marketingService(`/restaurants/${restaurantId}/customer-reservations`), { reservation, customers }, {} );
// New endpoint of the above
export const createReservation = (restaurantId, reservation, customers) => axios.post(reservationService(`/v1/restaurants/${restaurantId}/customer/reservations`), { reservation, customers }, {} );

export const cancelReservation = (restaurantId, reservationId, customerToken) => axios.delete(marketingService(`/restaurants/${restaurantId}/customer-reservations/${reservationId}`), { params: { customerToken } } );

export const getReservationConfig = (restaurantId) => axios.get(marketingService(`/restaurants/${restaurantId}/customer-reservation-config`), {});

export const updateRestaurantPickupRequests = (id) => axios.post(restaurantService(`/restaurants/${id}/pickupRequests`), {}, {});

export const getMenuItemByCode = (code) => axios.get(restaurantService(`/restaurants/menus/items/code/${code}?requester=client`), {});

/**
 * Order APIs
 */

export const getOrderById = (id, includeDiscount) => axios.get(orderService(`/orders/${id}?includeDiscount=${includeDiscount}`));

export const getCurrentOrderCart = (restaurantId) => axios.get(orderCartsService(`/restaurants/${restaurantId}/order-carts/current?cartId=`));

export const createOrderCart = (restaurantId, customer, table) => axios.post(orderCartsService(`/restaurants/${restaurantId}/order-carts/_generate-new-cart`), { customer, table }, {});

export const getOrderSummaryById = (id, includeDiscount) => axios.get(orderService(`/orders/${id}/currentReceipt?includeDiscount=${includeDiscount}`));

export const resolveOrder = (code, customer) => axios.post(businessService('/scan/resolveOrder'), { code, customer }, {});

export const createOrderByCode = (code) => axios.post(orderService('/orders'), { code }, {});

export const createPickupBySlug = (slug, orderId, orderType = 'PICKUP') => axios.post(orderService('/orders/pickup'), { restaurantSlug: slug, id: orderId, type: orderType }, {});

export const updateTakeawayOrderType = (id, type) => axios.put(orderService(`/orders/${id}/takeaway/_updateType`), { type });

export const patchOrder = (id, data) => axios.put(orderService(`/orders/${id}`), data);

export const createCustomerForOrder = (id, data) => axios.post(orderService(`/orders/${id}/participants`), data, {});

export const approveParticipant = (id, participantId) => axios.post(orderService(`/orders/${id}/participants/${participantId}/approve`), {}, {});

export const deleteParticipant = (id, participantId) => axios.delete(orderService(`/orders/${id}/participants/${participantId}`), {});

export const notifyWaiterForParticipantVerification = (id) => axios.post(orderService(`/orders/${id}/participants/verification/notify`), {});

export const getRestaurantMenus = (id) => axios.get(restaurantService(`/business/restaurants/${id}/client/menus`), {});

export const addOrderItem = (id, data) => axios.post(orderService(`/orders/${id}/items`), data);

export const addNestedOrderItem = (id, itemId, data) => axios.post(orderService(`/orders/${id}/items/${itemId}`), data);

export const removeOrderItem = (id, itemId) => axios.delete(orderService(`/orders/${id}/items/${itemId}`));

export const confirmOrderItems = (id) => axios.post(orderService(`/orders/${id}/items/confirm`), {});

export const addOrderPickupItem = (id, data) => axios.post(orderService(`/orders/${id}/pickup/items`), data);

export const addOrderCartItem = (restaurantId, data) => axios.post(orderCartsService(`/restaurants/${restaurantId}/order-carts/current/items?cartId=`), data);

export const removeOrderPickupItem = (id, itemId) => axios.delete(orderService(`/orders/${id}/pickup/items/${itemId}`));

export const removeOrderCartItem = (restaurantId, itemId) => axios.delete(orderCartsService(`/restaurants/${restaurantId}/order-carts/current/items/${itemId}?cartId=`));

export const requestOrderCartPayment = (restaurantId, data) => axios.post(orderCartsService(`/restaurants/${restaurantId}/order-carts/current/_request-payment`), data, {});

export const updatePickup = (id, customer, type, status = null, takeawayDate, pickupTime, notes, pendingCardCharges) => axios.put(orderService(`/orders/${id}/pickup`), { customer, type, status, takeawayDate, pickupTime, notes, pendingCardCharges }, {});

export const payPickup = (id) => axios.put(orderService(`/orders/${id}/pickup/pay`), {}, {});

export const getCheckout = (id) => axios.get(orderService(`/orders/${id}/checkout`), {});

export const completeCheckout = (id, checkoutSettings) => axios.post(orderService(`/orders/${id}/pay`), { ...checkoutSettings }, {});

export const getTableById = (id) => axios.get(restaurantService(`/tables/${id}`), {})

/**
 * Customer APIs
 */

export const getCurrentCustomer = () => axios.get(customerService('/me'), {});

export const createCustomer = (customer) => axios.post(customerService('/'), { customer }, {});

/**
 * Business layer API
 */

export const resolveActiveOrder = () => axios.get(businessService('/order/active'));

export const validateOrderCode = (code) => axios.post(businessService(`/order/code/${encodeURIComponent(code)}/validate`), {}, {});

export const resolveOrderByOrderCode = (code, customer) => axios.post(businessService(`/order/code/${encodeURIComponent(code)}`), { ...customer }, {});

export const createCheckoutSession = (orderId, customerId) => axios.post(businessService('/checkout/session'), { id: orderId, customerId }, {});

export const getStripeConfiguration = (restaurantId) => axios.get(orderService(`/restaurants/${restaurantId}/_stripe_configuration`), {});


export const getDynamicCode = (code) => axios.get(restaurantService(`/dynamic-codes/${code}`), {})

export const generateDigitalReceipt = (code, customer, receiptPurpose, receiptParticipants) => axios.post(orderCartsService('/customer-digital-receipts'), { code, customer, receiptPurpose, receiptParticipants }, {});

export const getDigitalReceiptsInfo = (code) => axios.get(orderCartsService(`/digital-receipts/${code}`), {});

// Pay
export const getPaymentLinkData = (orderId) => axios.get(orderCartsService(`/orders/${orderId}/payment-link-metadata`), {})

export const submitPaymentLinkData = (orderId, tipAmount) => axios.post(orderCartsService(`/orders/${orderId}/payment-links`), { tipAmount }, {})

// Side Screen
export const getGuestMonitorData = (monitorId) => axios.get(restaurantService(`/guest-monitors/${monitorId}`), {})

/**
 * Gift Card carts
 */

export const getCurrentCardCart = (restaurantId) => axios.get(marketingService(`/restaurants/${restaurantId}/card-carts/current`));

export const createCardCart = (restaurantId, customer, amount) => axios.post(marketingService(`/restaurants/${restaurantId}/card-carts/_generate-new-cart`), { customer, amount }, {});

export const updateCardCart = (restaurantId, amount) => axios.put(marketingService(`/restaurants/${restaurantId}/card-carts/current`), { amount }, {});

export const payCardCart = (restaurantId) => axios.post(marketingService(`/restaurants/${restaurantId}/card-carts/current/_request-payment`), {}, {});

export const getDiscountConfig = (restaurantId) => axios.post(uiService(`/graphql`),
  {
    query: `
      query {
        getRestaurantConfig(id: "${restaurantId}") {
          webshopConfig {
            pickupDiscountPercentage
            promotionText
          }
        }
      }
    `,
  },
  {});

export const getDeliveryInfo = (restaurantId, address = {}) => axios.post(orderCartsService(`/restaurants/${restaurantId}/delivery-info`), { address }, {});

/**
 * Gift Cards
 */

export const getGiftCardByCode = (restaurantId, code) => axios.get(marketingService('/cards/card/_resolve'), { params: { restaurantId: restaurantId, code, externalCode: code } });

/**
 * Promotions
 */

export const addPromotionToCart = (restaurantId, promoCode) => axios.post(orderCartsService(`/restaurants/${restaurantId}/order-carts/current/coupons?cartId=`), {
  promoCode
}, {});

export const removePromoCodeFromCart = (restaurantId) => axios.delete(orderCartsService(`/restaurants/${restaurantId}/order-carts/current/coupons`));

export const getReservationV2Flag = (slug) => axios.get(reservationService(`/v1/restaurants/slug/${slug}/enabled`), {});