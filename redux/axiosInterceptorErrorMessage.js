import {useDispatch} from "react-redux";
import { useEffect } from "react";
import axios from "axios";
import { appActions } from "../redux/actions";

const AxiosInterceptor = () => {
  const dispatch = useDispatch();

  useEffect(() => {
    const interceptor = axios.interceptors.response.use(
      (response) => response,
      (error) => {
        if (error && error.response) {
          const errorMessage = error.response?.data?.localizedMessage ?? null;
          if (errorMessage) {
            dispatch(appActions.showNotification(errorMessage, 'error'));
          } else {
            // if no 'localizedMessage' from backend -> whatever is set in .catch will handle error
          }
        }
        return Promise.reject(error);
      }
    );

    return () => {
      axios.interceptors.response.eject(interceptor);
    };
  }, [dispatch]);

  return null;
};

export default AxiosInterceptor;