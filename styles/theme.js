import { createMuiTheme } from '@material-ui/core/styles';
import { fade } from '@material-ui/core';
import palette from "./palette";
import shadows from "./shadows";
import typography from "./typography";

export const appBarHeight = 44;
export const drawer = 348;
export const bottomNavigation = 56;
export const tabs = 49;
export const offset = 56;

export const colors = {
  leviee: {
    main: {
      green: '#FF7C5C', //'#00848A',
      dark: '#04172F',
      white: '#FFFFFF'
    },
    secondary: {
      wine: '#B10744',
      red: '#FE4C56',
      brown: '#D0772A',
      orange: '#FE754B',
      yellow: '#FFAE33',
      lightYellow: '#FFC475',
      beige: '#FFDFB0',
      blue: '#4696F7',
      midGreen: '#209571',
      darkGreen: '#236B55',
    },
    greyscale: {
      darkGray: '#435163',
      midGray: '#919CA9',
      gray: '#CCD1D7',
      lightGray: '#E0E3E7',
      lighterGray: '#F0F1F3',
      lightestGray: '#F7F8F9',
    }
  },
};

export const severityColors = {
  success: colors.leviee.main.green,
  error: colors.leviee.secondary.red
};

export const fontStyles = {
  pageTitle: {
    fontFamily: 'Recoleta',
    fontSize: 50,
    fontWeight: 700,
    lineHeight: '60px',
    letterSpacing: '0.3px'
  },
  sectionTitle: {
    fontFamily: 'Recoleta',
    fontSize: 46,
    fontWeight: 700,
    lineHeight: '54px',
    letterSpacing: '0.3px'
  },
  header: {
    fontFamily: 'Recoleta',
    fontSize: 29,
    fontWeight: 700,
    lineHeight: '30px',
    letterSpacing: '-0.6px'
  },
  displayTitle: {
    fontSize: 24,
    fontWeight: 600,
    lineHeight: '30px',
    letterSpacing: '0px'
  },
  titleRegular: {
    fontSize: 19,
    fontWeight: 400,
    lineHeight: '24px',
    letterSpacing: '0px'
  },
  titleMedium: {
    fontSize: 19,
    fontWeight: 500,
    lineHeight: '24px',
    letterSpacing: '0px'
  },
  secondaryTitleRegular: {
    fontSize: 17,
    fontWeight: 400,
    lineHeight: '21px',
    letterSpacing: '0px'
  },
  paragraphRegular: {
    fontSize: 15,
    fontWeight: 400,
    lineHeight: '20px',
    letterSpacing: '-0.012em'
  },
  paragraphMedium: {
    fontSize: 15,
    fontWeight: 500,
    lineHeight: '20px',
    letterSpacing: '-0.012em'
  },
  smallParagraphRegular: {
    fontSize: 13,
    fontWeight: 400,
    lineHeight: '16px',
    letterSpacing: '-0.005em'
  },
  smallParagraphMedium: {
    fontSize: 13,
    fontWeight: 500,
    lineHeight: '16px',
    letterSpacing: '-0.005em'
  }
};

export const containerStyles = {
  displayTitleContainer: {
    paddingTop: 6,
    paddingBottom: 6,
    marginTop: 4,
    marginBottom: 4
  },
  titleMediumContainer: {
    paddingTop: 6,
    paddingBottom: 6,
    marginTop: 4 + 14,
    marginBottom: 4
  },
  titleMediumWithDividerContainer: {
    paddingTop: 6,
    paddingBottom: 6,
    marginTop: 12,
    marginBottom: 4
  },
  titleMediumWithTextContainer: {
    paddingTop: 6,
    paddingBottom: 6,
    marginTop: 4,
    marginBottom: 4
  },
  titleMediumDivider: {
    marginTop: 24,
    borderBottom: `1px solid ${fade(colors.leviee.main.dark, 0.06)}`
  },
  titleMediumWithTextDivider: {
    marginTop: 32,
    borderBottom: `1px solid ${fade(colors.leviee.main.dark, 0.06)}`
  },
  secondaryTitleContainer: {
    paddingTop: 6,
    paddingBottom: 6,
    marginTop: 24
  },
  buttonContainer: {
    padding: 12
  },
  buttonContainerWithMargin: {
    padding: 12,
    marginTop: 24
  },
  textContainer: {
    paddingTop: 6,
    paddingBottom: 6,
    marginTop: 4,
    marginBottom: 4
  }
};

export const drawerModalStyle = {
  position: 'fixed',
  top: 0,
  left: 0,
  right: 0,
  bottom: 0,
  zIndex: 1500,
  overscrollBehaviorY: 'contain',
  inset: 0,
  display: 'flex',
  justifyContent: 'center',
  flexShrink: 0,
  alignItems: 'center',
  transition: 'background-color 0.2s linear 0s',
  overflowY: 'auto',
};

const theme = createMuiTheme({
  palette: {
    common: {
      white: colors.leviee.main.white,
      black: colors.leviee.main.dark
    },
    primary: {
      main: colors.leviee.main.green,
    },
    text: {
      primary: colors.leviee.main.dark
    },
    contrastThreshold: 3
  },
  typography: {
    fontFamily: [
      'Inter',
      'Nunito Sans',
      '-apple-system',
      'system-ui',
      'BlinkMacSystemFont',
      '"Segoe UI"',
      'Roboto',
      '"Helvetica Neue"',
      'Arial',
      'sans-serif',
      '"Apple Color Emoji"',
      '"Segoe UI Emoji"',
      '"Segoe UI Symbol"',
    ].join(','),
    h1: {
      fontSize: '3.6rem',
      lineHeight: '56px',
      fontWeight: 700,
      fontFamily: 'Lato'
    },
    h2: {
      fontSize: '25px',
      lineHeight: '30px',
      fontWeight: 700,
      fontFamily: 'Lato'
    },
    body1: {
      fontSize: '1.1rem',
      lineHeight: '28px'
    },
    body2: {
      fontSize: 13
    }
  },
  props: {
    MuiButtonBase: {
      disableRipple: false,
    },
  },
  mixins: {
    toolbar: {
      minHeight: 48,
      '@media (min-width: 600px)': {
        minHeight: 64
      },
    },
  },
  spacing: 6
});

const withOverrides = {
  ...theme,
  overrides: {
    MuiCssBaseline: {
      '@global': {
        html: {
          WebkitFontSmoothing: 'auto',
          userSelect: 'none',
          '-webkit-tap-highlight-color': 'transparent',
          textRendering: 'optimizeLegibility !important',
          '-webkit-font-smoothing': 'antialiased !important',
          '-moz-osx-font-smoothing': 'grayscale'
        },
        body: {
          backgroundColor: palette.grayscale["200"],
          touchAction: 'pan-x pan-y'
        },
        a: {
          textDecoration: 'none',
          color: 'inherit'
        },
        '.osano-cm-widget': {
          display: 'none'
        },
        '.osano-cm-dialog': {
          boxShadow: '0px -1px 3px -1px rgba(4, 23, 47, 0.12)',
          padding: 32,
          background: palette.grayscale["800"],
          color: palette.grayscale["100"],
          '@media (max-width:600px)': {
            padding: 16
          }
        },
        '.osano-cm-dialog__close.osano-cm-close': {
          color: palette.grayscale["100"],
          stroke: palette.grayscale["100"]
        },
        '.osano-cm-dialog--type_bar .osano-cm-button': {
          width: "28%"
        },
        '.osano-cm-message': {
          ...typography.body.regular,
          color: palette.grayscale["100"]
        },
        '.osano-cm-link': {
          ...typography.body.regular,
          color: palette.grayscale["100"]
        },
        '.osano-cm-buttons': {
          // display: 'flex',
          // flexDirection: 'column-reverse',
        },
        '.osano-cm-buttons__button.osano-cm-button': {
          background: colors.leviee.main.green,
          maxWidth: 120,
          minHeight: 40,
          borderRadius: 10
        },
        '.osano-cm-toggle': {
          ...fontStyles.paragraphRegular
        },
        '.osano-cm-buttons__button.osano-cm-save': {
          background: fade(colors.leviee.main.green, 0.5)
        }
      }
    },
    MuiTabs: {
      flexContainerVertical: {
        '@media (min-width:901px)': {
          alignItems: "flex-start"
        }
      },
      scroller: {
        width: "100%",
        paddingTop: 8
      }
    },
    MuiTab: {
      root: {
        backgroundColor: "transparent",
        textTransform: 'none',
        // margin: '6px auto',
        minHeight: 32,
        ...typography.body.medium,
        color: "#757574",
        '&$selected': {
          background: palette.grayscale["300"],
          color: palette.grayscale["800"]
        },
        '&&': {
          minWidth: 'auto',
        },
        "&+&": {
          '@media (max-width:900px)': {
            marginLeft: 8
          },
          marginLeft: 8
          // '@media (min-width:901px)': {
          //   marginTop: 8
          // }
        },
        paddingLeft: 10,
        paddingRight: 10,
        borderRadius: 12
      },
      textColorInherit: {
        color: "#757574",
        opacity: 1
      }
    },
    MuiPickersStaticWrapper: {
      root: {
        backgroundColor: palette.grayscale["100"],
      }
    },
    MuiPickersDesktopDateRangeCalendar: {
      root: {
        justifyContent: "center"
      },
      arrowSwitcher: {
        paddingLeft: 0,
        paddingRight: 0
      },
      calendar: {
        minHeight: 280
      }
    },
    MuiPickersDay: {
      root: {
        color: "#333332",
        ...typography.body.regular,
        "&:focus": {
          "&$selected": {
            backgroundColor: '#FF7C5C',
            color: palette.grayscale["100"]
          },
        },
        "&$selected": {
          color: palette.grayscale["100"],
          backgroundColor: '#FF7C5C',
          "&:hover": {
            backgroundColor: '#FF7C5C',
            color: palette.grayscale["100"]
          },
          "&:focus": {
            backgroundColor: '#FF7C5C',
            color: palette.grayscale["100"]
          }
        },
      },
    },
    MuiPickersDateRangeDay: {
      root: {
        width: 44,
        margin: '0 2px',
        justifyContent: "center"
      },
      rangeIntervalDayHighlight: {
        backgroundColor: "transparent", //"#FDE7D6"
      },
      dayInsideRangeInterval: {
        color: "#333332",
      },
      rangeIntervalPreview: {
        border: "none",
        display: "flex",
        justifyContent: "center"
      },
      rangeIntervalDayPreview: {
        border: "none"
      },
      selected: {
        color: palette.grayscale["100"]
      }
    },
    MuiPickersCalendar: {
      week: {
        justifyContent: "center",
        height: 44
      },
      weekDayLabel: {
        width: 44,
        ...typography.extraSmall.medium,
        color: palette.grayscale["600"],
      }
    },
    MuiPickersArrowSwitcher: {
      iconButton: {
        backgroundColor: "transparent"
      }
    
    },
    PrivateTabIndicator: {
      root: {
        display: 'none'
      }
    },
    MuiButton: {
      root: {
        fontSize: 16,
        lineHeight: '28px',
        padding: '8px 16px',
        textTransform: 'none',
        whiteSpace: 'nowrap',
        borderRadius: 12
      },
      textSizeSmall: {
        fontSize: 14,
        paddingLeft: theme.spacing(1),
        paddingRight: theme.spacing(1)
      },
      containedSizeSmall: {
        fontSize: 14
      },
      outlinedSizeSmall: {
        borderRadius: 12,
        border: "1px solid #D9D9D8"
      }
    },
    MuiContainer: {
      maxWidthLg: {
        maxWidth: 1140,
        '@media (min-width:1280px)': {
          maxWidth: 1140,
          // boxSizing: "content-box"
        }
      }
    },
    MuiSkeleton: {
      root: {
        backgroundColor: palette.grayscale["300"]
      }
    },
    MuiInputBase: {
      root: {
        width: '100%'
      }
    },
    MuiInput: {
      underline: {
        '&::before': {
          borderBottom: '1px solid #f3f4f4'
        },
        '&::after': {
          borderBottom: '2px solid #f3f4f4'
        }
      }
    },
    MuiFormLabel: {
      root: {
        '&$focused': {
          color: palette.grayscale["800"]
        }
      }
    }
  },
};

export default withOverrides;
