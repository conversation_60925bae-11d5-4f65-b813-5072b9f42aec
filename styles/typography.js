/**
  All Rights Reserved
  Copyright (c) 2022 allO (Leviee GmbH)
  Created by <PERSON><PERSON><PERSON>

  THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
  IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
  FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
  AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
  LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
  OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
  THE SOFTWARE.
 */
import palette from "./palette";

export default {
  large: {
    regular: {
      fontFamily: "Inter",
      fontWeight: "400",
      fontStyle: "normal",
      fontSize: 20,
      lineHeight: "24px",
      color: palette.grayscale["800"],
    },
    semiBold: {
      fontFamily: "Inter",
      fontWeight: "600",
      fontStyle: "normal",
      fontSize: 20,
      lineHeight: "24px",
      color: palette.grayscale["800"],
    },
  },
  medium: {
    regular: {
      fontFamily: "Inter",
      fontWeight: "400",
      fontStyle: "normal",
      fontSize: 16,
      lineHeight: "22px",
      letterSpacing: -0.0016,
      color: palette.grayscale["800"],
    },
    medium: {
      fontFamily: "Inter",
      fontWeight: "500",
      fontStyle: "normal",
      fontSize: 16,
      lineHeight: "22px",
      color: palette.grayscale["800"],
    },
    semiBold: {
      fontFamily: "Inter",
      fontWeight: "600",
      fontStyle: "normal",
      fontSize: 16,
      lineHeight: "22px",
      color: palette.grayscale["800"],
    },
  },
  email: {
    large: {
      fontFamily: "Inter",
      fontWeight: "600",
      fontStyle: "normal",
      fontSize: 28,
      lineHeight: "36px",
      color: palette.grayscale["800"]
    },
    medium: {
      fontFamily: "Inter",
      fontWeight: "500",
      fontStyle: "normal",
      fontSize: 18,
      lineHeight: "26px",
      letterSpacing: -0.0014,
      color: palette.grayscale["800"],
    },
    regular: {
      fontFamily: "Inter",
      fontWeight: "400",
      fontStyle: "normal",
      fontSize: 18,
      lineHeight: "26px",
      letterSpacing: -0.0014,
      color: palette.grayscale["800"],
    },
    extraSmall: {
      medium: {
        fontFamily: "Inter",
        fontWeight: "500",
        fontStyle: "normal",
        fontSize: 14,
        lineHeight: "20px",
        letterSpacing: 0.02,
        textTransform: "uppercase",
        color: palette.grayscale["800"],
      },
    },
  },
  body: {
    regular: {
      fontFamily: "Inter",
      fontWeight: "400",
      fontStyle: "normal",
      fontSize: 14,
      lineHeight: "20px",
      letterSpacing: -0.0014,
      color: palette.grayscale["800"],
    },
    medium: {
      fontFamily: "Inter",
      fontWeight: "500",
      fontStyle: "normal",
      fontSize: 14,
      lineHeight: "20px",
      letterSpacing: -0.0014,
      color: palette.grayscale["800"],
    },
  },
  small: {
    regular: {
      fontFamily: "Inter",
      fontWeight: "400",
      fontStyle: "normal",
      fontSize: 12,
      lineHeight: "16px",
      letterSpacing: -0.012,
      color: palette.grayscale["800"],
    },
    medium: {
      fontFamily: "Inter",
      fontWeight: "500",
      fontStyle: "normal",
      fontSize: 12,
      lineHeight: "16px",
      letterSpacing: -0.012,
      color: palette.grayscale["800"],
    },
  },
  extraSmall: {
    medium: {
      fontFamily: "Inter",
      fontWeight: "500",
      fontStyle: "normal",
      fontSize: 11,
      lineHeight: "16px",
      letterSpacing: 0.02,
      textTransform: "uppercase",
      color: palette.grayscale["800"],
    },
  },
  mini: {
    medium: {
      fontFamily: "Inter",
      fontWeight: "500",
      fontStyle: "normal",
      fontSize: 8,
      lineHeight: "12px",
      letterSpacing: 0.02,
      textTransform: "uppercase",
      color: palette.grayscale["800"],
    },
  },
  x: {
    paymentAmount: {
      fontFamily: "Inter",
      fontWeight: "500",
      fontStyle: "normal",
      fontSize: 40,
      lineHeight: "40px",
      letterSpacing: "-0.14%",
      textTransform: "uppercase",
      color: palette.grayscale["800"],
    }
  },
};
