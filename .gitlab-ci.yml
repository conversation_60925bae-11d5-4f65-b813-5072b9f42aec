variables:
  DEFAULT_IMAGE: "europe-west3-docker.pkg.dev/iac-dev-432418/registry-docker-io/library/docker:27.3.1-git"
  DEFAULT_SERVICE_DIND_IMAGE: "europe-west3-docker.pkg.dev/iac-dev-432418/registry-docker-io/library/docker:27.3.1-dind"
  AUTH_IMAGE: "europe-west3-docker.pkg.dev/iac-dev-432418/gcr-io/google.com/cloudsdktool/cloud-sdk"
  #BUILD_IMAGE: "europe-west3-docker.pkg.dev/iac-dev-432418/allo-docker-public/allo-pipeline:node20_gcloud_503"
  BUILD_IMAGE: "europe-west3-docker.pkg.dev/iac-dev-432418/allo-docker-public/allo-pipeline:node18_gcloud_485"
  BUILD_DOCKER_FILE: Dockerfile.build-node20-corepack
  RUNTIME_IMAGE: "europe-docker.pkg.dev/endless-gizmo-264508/allo-docker/allo/nginx:1.27.3-debian-vts"
  RUNTIME_DOCKER_FILE: Dockerfile.runtime
  PUBLISH_CONTAINER_IMAGE: "europe-west3-docker.pkg.dev/iac-dev-432418/gcr-io/kaniko-project/executor:v1.23.2-debug"
  DEPLOY_KUBERNETES_IMAGE: "europe-west3-docker.pkg.dev/iac-dev-432418/gcr-io/google.com/cloudsdktool/cloud-sdk"
  E2E_IMAGE: "europe-west3-docker.pkg.dev/iac-dev-432418/mcr-microsoft-com/playwright:v1.50.0-noble"
  #START_COMMAND: "node_modules/.bin/next start"
  START_COMMAND: "node server/app.js"
  NODEJS_VERSION: "18.20.7"
  ALPINE_VERSION: "3.20"
  TEST__NEW_RELIC_APP_ID: "0"
  PROD__NEW_RELIC_APP_ID: "0"

  
stages:
  - preprepare
  - prepare
  - build
  - publish
  - deploy
  - e2e-test
  - rollback

include:
  - local: .src/main/gitlab-ci/gitlab-ci-authenticate-gcloud.yml
  - local: .src/main/gitlab-ci/gitlab-ci-authenticate-gcloud-gke.yml
  - local: .src/main/gitlab-ci/gitlab-ci-print-info.yml
  - local: .src/main/gitlab-ci/gitlab-ci-print-timestamp-logging.yml
  - local: .src/main/gitlab-ci/gitlab-********************yaml.yml
  - local: .src/main/gitlab-ci/gitlab-ci-cache-npm.yml
  - local: .src/main/gitlab-ci/gitlab-ci-cache-yarn.yml

image: $DEFAULT_IMAGE
services:
  - $DEFAULT_SERVICE_DIND_IMAGE

# Set default config for jobs
# see https://docs.gitlab.com/ee/ci/yaml/#default
default:
  retry:
    max: 2
    # specify retry on certain conditions
    # see https://docs.gitlab.com/ee/ci/yaml/index.html#retrywhen
    when:
      - runner_system_failure
      - stuck_or_timeout_failure

.cache_keys: &cache_keys
  - yarn.lock

.cache_pathes: &cache_pathes
  - .build/
  - .yarn/
  - node_modules/
  - prod_node_modules/
  - package-lock.json
  - package-lock.json.sha256sum
  - yarn.lock
  - yarn.lock.sha256sum

.before_pkg: &before_pkg
  - |
    while (! docker stats --no-stream >/dev/null 2>&1); do
      sleep 2
    done

##########################################################
# 
# PREPREPARE
#
##########################################################
preprepare-authenticate-container-registry:
  stage: preprepare
  image:
    name: $AUTH_IMAGE
    entrypoint: [""]
  before_script:
    - !reference [.gitlab-ci-print-info.print_info]
    - INT_GCLOUD_PROJECT_ID=$GCLOUD_PROJECT_ID
    - INT_GCLOUD_SA_JSON_KEY_BASE64=$ARTIFACT_REGISTRY_PUBLISHER_JSON_KEY
    - !reference [.gitlab-ci-authenticate.gcloud]
    - !reference [.gitlab-ci-print-timestamp-logging.logging]
  script:
    - gcloud auth print-access-token > ${CI_PROJECT_DIR}/.tmp/gcloud-access-token
    - chmod +x ${CI_PROJECT_DIR}/.src/main/docker/generateGcloudDockerAuthFile.sh
    - mkdir -p "${CI_PROJECT_DIR}/.tmp/src/main/docker/"
    - ${CI_PROJECT_DIR}/.src/main/docker/generateGcloudDockerAuthFile.sh ${CI_PROJECT_DIR}/.tmp/gcloud-access-token "${CI_PROJECT_DIR}/.tmp/src/main/docker/config.json"
  artifacts:
    paths:
      - ${CI_PROJECT_DIR}/.tmp/src/main/docker/config.json
    expire_in: 1 day
  only:
    - main
    - master
    - /^(development|feature\/.+)$/

preprepare-authenticate-npm-registry:
  stage: preprepare
  image:
    name: $AUTH_IMAGE
    entrypoint: [""]
  before_script:
    - !reference [.gitlab-ci-print-info.print_info]
    - INT_GCLOUD_PROJECT_ID=$GCLOUD_ARTIFACT_REGISTRY_NPM_PROJECT_ID
    - INT_GCLOUD_SA_JSON_KEY_BASE64=$GCLOUD_ARTIFACT_REGISTRY_NPM_JSON_KEY_BASE64
    - !reference [.gitlab-ci-authenticate.gcloud]
    - !reference [.gitlab-ci-print-timestamp-logging.logging]
  script:
    - gcloud auth print-access-token --project=$INT_GCLOUD_PROJECT_ID > .tmp/.gcloud-auth-access-token-npm
  artifacts:
    paths:
      - ${CI_PROJECT_DIR}/.tmp/.gcloud-auth-access-token-npm
    expire_in: 1 day

preprepare-authenticate-npm-local-registry-test:
  stage: preprepare
  image:
    name: $AUTH_IMAGE
    entrypoint: [""]
  before_script:
    - !reference [.gitlab-ci-print-info.print_info]
    - INT_GCLOUD_PROJECT_ID=$TEST__PROJECT_ID
    - INT_GCLOUD_SA_JSON_KEY_BASE64=$TEST__ARTIFACT_REGISTRY_READER_SA_JSON_KEY_BASE64
    - !reference [.gitlab-ci-authenticate.gcloud]
    - !reference [.gitlab-ci-print-timestamp-logging.logging]
  script:
    - gcloud auth print-access-token --project=$INT_GCLOUD_PROJECT_ID > .tmp/.gcloud-auth-access-token-npm-local
  artifacts:
    paths:
      - ${CI_PROJECT_DIR}/.tmp/.gcloud-auth-access-token-npm-local
    expire_in: 1 day



##########################################################
# 
# PREPARE
#
##########################################################
prepare-versions:
  stage: prepare
  image:
    name: $AUTH_IMAGE
    entrypoint: [""]
  script:
    - mkdir -p ${CI_PROJECT_DIR}/.tmp/
    - date_now=$(date -d "today" '+%Y-%m-%d-%H-%M-%S')
    - echo $date_now
    - CLEAN_CI_COMMIT_REF_SLUG=$(echo -n "${CI_COMMIT_REF_SLUG}" | sed 's/[^a-zA-Z0-9]/-/g' | head -c40 | sed 's/[^a-zA-Z0-9]$/x/g')
    - CLEAN_CI_PROJECT_NAME=$(echo -n "${CI_PROJECT_NAME}" | sed 's/[^a-zA-Z0-9]/-/g')
    - echo -n "${CLEAN_CI_COMMIT_REF_SLUG}-${date_now}-${CI_COMMIT_SHORT_SHA}"  | head -c63 | sed 's/[^a-zA-Z0-9]$/x/g' >> ${CI_PROJECT_DIR}/.tmp/release_version
    - echo -n "${CLEAN_CI_PROJECT_NAME}" >> ${CI_PROJECT_DIR}/.tmp/service_name
    - |
      SERVICE_NAME_PREFIX=""
      if grep -q "development" <<<"$CI_COMMIT_BRANCH"; then 
        SERVICE_NAME_PREFIX="development/";
      elif grep -q "feature/" <<<"$CI_COMMIT_BRANCH"; then 
        SERVICE_NAME_PREFIX="feature/";
      fi
    - echo -n "${SERVICE_NAME_PREFIX}${CLEAN_CI_PROJECT_NAME}" >> ${CI_PROJECT_DIR}/.tmp/service_name_long
    - echo -n "$(echo $(cat ${CI_PROJECT_DIR}/.tmp/service_name_long):$(cat ${CI_PROJECT_DIR}/.tmp/release_version))" >> ${CI_PROJECT_DIR}/.tmp/release_container_name
    - echo -n "$(echo $(cat ${CI_PROJECT_DIR}/.tmp/service_name_long)):latest" >> ${CI_PROJECT_DIR}/.tmp/latest_container_name
  artifacts:
    paths:
      - ${CI_PROJECT_DIR}/.tmp/release_version
      - ${CI_PROJECT_DIR}/.tmp/service_name
      - ${CI_PROJECT_DIR}/.tmp/service_name_long
      - ${CI_PROJECT_DIR}/.tmp/release_container_name
      - ${CI_PROJECT_DIR}/.tmp/latest_container_name
    expire_in: 1 day


cache_job:
  stage: prepare
  image: $BUILD_IMAGE
  before_script: 
    - !reference [.gitlab-ci-print-info.print_info]
    - INT_GCLOUD_PROJECT_ID=$TEST__PROJECT_ID
    - !reference [.gitlab-ci-print-timestamp-logging.logging]
  script:
    - export JS_BUILD_GCLOUD_PROJECT=$INT_GCLOUD_PROJECT_ID
    - export JS_BUILD_GCLOUD_REGISTRY_SERVER="${JS_BUILD_GCLOUD_REGISTRY_SERVER:-https://europe-west3-npm.pkg.dev/$JS_BUILD_GCLOUD_PROJECT/registry-npmjs-org/}"
    - export JS_BUILD_GCLOUD_REGISTRY_SERVER_TOKEN=$(cat .tmp/.gcloud-auth-access-token-npm-local)

    - export JS_BUILD_ALLO_GCLOUD_PROJECT=$GCLOUD_ARTIFACT_REGISTRY_NPM_PROJECT_ID
    - export JS_BUILD_GCLOUD_REGISTRY_ALLO_SERVER="${JS_BUILD_GCLOUD_REGISTRY_ALLO_SERVER:-https://europe-npm.pkg.dev/$JS_BUILD_ALLO_GCLOUD_PROJECT/allo-npm/}"
    - export JS_BUILD_GCLOUD_REGISTRY_ALLO_SERVER_TOKEN=$(cat .tmp/.gcloud-auth-access-token-npm)

    - . .src/main/build/build-dep.sh
    - !reference [.gitlab-ci-cache.yarn] # exits with 218 if something was renewed
  allow_failure:
    exit_codes: 218
  cache:
    key:
      files:
        *cache_keys
    paths:
      *cache_pathes
    policy: pull-push
    when: on_failure

##########################################################
# 
# BUILD
#
##########################################################
build:
  image: $BUILD_IMAGE
  stage: build
  before_script: 
    - !reference [.gitlab-ci-print-info.print_info]
    - INT_GCLOUD_PROJECT_ID=$TEST__PROJECT_ID
    - !reference [.gitlab-ci-print-timestamp-logging.logging]
  script:
    - export JS_BUILD_GCLOUD_PROJECT=$INT_GCLOUD_PROJECT_ID
    - export JS_BUILD_GCLOUD_REGISTRY_SERVER="${JS_BUILD_GCLOUD_REGISTRY_SERVER:-https://europe-west3-npm.pkg.dev/$JS_BUILD_GCLOUD_PROJECT/registry-npmjs-org/}"
    - export JS_BUILD_GCLOUD_REGISTRY_SERVER_TOKEN=$(cat .tmp/.gcloud-auth-access-token-npm-local)

    - export JS_BUILD_ALLO_GCLOUD_PROJECT=$GCLOUD_ARTIFACT_REGISTRY_NPM_PROJECT_ID
    - export JS_BUILD_GCLOUD_REGISTRY_ALLO_SERVER="${JS_BUILD_GCLOUD_REGISTRY_ALLO_SERVER:-https://europe-npm.pkg.dev/$JS_BUILD_ALLO_GCLOUD_PROJECT/allo-npm/}"
    - export JS_BUILD_GCLOUD_REGISTRY_ALLO_SERVER_TOKEN=$(cat .tmp/.gcloud-auth-access-token-npm)

    - . .src/main/build/build.sh run build
  artifacts:
    paths:
      - dist
      - .next/
      - next.config.*
  cache:
    key:
      files:
        *cache_keys
    paths:
      *cache_pathes
    policy: pull


##########################################################
# 
# E2E TEST
#
##########################################################
e2e-test:
  stage: e2e-test
  image: 
    name: $E2E_IMAGE
    entrypoint: ["/bin/sh", "-c"]
  dependencies:
    - deploy-kubernetes-development
  before_script: 
    - !reference [.gitlab-ci-print-info.print_info]
    - !reference [.gitlab-ci-print-timestamp-logging.logging]
  script:
    - |
      if [[ -f playwright.config.js ]];then
      # echo "Starting app..."
      # nohup yarn start & 
      # echo "Waiting server..."
      # npx wait-on http://localhost:3000
        echo "Start e2e tests..."
        npx playwright test
      else
        echo "no playwright.config.js found - skipping tests...";
        exit 219;
      fi
  after_script:
    - |
      if [[ -f playwright.config.js ]];then
       echo "====================================="
       echo "-"
       echo "- Report will be available at https://$CI_PROJECT_ROOT_NAMESPACE.$CI_PAGES_DOMAIN/-/$CI_PROJECT_NAME/-/jobs/$CI_JOB_ID/artifacts/playwright-report/index.html"
       echo "-"
       echo "====================================="
      fi
  allow_failure:
    exit_codes: 219
  artifacts:
    paths:
      - playwright-report
      - test-results
  cache:
    key:
      files:
        *cache_keys
    paths:
      *cache_pathes
    policy: pull
  only:
    - /^(development|feature\/.+)$/


##########################################################
# 
# PUBLISH
#
##########################################################
.publish-container:
  stage: publish
  image:
    name: $PUBLISH_CONTAINER_IMAGE
    entrypoint: [""]
  before_script: 
    - !reference [.gitlab-ci-print-info.print_info]
  script:
    - if [[ -f ${CI_PROJECT_DIR}/.tmp/src/main/docker/config.json && -s ${CI_PROJECT_DIR}/.tmp/src/main/docker/config.json ]];then echo "found ${CI_PROJECT_DIR}/.tmp/src/main/docker/config.json and its not empty"; else echo "ERROR!! - ${CI_PROJECT_DIR}/.tmp/src/main/docker/config.json not found or its empty"; exit 1; fi;
    - if [[ -z "${ACTIVE_PROFILE_VALUE}" ]]; then echo "ERROR!! - ACTIVE_PROFILE_VALUE is not set"; exit 1; fi;
    - mkdir -p /kaniko/.docker/
    - ls -lah "${CI_PROJECT_DIR}/.tmp/src/main/docker"
    - export GOOGLE_APPLICATION_CREDENTIALS="${CI_PROJECT_DIR}/.tmp/src/main/docker/config.json"
    - cp ${CI_PROJECT_DIR}/.tmp/src/main/docker/config.json /kaniko/.docker/config.json
    - set -euxo pipefail
    - /kaniko/executor
      --context "${CI_PROJECT_DIR}"
      --dockerfile "${CI_PROJECT_DIR}/.src/main/docker/$RUNTIME_DOCKER_FILE"
      --destination "europe-docker.pkg.dev/${GCR_PROJECT}/allo-docker/allo/$(cat ${CI_PROJECT_DIR}/.tmp/release_container_name)"
      --destination "europe-docker.pkg.dev/${GCR_PROJECT}/allo-docker/allo/$(cat ${CI_PROJECT_DIR}/.tmp/latest_container_name)"
      --reproducible
      --snapshot-mode redo
      --compressed-caching=false
      --build-arg "NODEJS_VERSION=${NODEJS_VERSION}"
      --build-arg "ALPINE_VERSION=${ALPINE_VERSION}"
      --build-arg "START_COMMAND=${START_COMMAND}"
      --build-arg "SERVICE_NAME=${CI_PROJECT_NAME}"
      --build-arg "ENVIRONMENT=${ACTIVE_PROFILE_VALUE}"
      #--cache=true 
      #--cache-run-layers
      #--cache-copy-layers=true 
      #--cache-ttl=672h 
      #--cache-repo=europe-docker.pkg.dev/${GCLOUD_ARTIFACT_REGISTRY_DOCKER_PROJECT_ID}/allo-docker/allo/${CI_PROJECT_NAME}/cache
  cache:
    key:
      files:
        *cache_keys
    paths:
      *cache_pathes
    policy: pull

publish-container-production:
  extends: .publish-container
  before_script: 
  - export ACTIVE_PROFILE_VALUE=production
  only:
    - main
    - master

publish-container-development:
  extends: .publish-container
  before_script: 
  - export ACTIVE_PROFILE_VALUE=staging
  only:
    - /^(development|feature\/.+)$/


##########################################################
# 
# DEPLOYMENT
#
##########################################################
.deploy-kubernetes:
  stage: deploy
  image: $DEPLOY_KUBERNETES_IMAGE
  before_script: 
    - !reference [.gitlab-ci-print-info.print_info]
    - !reference [.gitlab-ci-print-timestamp-logging.logging]
  script:

    - INT_SERVICE_NAME="$(cat ${CI_PROJECT_DIR}/.tmp/service_name)"
    - INT_SERVICE_VERSION="$(cat ${CI_PROJECT_DIR}/.tmp/release_version)"
    - INT_KUBERNETES_FILE_INPUT_NAME="${CI_PROJECT_DIR}/.src/main/k8s/kubernetes.yaml"
    - INT_KUBERNETES_FILE_OUTPUT_NAME=kubernetes-out.yaml
    - apt-get -y install gettext
    - !reference [.gitlab-ci-patch.kubernetes-yaml]
    - INT_NEW_RELIC_APP_DEPLOYMENT_NAME="$(cat ${CI_PROJECT_DIR}/.tmp/release_container_name)"
    - kubectl apply -f kubernetes-out.yaml
    - kubectl annotate deployment/$INT_SERVICE_NAME kubernetes.io/change-cause="$CI_COMMIT_AUTHOR - $CI_COMMIT_REF_SLUG - $CI_JOB_URL - $INT_SERVICE_VERSION"
    - sleep 5
    - |
      echo ------------------
      echo -
      echo - current history deployed of $INT_SERVICE_NAME
      echo - 
      kubectl rollout history deployment/$INT_SERVICE_NAME
      echo -
      echo -
      echo ------------------
    #- curl -X POST "https://api.eu.newrelic.com/v2/applications/${INT_NEW_RELIC_APP_ID}/deployments.json" -H "Api-Key:${NEW_RELIC_API_KEY}" -H "Content-Type:application/json" -d "{\"deployment\":{\"revision\":\"${INT_NEW_RELIC_APP_DEPLOYMENT_NAME}\"}}"
  only:
    - /^(development|feature\/.+)$/

deploy-kubernetes-production:
  extends: .deploy-kubernetes
  before_script:
    - INT_GCLOUD_PROJECT_ID=$PROD__PROJECT_ID
    - INT_GCLOUD_SA_JSON_KEY_BASE64=$PROD__K8S_SA_JSON_KEY_BASE64
    - INT_GCLOUD_REGION=$PROD__K8S_COMPUTE_REGION
    - INT_GCLOUD_CLUSTER_NAME=$PROD__K8S_CLUSTER_NAME
    - !reference [.gitlab-ci-authenticate.gcloud-gke]
    - INT_ENV_ENVIRONMENT="production"
    - INT_LEVIEE_ENV_ENVIRONMENT="production"
    - INT_SERVICE_IMAGE="europe-docker.pkg.dev/${PROD__PROJECT_ID}/allo-docker/allo/$(cat ${CI_PROJECT_DIR}/.tmp/release_container_name)"
    - INT_NEW_RELIC_APP_ID=${PROD__NEW_RELIC_APP_ID}
  only:
    - main
    - master

deploy-kubernetes-development:
  extends: .deploy-kubernetes
  before_script:
    - INT_GCLOUD_PROJECT_ID=$TEST__PROJECT_ID
    - INT_GCLOUD_SA_JSON_KEY_BASE64=$TEST__K8S_SA_JSON_KEY_BASE64
    - INT_GCLOUD_REGION=$TEST__K8S_COMPUTE_REGION
    - INT_GCLOUD_CLUSTER_NAME=$TEST__K8S_CLUSTER_NAME
    - !reference [.gitlab-ci-authenticate.gcloud-gke]
    - INT_ENV_ENVIRONMENT="production"
    - INT_LEVIEE_ENV_ENVIRONMENT="staging"
    - INT_SERVICE_IMAGE="europe-docker.pkg.dev/${TEST__PROJECT_ID}/allo-docker/allo/$(cat ${CI_PROJECT_DIR}/.tmp/release_container_name)"
    - INT_NEW_RELIC_APP_ID=${TEST__NEW_RELIC_APP_ID}
  only:
    - /^(development|feature\/.+)$/


##########################################################
# 
# ROLLBACK
#
##########################################################
.rollback-kubernetes:
  stage: rollback
  image: $DEPLOY_KUBERNETES_IMAGE
  before_script: 
    - !reference [.gitlab-ci-print-info.print_info]
    - INT_SERVICE_NAME="$(cat ${CI_PROJECT_DIR}/.tmp/service_name)"
    - !reference [.gitlab-ci-print-timestamp-logging.logging]
  script:
    - |
      echo ------------------
      echo -
      echo - current version deployed of $INT_SERVICE_NAME
      echo - 
      kubectl get deployment $INT_SERVICE_NAME -o yaml | grep "deployment.kubernetes.io/revision"
      echo -
      echo -
      echo ------------------
    - kubectl rollout undo deployment/$INT_SERVICE_NAME
    - |
      echo ------------------
      echo -
      echo - current version deployed of $INT_SERVICE_NAME
      echo - 
      kubectl get deployment $INT_SERVICE_NAME -o yaml | grep "deployment.kubernetes.io/revision"
      echo -
      echo -
      echo ------------------
    - sleep 5
    - |
      echo ------------------
      echo -
      echo - current history deployed of $INT_SERVICE_NAME
      echo - 
      kubectl rollout history deployment/$INT_SERVICE_NAME
      echo -
      echo -
      echo ------------------
    #- curl -X POST "https://api.eu.newrelic.com/v2/applications/${INT_NEW_RELIC_APP_ID}/deployments.json" -H "Api-Key:${NEW_RELIC_API_KEY}" -H "Content-Type:application/json" -d "{\"deployment\":{\"revision\":\"${INT_NEW_RELIC_APP_DEPLOYMENT_NAME}\"}}"
  when: manual

rollback-kubernetes-production:
  extends: .rollback-kubernetes
  before_script:
    - INT_GCLOUD_PROJECT_ID=$PROD__PROJECT_ID
    - INT_GCLOUD_SA_JSON_KEY_BASE64=$PROD__K8S_SA_JSON_KEY_BASE64
    - INT_GCLOUD_REGION=$PROD__K8S_COMPUTE_REGION
    - INT_GCLOUD_CLUSTER_NAME=$PROD__K8S_CLUSTER_NAME
    - !reference [.gitlab-ci-authenticate.gcloud-gke]
    - INT_NEW_RELIC_APP_ID=${PROD__NEW_RELIC_APP_ID}
  only:
    - main
    - master

rollback-kubernetes-development:
  extends: .rollback-kubernetes
  before_script:
    - INT_GCLOUD_PROJECT_ID=$TEST__PROJECT_ID
    - INT_GCLOUD_SA_JSON_KEY_BASE64=$TEST__K8S_SA_JSON_KEY_BASE64
    - INT_GCLOUD_REGION=$TEST__K8S_COMPUTE_REGION
    - INT_GCLOUD_CLUSTER_NAME=$TEST__K8S_CLUSTER_NAME
    - !reference [.gitlab-ci-authenticate.gcloud-gke]
    - INT_NEW_RELIC_APP_ID=${TEST__NEW_RELIC_APP_ID}
  only:
    - /^(development|feature\/.+)$/
