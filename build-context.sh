#!/bin/bash
#set -o errexit
set -euxo pipefail

DIR=`pwd`
export UI_BUILD_DIR="$DIR/.build"
mkdir -p "$UI_BUILD_DIR"

export UI_BUILD_GCLOUD_PROJECT=endless-gizmo-264508
export UI_BUILD_GCLOUD_REGISTRY_SERVER=https://europe-npm.pkg.dev/$UI_BUILD_GCLOUD_PROJECT/allo-npm/

export NPM_PACKAGES="$UI_BUILD_DIR/.npm-packages"
export YARN_GLOBAL_FOLDER="$UI_BUILD_DIR/.yarn/berry"
export COREPACK_HOME="$UI_BUILD_DIR/.corepack"
export COREPACK_ENABLE_DOWNLOAD_PROMPT=0


mkdir -p "$NPM_PACKAGES" 
npm config set prefix $NPM_PACKAGES
export PATH=$NPM_PACKAGES/bin:$PATH 
NODE_PATH="${NODE_PATH:-}"
NODE_PATH="$NPM_PACKAGES/lib/node_modules:$NODE_PATH"


gcloud -v >/dev/null 2>&1 || alias gcloud="$DIR/.src/main/yarn/dummy-gcloud-bin ${UI_BUILD_DIR}/.gcloud-auth-access-token"


if grep -q packageManager package.json; then
   echo "packageManager defined in package.json - will run corepack"
   YARN_VERSION=$(cat package.json | grep packageManager | xargs echo -n | cut -d ',' -f1 | cut -d ':' -f2 | xargs echo -n | cut -d '+' -f1 | xargs echo -n)
   COREPACK_PREFIX="corepack "
   
   mkdir -p "$COREPACK_HOME"

   npm install -g corepack --force
   
   corepack -v
   corepack enable --install-directory $COREPACK_HOME

   command -v yarn || true
   alias yarn || true
   realpath `which yarn` || true

   #npm install -g yarn  --force

   corepack yarn -v

   corepack yarn config set --json 'npmScopes.allo.npmAuthToken' "\"$(gcloud auth print-access-token --project=$UI_BUILD_GCLOUD_PROJECT)\""
   corepack yarn config set --json 'npmScopes.allo.npmAlwaysAuth' "true"
   corepack yarn config set --json 'npmScopes.allo.npmRegistryServer' "\"$UI_BUILD_GCLOUD_REGISTRY_SERVER\""
   corepack yarn config set nodeLinker node-modules

   alias yarn="corepack yarn"
   alias yarnpkg="corepack yarnpkg"
   alias pnpm="corepack pnpm"
   alias pnpx="corepack pnpx"
   alias npm="corepack npm"
   alias npx="corepack npx"

   command -v yarn || true
   alias yarn || true
   realpath `which yarn` || true

   corepack prepare $YARN_VERSION --activate
   corepack use $YARN_VERSION
   corepack yarn -v

fi

exec "$@"