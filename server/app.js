require('dotenv').config();

const express = require('express');
const next = require('next');
const bodyParser = require('body-parser');
const compression = require('compression');
const axios = require("axios");

const dev = process.env.NODE_ENV !== 'production';
const port = process.env.PORT || 3000;
const ROOT_URL = `http://localhost:${port}`;
const app = next({dev});
const handle = app.getRequestHandler();

const lEnv = process.env.NEXT_PUBLIC_LEVIEE_ENV || 'local';
const lEnvSettings = {
    local: {
        oauth: {
            clientId: 'restaurant-app',
            clientSecret: 'd7993ccc-a4e3-4c63-a9e7-132deafaf686',
            accessTokenUri: 'https://eat-dev.allo.restaurant/user-service/auth/token',
            refreshTokenUri: 'https://eat-dev.allo.restaurant/user-service/auth/refresh-token',
            healthUri: 'https://eat-dev.allo.restaurant/restaurant-api/business/restaurants/slug/sushi-banana'
        }
    },
    staging: {
        oauth: {
            clientId: 'restaurant-app',
            clientSecret: 'd7993ccc-a4e3-4c63-a9e7-132deafaf686',
            accessTokenUri: 'http://user-service:8080/user-service/auth/token',
            refreshTokenUri: 'http://user-service:8080/user-service/auth/refresh-token',
            healthUri: 'http://restaurant-api:8080/restaurant-api/business/restaurants/slug/sushi-banana'
        }
    },
    production: {
        oauth: {
            clientId: 'public-api',
            clientSecret: '6aca0029-f3cb-4d4a-94ff-4666b6ceab96',
            accessTokenUri: 'http://user-service:8080/user-service/auth/token',
            refreshTokenUri: 'http://user-service:8080/user-service/auth/refresh-token',
            healthUri: 'http://restaurant-api:8080/restaurant-api/business/restaurants/slug/leviee-sushi'
        }
    }
};

const oauthSettings = lEnvSettings[lEnv].oauth;
const tokenInfo = {
    accessToken : null,
    refreshToken : null,
    tokenExpirationTime : new Date()
}

const refreshAxios = axios.create({})
const accessTokenAxios = axios.create({})
const healthCheckAxios = axios.create({})

healthCheckAxios.interceptors.request.use(
    (config) => {
        if (tokenInfo.accessToken) {
            config.headers['Authorization'] = `Bearer ${tokenInfo.accessToken}`;
        }

        config.headers['Content-Type'] = 'application/json';
        config.headers['Accept'] = 'application/json';
        config.headers['content-language'] = 'en';

        return config;
    },
    (error) => Promise.reject(error)
);

function updateExpirationTime(expires_in) {
    tokenInfo.tokenExpirationTime.setTime(new Date().getTime() + (expires_in * 1000));
}

function updateTokenInfo(data) {
    const {access_token} = data;
    const {refresh_token} = data;
    const {expires_in} = data;
    tokenInfo.accessToken = access_token;
    tokenInfo.refreshToken = refresh_token;
    updateExpirationTime((parseInt(expires_in) - 5));
}

async function renewAccessToken() {
    if (tokenInfo.refreshToken) {
        await renewUsingRefreshToken();
        if (!needRefreshToken()) {
            return;
        }
    }

    console.log('Renewing internal access token');

    try {
        let response = await accessTokenAxios
            .post(
                oauthSettings.accessTokenUri,
                {
                    clientId: oauthSettings.clientId,
                    clientSecret: oauthSettings.clientSecret,
                    type: "CLIENT_CREDENTIALS"
                },
                {
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    }
                }
            )

        updateTokenInfo(response.data);
        console.log("access token response : " + JSON.stringify(response.data));
    } catch (err) {
        console.log("access token error : " + err);
        tokenInfo.accessToken = null;
        tokenInfo.refreshToken = null;
        tokenInfo.tokenExpirationTime = new Date();
    }
}

async function renewUsingRefreshToken() {
    console.log('Renewing using refresh token');

    try {
        let response = await refreshAxios
            .post(
                oauthSettings.refreshTokenUri,
                {
                    clientId: oauthSettings.clientId,
                    clientSecret: oauthSettings.clientSecret,
                    refreshToken: tokenInfo.refreshToken
                },
                {
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    }
                }
            )

        updateTokenInfo(response.data);
        console.log("refresh token response: " + JSON.stringify(response.data));
    } catch (err) {
        console.log("refresh token error: " + err);
        tokenInfo.accessToken = null;
        tokenInfo.refreshToken = null;
        tokenInfo.tokenExpirationTime = new Date();
    }
}

async function callHealthCheck() {
    return healthCheckAxios.get(oauthSettings.healthUri)
}

function needRefreshToken() {
    return (!tokenInfo.accessToken || new Date() >= tokenInfo.tokenExpirationTime)
}

app
    .prepare()
    .then(() => {
        console.log('NODE_ENV: ' + process.env.NODE_ENV);
        console.log('NEXT_PUBLIC_LEVIEE_ENV: ' + process.env.NEXT_PUBLIC_LEVIEE_ENV);
        console.log('lEnv: ' + lEnv);

        const server = express();

        server.use(compression());
        server.use(bodyParser.urlencoded({extended: true}));
        server.use(bodyParser.json());

        server.get('/health-check', async (req, res) => {
            if (needRefreshToken()) {
                await renewAccessToken();
            }

            let status;

            try {
                if(!tokenInfo.accessToken) {
                    console.log("Access token not available for health check");
                    status = 500;
                } else {
                    await callHealthCheck();
                    status = 200;
                }
            } catch(err) {
                console.log('Health check err: ' + err);

                status = 500;
                if(err.response && err.response.status === 401) {
                    await renewAccessToken();
                    if (tokenInfo.accessToken) {
                        status = 200;
                    }
                }
            }

            res.status(status).send('');
        });

        server.get('/validate-access', async (req, res) => {
            if (needRefreshToken()) {
              await renewAccessToken();
            }
  
            console.log('refreshing-token');
            if (tokenInfo.accessToken) {
              console.log('setting-token');
              res.cookie('lvClientToken', tokenInfo.accessToken, {maxAge: 90000 * 60 * 10000, httpOnly: false});
            }
            res.status(200).send('');
        });
        
        server.get('/manifest/:id', async (req, res) => {
          res.status(200).send({
            "name": "allO Screen",
            "short_name": "allO Screen",
            "start_url": `/screen/${req.params.id}`,
            "display": "standalone",
            "background_color": "#FF7C5C",
            "theme_color": "#FF7C5C",
            "scope": `/screen/${req.params.id}`,
            "icons": [
              {
                "src": "/rel/favicon-72.png",
                "type": "image/png",
                "sizes": "72x72"
              },
              {
                "src": "/rel/favicon-96.png",
                "type": "image/png",
                "sizes": "96x96"
              },
              {
                "src": "/rel/favicon-128.png",
                "type": "image/png",
                "sizes": "128x128"
              },
              {
                "src": "/rel/favicon-144.png",
                "type": "image/png",
                "sizes": "144x144"
              },
              {
                "src": "/rel/favicon-152.png",
                "type": "image/png",
                "sizes": "152x152"
              },
              {
                "src": "/rel/favicon-192.png",
                "type": "image/png",
                "sizes": "192x192"
              },
              {
                "src": "/rel/maskable-192.png",
                "type": "image/png",
                "sizes": "192x192",
                "purpose": "any maskable"
              },
              {
                "src": "/rel/favicon-384.png",
                "type": "image/png",
                "sizes": "384x384"
              },
              {
                "src": "/rel/favicon-5122.png",
                "type": "image/png",
                "sizes": "512x512"
              }
            ],
            "url_handlers" : [
              {
                "origin": "https://eat.allo.restaurant/screen/*"
              },
              {
                "origin": "https://eat-dev.allo.restaurant/screen/*"
              }
            ]
          }
        );
        });
        
        server.get('*', (req, res) => {
            if (tokenInfo.accessToken) {
                res.cookie('lvClientToken', tokenInfo.accessToken, {maxAge: 90000 * 60 * 10000, httpOnly: false});
            }
            return handle(req, res);
        });

        // starting express server
        server.listen(port, (err) => {
            if (err) throw err;
            console.log(`> Ready on ${ROOT_URL}`); // eslint-disable-line no-console
        });
    })
    .catch((err) => {
        // eslint-disable-line no-console
        console.error(err.stack);
    });
