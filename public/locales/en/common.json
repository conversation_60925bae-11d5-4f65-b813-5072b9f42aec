{"navigation-restaurants": "Restaurants", "navigation-historical-orders": "Historical orders", "landing-hero-title": "We bring restaurants and customers closer than ever before", "landing-hero-action-btn-label": "Get Started", "landing-instruction-sit-at-a-leviee-restaurant-label": "Sit down at a Leviee restaurant", "landing-instruction-scan-the-qr-code-label": "Scan the QR code and start ordering", "landing-instruction-no-payment-needed-label": "No payment needed, keep the tap open", "landing-instruction-split-the-bill-label": "Share, split or pay the entire bill", "landing-restaurant-list-featured-label": "Featured", "landing-restaurant-list-near-by-label": "Nearby", "landing-restaurant-list-all-label": "All Restaurants", "footer-imprint-label": "Impressum", "footer-data-privacy-label": "Privacy Policy", "footer-terms-of-service-label": "Terms and Conditions", "landing-scan-qr-code-btn-label": "Scan table QR code", "landing-return-to-order-btn-label": "Return to Order", "activity-title-scan-qr-code": "Scan table QR code", "activity-title-input-qr-code": "Input table code", "scanning-input-qr-code-btn-label": "Input table code", "scanning-enter-table-code-manually-title": "Enter your table code manually", "scanning-input-qr-code-field-label": "Table Code", "scanning-input-qr-code-field-placeholder": "Enter the table code", "common-continue": "Continue", "common-cancel": "Cancel", "common-okay": "Okay", "welcome-info-title": "We’re happy to have you at {{name}}", "welcome-info-description": "For a more personal service, we’d like to know your name?", "welcome-info-firstName-field-label": "How should we call you?", "welcome-info-firstName-field-placeholder": "Enter your name", "welcome-info-start-ordering-btn-label": "Start Ordering", "welcome-details-title": "Due to the current regulation of the German government under COVID-19, please submit your contact information to check in.", "welcome-details-form-mandatory": "* mandatory fields.", "welcome-details-firstName-field-label": "Firstname", "welcome-details-lastName-field-label": "Lastname", "welcome-details-street-field-label": "Street", "welcome-details-number-field-label": "No.", "welcome-details-zipCode-field-label": "Zip Code", "welcome-details-phone-field-label": "Mobile", "welcome-details-email-field-label": "Email", "welcome-details-start-ordering-btn-label": "Start Ordering", "activity-title-welcome": "Welcome", "activity-title-corona-personal-information": "Personal Information", "order-table-status-verification-description": "Table verification needed", "order-table-status-verify-table-btn-label": "Verify Table", "order-table-status-requests-label": "{{requests}} Requests", "restaurant-more-info-btn-label": "More info", "restaurant-order-btn-label": "Start Ordering", "restaurant-schedule-order-btn-label": "Schedule an order", "restaurant-open-order-btn-label": "My Receipt", "activity-title-restaurant-details": "Details", "activity-title-table-verification": "Table verification", "activity-title-table": "Table", "order-table-verification-drawer-title": "Ask the waiter", "order-table-verification-drawer-description": "The waiter can verify the table in person by scanning the QR accessible below.", "order-table-verification-drawer-friend-verification-title": "Ask a friend", "order-table-verification-drawer-friend-verification-description": "Your friend {{name}} can approve you to join this table.", "order-table-verification-drawer-notify-waiter-btn": "Notify the waiter", "order-table-verification-drawer-waiter-notified-label": "Waiter has been notified", "activity-title-current-order": "Current Order", "order-current-order-drawer-unconfirmed-category-label": "Still to be ordered", "order-current-order-drawer-confirmed-category-label": "Already ordered", "order-current-order-drawer-users-total-label": "{{name}}'s total", "order-current-order-drawer-order-total-amount-label": "Total order amount", "order-current-order-drawer-disclaimer-label": "All the amounts presented above include VAT at the current German rate of 7%.", "order-current-order-table-verification-modal-title": "Quick table verification required", "order-current-order-table-verification-modal-description": "For safety purposes we need to verify your table. Quickly done, hassle-free.", "order-current-order-table-verification-modal-verify-btn-label": "Quickly verify table", "order-current-order-remove-item-btn-label": "Remove", "order-current-order-ongoing-order-item-btn-label": "Order", "order-current-order-ongoing-order-item-round-label": "Round {{round}}", "order-current-view-ongoing-menu-item-btn-label": "View", "order-item-details-notes-section-title": "Notes to the kitchen", "order-item-details-notes-section-descriptions": "Allergic restrictions or any requests", "order-item-details-notes-section-notes-filed-placeholder": "Add your note", "order-item-details-remarks-section-title": "Remarks", "order-item-details-remarks-section-descriptions": "Allergens & Additives", "order-item-details-quantity-label": "Quantity", "order-item-details-add-to-order-btn-label": "Add to Order", "order-see-current-order-btn-label": "Order", "order-see-current-order-items-quantity-btn-label": "{{count}} items", "you-have-pending-items": "You have items waiting.", "order-current-order-order-items-modal-description": "Please click below for kitchen to start preparing.", "order-current-order-order-items-pickup-modal-description": "Proceed to the next step to complete your order.", "order-current-order-order-items-pickup-threshold-modal-description": "You have not reached minimum order amount of {{amount}}€ for delivery", "order-current-order-order-items-delivery-areas-modal-description": "Delivery is not available for your area. You can select Pickup to proceed.", "order-current-order-order-items-modal-order-btn-label": "Order items", "order-current-order-order-items-modal-order-quantity-btn-label": "{{count}} items", "order-current-order-payment-modal-description": "Done with ordering? To payment you shall progress. Still ordering? Go back and keep them coming.", "order-current-order-payment-modal-proceed-to-payment-btn-label": "Proceed to Payment", "activity-title-pickup-complete": "Order Details.", "pickup-complete-user-information-title": "Please add your details to complete the order.", "pickup-complete-user-information-complete-order-btn-label": "Complete Order", "pickup-complete-about-your-order-title": "About your order", "pickup-complete-day-field-label": "Day", "pickup-complete-time-field-label": "Time", "pickup-complete-time-field-now-option-label": "Standard", "pickup-complete-time-field-none-option-label": "No slot available", "pickup-complete-time-field-schedule-option-label": "Pre-order", "pickup-complete-request-to-restaurant-field-label": "Add a note", "pickup-success-pending-title": "{{name}}, thank you for ordering at {{restaurant}}.", "pickup-success-pending-description": "Your order has been sent to the restaurant. You will get a confirmation email shortly.", "pickup-success-order-summary-title": "Order Summary", "pickup-success-order-summary-total-label": "Total", "pickup-order-type-field-label": "Pickup or Delivery", "pickup-order-type-field-pickup-option-label": "Pickup at the shop", "pickup-order-type-field-delivery-option-label": "Deliver to my address", "order-summary-vat-disclaimer-label": "All the amounts presented above include VAT (currently 7% for food and 19% for drinks for takeaway orders, and 19% for both food and drinks for dine-in orders in Germany).", "order-summary-data-policy-disclaimer-label": "By completing payment, you agree to our data protection policy and terms and conditions.", "activity-title-pickup-disabled": "No pickup", "pickup-disabled-title": "Pickup is not available", "pickup-disabled-message": "{{name}} does not accept pickup or delivery orders yet. We are working hard to get them on board as soon as possible.", "restaurant-closed-btn-label": "The restaurant is currently closed", "common-monday": "Monday", "common-tuesday": "Tuesday", "common-wednesday": "Wednesday", "common-thursday": "Thursday", "common-friday": "Friday", "common-saturday": "Saturday", "common-sunday": "Sunday", "table-management-leave-table-btn-label": "Leave Table", "table-management-leave-table-confirmation-label": "Are you sure you want to leave?", "table-management-leave-table-non-confirmation-label": "You can not leave the table yet.", "table-management-leave-table-non-confirmation-description": "Please finalize the payment for this order.", "table-management-kick-out-table-confirmation-label": "Are you sure you want to kick {{name}} out of this order?", "table-management-leave-btn-label": "Leave", "table-management-kick-out-btn-label": "Kick-out", "common-approve": "Approve", "common-approved": "Approved", "common-pending": "Pending", "common-approved-by": "Approved by {{name}}", "activity-title-payment": "Payment", "order-payment-drawer-split-bill-title": "Split the bill", "order-payment-drawer-split-bill-description": "Choose your preferred option", "common-payment-option-own": "My share", "common-payment-option-split": "Split bill", "common-payment-option-all": "Pay all", "order-payment-drawer-order-summary-title": "Order Summary", "order-payment-drawer-order-summary-description": "Double-check before paying", "order-payment-drawer-tip-title": "Tip the waiter", "order-payment-drawer-tip-description": "Thank you for your support", "order-payment-drawer-custom-tip-btn-label": "Add custom tip", "order-payment-drawer-no-tip-btn-label": "No Tip", "order-payment-drawer-current-tip-label": "Tipping {{amount}}€", "common-update": "Update", "common-replace": "Replace", "order-payment-drawer-payment-method-title": "Payment method", "order-payment-drawer-payment-method-description": "Chose your prefered payment method", "common-payment-method-app": "Online", "common-payment-method-cash": "Cash", "common-payment-method-card": "Card", "order-payment-drawer-total-breakdown-tip-label": "Tip", "order-payment-drawer-total-breakdown-subtotal-label": "Subtotal", "order-payment-drawer-complete-payment-btn": "Complete Payment", "order-success-title": "{{name}}, thank you for ordering at {{restaurant}}.", "order-success-description": "We hope to have you with us again. Meanwhile, take a look at other restaurants available at Leviee.", "order-success-return-to-landing-btn-label": "Return to Homepage", "order-payment-btn-label": "Complete Payment", "order-payment-btn-label-disabled": "Processing payment...", "scanner-view-processing-code": "Processing code...", "welcome-view-preparing-order": "Preparing your order...", "payment-add-payment-method-btn-label": "Add Payment method", "notification-order-items-processing": "Processing your order...", "notification-order-items-confirmation": "Your items have been ordered.", "notification-order-items-error": "There was an issue ordering items.", "notification-order-items-ongoing-round-already-in-progress": "Round ordering is already in the cart. A new Round can not be started.", "notification-participant-approved-confirmation": "You have been approved.", "notification-invalid-order-code": "We could not process your code!", "notification-scan-not-configured": "The restaurant is not taking orders at the moment.", "menu-item-price-from": "From", "menu-item-price-included": "Included", "order-item-base-price-label": "Base Price", "common-loader-redirecting-to-payment-message": "Loading payment. Just a moment...", "common-loader-processing-payment-message": "We are processing your payment. Please do not close this window.", "takeaway-type-drawer-title": "Takeaway Order", "delivery-fee-label": "Delivery Fee", "pickup-day-field-option-0-label": "Today", "pickup-day-field-option-1-label": "Tomorrow", "pickup-day-field-option-2-label": "Day after tomorrow", "common-sold-out": "Sold out", "select-date": "Select date", "select-time": "Select time", "reserve-table": "Reserve table", "number-of-guests": "Number of guests", "reservation-partner": "Reservation partner", "select-tables": "Select tables", "add-participant": "Add participant", "no-time-slots-available": "No time slots available", "suggestions": "Suggestions", "available": "Available", "minutes-overlap": "{{minutes}} min overlap", "select-table": "Select table", "seats": "seats", "your-name": "Your name", "enter-full-name": "Enter full name", "your-phone-number": "Your phone number", "your-email": "Your email", "email-address": "Email address", "add-notes": "Add notes", "write-your-note": "Ex: Need a toddler chair", "your-upcoming-reservation": "Your upcoming reservation", "reservation-cancelled": "Reservation cancelled", "thank-you-for-reserving-see-details-below": "Thank you for your reservation at {{restaurant}}. Below you can find more details.", "cancelled-reservation-see-details-below": "Your reservation at {{restaurant}} was cancelled. Below you can find your more details.", "your-reservation-at": "Your reservation at {{restaurant}}", "your-reservation": "Your reservation", "location": "Location", "date-and-time": "Date and time", "reserved-under": "Reserved under", "cancel-reservation": "Cancel reservation", "create-another-reservation": "Create another reservation", "your-order": "Your order", "you-have-an-order-in-progress-at-restaurant": "You have an order in progress at {{name}}", "restaurant-is-open": "Restaurant is open.", "closed": "Closed", "more-info": "More info", "no-delivery-fee": "No delivery fee.", "amount-delivery-fee": "{{amount}}€ delivery fee.", "restaurant-does-not-deliver": "{{restaurant}} does not deliver.", "opening-hours": "Opening hours", "close": "Close", "enter-your-table-to-order": "Join your table", "camera-not-found-enter-table-code-manually": "Welcome to allO. Please enter the table code below.", "in-progress": "In progress", "your-buffet": "Your Buffet", "please-select-number-of-people": "Please select number of people", "please-select-date": "Please select date", "please-select-time": "Please select time", "please-fill-your-info": "Please fill in your data", "oh-something-missing": "Oh oh, something is missing!", "view-your-basket": "View your basket", "send-items-to-kitchen": "Send to Kitchen", "you-have-items-waiting-in-your-basket": "You have items waiting in your basket", "enjoy-your-food": "Enjoy your food", "view-receipt": "View receipt", "send-to-kitchen": "Send to Kitchen", "buffet-ordering": "Buffet Ordering", "checkout": "Checkout", "confirm-checkout-preference-for-your-purchase": "Confirm your checkout preference for the order", "missing-information": "Missing data", "by-proceeding-you-agree": "By proceeding you agree to our", "and": "and", "privacy-policy": "Privacy Policy", "terms-and-conditions-to-agree-to": "Terms and Conditions", "empty-basket": "Basket is empty", "add-items-to-proceed": "Items your select will appear here.", "back-to-order": "Back to order", "wait-while-we-verify-payment-and-prepare-order": "Thank you for ordering at {{name}}. We are verifying your payment and will start preparing your order shortly.", "thank-you-for-ordering-here-is-your-order-number": "Thank you for ordering at {{name}}. We are preparing your order. Please use your order number to collect your items.", "your-receipt": "Your receipt", "fill-in-your-data-below-to-receive-a-digital-receipt": "Fill in your data below and submit to receive a digital receipt, by email.", "get-receipt": "Get receipt", "receipt-purpose": "Receipt purpose", "receipt-participants": "Receipt participants", "vatId": "VAT id", "receipt-generated": "Receipt generated", "please-check-your-email-for-the-pdf-receipt": "Please check your email {{email}} for the PDF version of the receipt.", "generating-receipt": "Generating receipt", "receipt-sent-via-email": "Receipt sent via email", "could-not-generate-receipt": "Receipt could not be generated", "your-order-was-processed-you-will-get-digital-receipt": "Your order was processed successfully. You will receive a digital receipt shortly.", "please-enter-a-valid-phone-number": " Please enter a valid phone number", "please-enter-a-valid-email-address": "Please enter a valid email address", "thank-you-for-ordering-at-restaurant": "Thank you for ordering at {{name}}.", "below-you-can-find-your-receipt-details": "Below you can find your receipt details.", "order-summary": "Order summary", "proceed-to-payment": "Proceed to payment", "subtotal": "Subtotal", "discounts": "Discounts", "total": "Total", "we-hope-everything-was-to-your-satisfaction": "We hope everything was to your satisfaction.", "please-select-the-payment-amount": "Please select the payment amount:", "you-have-requested-to-pay-full-amount-of-this-order": "You have requested to pay all of this order.", "you-have-requested-to-pay-a-partial-amount-of-this-order": "You have selected to pay a partial amount of this order.", "waiting-for-payment": "Waiting for payment", "please-follow-the-instructions-on-the-terminal": "Please follow the instructions on the terminal", "scan-qr-code-to-pay": "Scan QR code to pay", "scan-the-qr-code-above-to-pay-for-your-order": "Scan the QR code above to complete payment for your order.", "processing-payment": "Processing payment", "we-are-processing-your-payment": "We are processing your payment.", "thank-you-for-your-order": "Thank you for your order!", "delivery-fees-may-apply": "Delivery fee may apply.", "please-fill-in-your-address": "Please fill in your address", "gift-card": "Gift card", "order-your-digital-gift-card-at-restaurant": "Order your digital gift card at {{name}}.", "select-your-card-amount-to-proceed": "Select your gift card amount to proceed.", "gift-card-amount": "Gift card amount", "to-send-the-gift-card-we-need-your-email": "To send you the gift card, we need your email address", "digital-gift-card-email": "Digital gift card email", "enter-email": "Enter email", "enter-amount": "Enter amount", "other": "Other", "your-gift-card": "Your gift card", "please-check-your-email-for-the-details-and-purchase-confirmation": "Please check your email {{email}} for details and purchase confirmation", "percent-off-on-pickup": "{{percent}}% discount on Pickup", "pickup-discount-description": "Order Pickup now to use this deal", "percent-discount": "{{percent}}% discount", "discount": "Discount", "language": "Language", "english": "English", "german": "De<PERSON>ch", "chinese": "中文", "enter-code": "Enter code", "approval-code-not-correct": "Your approval code is invalid", "items-not-ordered": "Items could not be ordered", "your-items-are-still-in-the-basket": "Oops, your items could not be ordered and are still in the basket, please check with a waiter.", "invalid-email": "Invalid email address", "terms-of-service": "Terms of service", "reservation-terms-of-service-disclaimer": "To confirm your reservation please accept the terms and conditions set by {{restaurant}}, which uses allO to assists the communication between you and the restaurant within the GDPR data protection framework. You always establish contractual relationship with the restaurant and for any questions, please contact the restaurant directly.", "please-accept-terms": "Please accept terms", "selected-date-is-blocked": "Sorry, no reservations are available on this date. Please choose another.", "webshop-opening-hours": "Webshop Opening Hours", "pick-up-options": "Pick up Options", "delivery-options": "Delivery Options", "choose-scheduling-time": "Schedule time", "scheduled-for": "scheduled for {{ date }} at {{ time }}", "done": "Done", "do-you-want-to-redeem-a-gift-card": "Do you want to redeem a Gift Card?", "enter-your-gift-card-code-and-select-the-desired-amount-in-the-next-step": "Enter you gift card and select the desired amount in the next step.", "common-apply": "Apply", "gift-card-balance": "Gift Card Balance", "amount-to-redeem": "Amount to Redeem", "no-gift-card-found-with-this-code": "Sorry, we couldn't find a gift card with this code.", "gift-card-code": "Gift Card Code", "the-entered-amount-exceeds-gift-card-balance": "The entered gift card amount exceeds the total value of your order or the gift card balance.", "add-another-gift-card": "Add another Gift Card", "gift-card-with-this-code-already-added": "You have already added a gift card with this code.", "generic-error-notification": "An unexpected error occurred. Please try again later.", "hide-this-email-from-receipt-toggle-header": "<PERSON><PERSON>", "hide-this-email-from-receipt-description": "Enable this option if you do NOT want to show the email entered above on the final pdf receipt.", "promo-code-field-label": "Promo code", "express-checkout-promocode-error-message": "Promo code could not be removed"}