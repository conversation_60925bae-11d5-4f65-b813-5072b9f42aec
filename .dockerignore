.dockerignore
.git
.gitignore
README.md
LICENSE


**/.vscode

# Logs
logs
*.log

# Runtime data
pids
*.pid
*.seed

# Directory for instrumented libs generated by jscoverage/JSCover
lib-cov

# Coverage directory used by tools like istanbul
coverage

# Grunt intermediate storage (http://gruntjs.com/creating-plugins#storing-task-files)
.grunt

# node-waf configuration
.lock-wscript

# Compiled binary addons (http://nodejs.org/api/addons.html)
build/Release


.env
.editorconfig
.aws
dist

.gitlab-ci.yml
.src/main/docker
.src/main/gitlabci
.src/main/k8s

