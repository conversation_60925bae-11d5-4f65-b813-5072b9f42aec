import path from 'path';

const NextI18Next = require('next-i18next').default;

const languages = ['de', 'en', 'zh'];

const options = {
  browserLanguageDetection: true,
  defaultLanguage: 'de',
  otherLanguages: ['en', 'zh'],
  fallbackLng: 'de',
  shallowRender: true,
  localePath: path.resolve('./public/locales')
};

const NextI18NextInstance = new NextI18Next(options);

NextI18NextInstance.i18n.languages = languages;

module.exports = NextI18NextInstance;
