if you want to test it: 
https://eat-dev.allo.restaurant/restaurant/sushi-banana




This is a [Next.js](https://nextjs.org/) project bootstrapped with [`create-next-app`](https://github.com/vercel/next.js/tree/canary/packages/create-next-app).

## Getting Started 

To run this project locally please ensure that you’re using node v18.17.1. 
i.e. by running:

```bash
nvm use 18
```

Should you encounter this error: 

```bash
Your user’s .npmrc file (${HOME}/.npmrc)
has a globalconfig and/or a prefix setting, which are incompatible with nvm.
Run nvm use --delete-prefix v18.17.1 to unset it.
```
 run: 

```bash
nvm use --delete-prefix v18.17.1 
```

When you switch Node.js versions using nvm like that, globally installed npm packages (like Yarn) may no longer be available because nvm isolates each Node.js version with its own set of global packages. 
Therefore, you might be required to reinstall yarn by i.e. running: 

```bash
npm install -g yarn
```

You should then have yarn v1.22.22 installed and node v18.17.1

Then to start the development server, run:

```bash
yarn start
```

Then Open [http://localhost:3000](http://localhost:3000) in your browser to view the result.

You can start editing the page by modifying `pages/index.js`. The page auto-updates as you edit the file.

## to generate .yarnrc file 

run: 

```bash
.build-context.sh
```


## Learn More

To learn more about Next.js, take a look at the following resources:

- [Next.js Documentation](https://nextjs.org/docs) - learn about Next.js features and API.
- [Learn Next.js](https://nextjs.org/learn) - an interactive Next.js tutorial.

You can check out [the Next.js GitHub repository](https://github.com/vercel/next.js/) - your feedback and contributions are welcome!

## Deploy on Vercel

The easiest way to deploy your Next.js app is to use the [Vercel Platform](https://vercel.com/import?utm_medium=default-template&filter=next.js&utm_source=create-next-app&utm_campaign=create-next-app-readme) from the creators of Next.js.

Check out our [Next.js deployment documentation](https://nextjs.org/docs/deployment) for more details.
